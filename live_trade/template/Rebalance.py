import ccxt
import pandas as pd
import numpy as np
import datetime
import time
import requests
import yaml  # install package PyYAML
import glob
import os
import matplotlib.pyplot as plt
import schedule
import re
import sys
from datetime import datetime, timedelta
import shutil

# Get the directory where this script is located
script_dir = os.path.dirname(os.path.abspath(__file__))

# User configurable settings
max_wait_time = 600  # Maximum wait time in seconds (10 minutes) for strategy updates, fallback to equal method if not all strategies are updated within this time

# Load key file from key.yaml using absolute path
key_file = os.path.join(script_dir, 'config/key.yaml')

with open(key_file) as f:
    keys = yaml.safe_load(f)
print('Loading key file', key_file, ".....")

# Check if keys has a flat structure or is organized by exchange
is_flat_structure = 'EXCHANGE' in keys and not any(isinstance(keys[k], dict) and 'apiKey' in keys[k] for k in keys)

# Get selected exchange from keys
if is_flat_structure:
    selected_exchange = keys.get('EXCHANGE', '').upper()
else:
    # If using nested structure, use the first exchange key
    for key in keys:
        if key.upper() in ['BINANCE', 'BYBIT', 'OKX']:
            selected_exchange = key.upper()
            break
    else:
        selected_exchange = None

print(f"Selected Exchange: {selected_exchange}")

# Handle different exchange logic
if selected_exchange == "BYBIT":
    if is_flat_structure:
        EXCHANGE = ccxt.bybit({
            'apiKey': keys.get('APIKEY'),
            'secret': keys.get('SECRET'),
            'rateLimit': True,
            'options': {
                'adjustTime': True,
            },
        })
        if keys.get('LIVE_ACCOUNT', 'N') == "N":
            EXCHANGE.enable_demo_trading(True)
    else:
        EXCHANGE = ccxt.bybit({
            'apiKey': keys['BYBIT']['apiKey'],
            'secret': keys['BYBIT']['secret'],
            'rateLimit': True,
            'options': {
                'adjustTime': True,
            },
        })
        if keys['BYBIT'].get('live', 'N') == "N":
            EXCHANGE.enable_demo_trading(True)
    try:
        time_diff = EXCHANGE.load_time_difference()  # Adjust the local timestamp offset with server time
    except Exception as e:
        print("Failed to load time difference for Bybit:", e)
elif selected_exchange == "BINANCE":
    if is_flat_structure:
        EXCHANGE = ccxt.binance({
            'apiKey': keys.get('APIKEY'),
            'secret': keys.get('SECRET'),
            'enableRateLimit': True,
            'options': {'defaultType': 'future'}})
        if keys.get('LIVE_ACCOUNT', 'N') == "N":
            EXCHANGE.set_sandbox_mode(True)
    else:
        EXCHANGE = ccxt.binance({
            'apiKey': keys['BINANCE']['apiKey'],
            'secret': keys['BINANCE']['secret'],
            'enableRateLimit': True,
            'options': {'defaultType': 'future'}})
        if keys['BINANCE'].get('live', 'N') == "N":
            EXCHANGE.set_sandbox_mode(True)
elif selected_exchange == "OKX":
    if is_flat_structure:
        EXCHANGE = ccxt.okx({
            'apiKey': keys.get('APIKEY'),
            'secret': keys.get('SECRET'),
            'password': keys.get('PASSPHRASE', ''),  # OKX uses 'password' parameter for the passphrase
            'enableRateLimit': True,
            'options': {
                'defaultType': 'swap',  # Use swap for perpetual futures
                'adjustForTimeDifference': True
            }
        })
        if keys.get('LIVE_ACCOUNT', 'N') == "N":
            EXCHANGE.set_sandbox_mode(True)
    else:
        EXCHANGE = ccxt.okx({
            'apiKey': keys['OKX']['apiKey'],
            'secret': keys['OKX']['secret'],
            'password': keys['OKX'].get('password', ''),
            'enableRateLimit': True,
            'options': {
                'defaultType': 'swap',  # Use swap for perpetual futures
                'adjustForTimeDifference': True
            }
        })
        if keys['OKX'].get('live', 'N') == "N":
            EXCHANGE.set_sandbox_mode(True)
else:
    raise ValueError(f"Unsupported exchange: {selected_exchange}")


def display_collaterals(collateral_value, margin_details, account_balance):
    """Display available collaterals in a clear format"""
    print("\n=== Available Collaterals ===")
    print(f"Total Account Balance (USD): {account_balance:,.2f}\n")
    
    if collateral_value and margin_details:
        print("Collateral Assets:")
        for currency, details in margin_details.items():
            usd_value = collateral_value.get(currency, 0)
            percent = (usd_value / account_balance * 100) if account_balance > 0 else 0
            print(f"{currency}: {details['total']:,.4f} ({usd_value:,.2f} USD, {percent:.2f}%)")
    
    print("===========================\n")

def get_acct_bal():
    account_balance = 0
    margin_details = {}
    collateral_value = {}

    try:
        if selected_exchange == "BYBIT":
            balance = EXCHANGE.fetch_balance()
            # Get all collaterals available in the unified account
            for currency in balance:
                if currency != 'info' and currency != 'timestamp' and currency != 'datetime' and currency != 'free' and currency != 'used' and currency != 'total':
                    if isinstance(balance[currency], dict) and 'total' in balance[currency] and balance[currency][
                        'total'] > 0:
                        margin_details[currency] = {
                            'total': round(balance[currency]['total'] or 0, 4),
                            'free': round(balance[currency]['free'] or 0, 4),
                            'used': round(balance[currency]['used'] or 0, 4)
                        }

                        # Calculate USD value of each collateral
                        try:
                            if currency == 'USDT' or currency == 'USDC':
                                # Stablecoins at 1:1 ratio
                                collateral_value[currency] = balance[currency]['total']
                            else:
                                # For other crypto, get current market price
                                ticker_symbol = f"{currency}/USDT"
                                ticker = EXCHANGE.fetch_ticker(ticker_symbol)
                                price = ticker['last']
                                collateral_value[currency] = balance[currency]['total'] * price
                        except Exception as e:
                            print(f"Error getting price for {currency}: {e}. Using fallback method.")
                            # Fallback: use the exchange's valuation if available
                            collateral_value[currency] = balance[currency]['total']

            # Calculate total account balance from all collaterals
            account_balance = round(sum(collateral_value.values()), 2)

            # Ensure we have at least USDT value as fallback
            if account_balance == 0 and 'USDT' in balance and isinstance(balance['USDT'], dict) and 'total' in balance[
                'USDT']:
                account_balance = round(balance['USDT']['total'], 2)
                print("Warning: Using only USDT balance as fallback.")

        elif selected_exchange == "BINANCE":
            balance = EXCHANGE.fetch_balance()
            total_usd_value = 0

            # For Binance, the totalMarginBalance already includes all collaterals in USD value
            if 'info' in balance:
                if 'totalMarginBalance' in balance['info']:
                    account_balance = round(float(balance['info']['totalMarginBalance']), 2)
                elif 'totalWalletBalance' in balance['info']:
                    account_balance = round(float(balance['info']['totalWalletBalance']), 2)
                else:
                    print("Warning: Total margin balance not found. Calculating manually.")
                    account_balance = 0

            # Get all assets from the margins section and calculate their USD value
            if 'info' in balance and 'assets' in balance['info']:
                for asset in balance['info']['assets']:
                    if 'asset' in asset and 'walletBalance' in asset and 'availableBalance' in asset:
                        currency = asset['asset']
                        wallet_balance = float(asset['walletBalance'])

                        if wallet_balance > 0:
                            margin_details[currency] = {
                                'total': round(wallet_balance or 0, 4),
                                'free': round(float(asset['availableBalance']) or 0, 4),
                                'used': round(wallet_balance - float(asset['availableBalance']) or 0, 4)
                            }

                            # If we need to manually calculate total balance
                            if account_balance == 0:
                                try:
                                    if currency == 'USDT' or currency == 'USDC' or currency == 'BUSD':
                                        # Stablecoins at 1:1 ratio
                                        usd_value = wallet_balance
                                    elif 'usdValue' in asset:
                                        # Use exchange-provided USD value if available
                                        usd_value = float(asset['usdValue'])
                                    else:
                                        # For other crypto, get current market price
                                        ticker_symbol = f"{currency}/USDT"
                                        ticker = EXCHANGE.fetch_ticker(ticker_symbol)
                                        price = ticker['last']
                                        usd_value = wallet_balance * price

                                    collateral_value[currency] = usd_value
                                    total_usd_value += usd_value
                                except Exception as e:
                                    print(f"Error getting price for {currency}: {e}")

                # If we manually calculated, update the account balance
                if account_balance == 0:
                    account_balance = round(total_usd_value, 2)
                    
        elif selected_exchange == "OKX":
            balance = EXCHANGE.fetch_balance()
            total_usd_value = 0
            
            # For OKX, parse the balance information
            if 'info' in balance and 'data' in balance['info'] and len(balance['info']['data']) > 0:
                balances = balance['info']['data'][0]
                if 'totalEq' in balances:
                    # Use the total equity value provided by OKX
                    account_balance = round(float(balances['totalEq']), 2)
                else:
                    print("Warning: Total equity not found. Calculating manually.")
                    account_balance = 0
                    
            # Process individual currency balances
            for currency in balance:
                if currency != 'info' and currency != 'timestamp' and currency != 'datetime' and currency != 'free' and currency != 'used' and currency != 'total':
                    if isinstance(balance[currency], dict) and 'total' in balance[currency] and balance[currency]['total'] > 0:
                        margin_details[currency] = {
                            'total': round(balance[currency]['total'] or 0, 4),
                            'free': round(balance[currency]['free'] or 0, 4),
                            'used': round(balance[currency]['used'] or 0, 4)
                        }
                        
                        # Calculate USD value of each currency
                        try:
                            if currency == 'USDT' or currency == 'USDC':
                                # Stablecoins at 1:1 ratio
                                collateral_value[currency] = balance[currency]['total']
                            else:
                                # For other crypto, get current market price
                                ticker_symbol = f"{currency}/USDT"
                                ticker = EXCHANGE.fetch_ticker(ticker_symbol)
                                price = ticker['last']
                                collateral_value[currency] = balance[currency]['total'] * price
                                
                            # Add to total USD value if we're calculating manually
                            if account_balance == 0:
                                total_usd_value += collateral_value[currency]
                        except Exception as e:
                            print(f"Error getting price for {currency}: {e}. Using fallback method.")
            
            # If we manually calculated, update the account balance
            if account_balance == 0:
                account_balance = round(total_usd_value, 2)
                
        # Display available collaterals
        display_collaterals(collateral_value, margin_details, account_balance)
        
        print("⚠️ IMPORTANT: Enable 'Used As Collateral' option for any assets you want included in your margin calculations.\n")
        print("⚠️ RISK WARNING: Recommended leverage is between 1-2. Higher leverage significantly increases liquidation risk during market volatility.\n")
        
        return account_balance, collateral_value, margin_details
    except Exception as e:
        print(f"Error retrieving account balance: {e}")
        return 0, {}, {}


def calculate_max_pos(account_balance, leverage=1):
    # Dynamically retrieve symbols from all YAML configuration files
    config_dir = os.path.join(script_dir, 'config')
    config_files = glob.glob(os.path.join(config_dir, 'config_*.yaml'))
    symbols = []

    # Map to store the exchange format of symbols
    exchange_symbols = {}

    for config_file in config_files:
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
            symbol = None  # Initialize symbol to None for each config file
            
            # Try to get exchange-specific symbol first
            if selected_exchange == "BYBIT":
                symbol = config.get('ASSET', {}).get('bybit_symbol')
            elif selected_exchange == "BINANCE":
                symbol = config.get('ASSET', {}).get('binance_symbol')
            elif selected_exchange == "OKX":
                symbol = config.get('ASSET', {}).get('okx_symbol')
                
            # If no exchange-specific symbol found, try to get the default symbol
            if not symbol and 'ASSET' in config and 'symbol' in config['ASSET']:
                # For OKX, the general format in ASSET.symbol might be BTC-USDT-SWAP
                symbol = config['ASSET']['symbol']
                
            if symbol:
                symbols.append(symbol)
                # Store how the exchange expects the symbol (for ticker fetch)
                if selected_exchange == "OKX":
                    # OKX uses the full symbol format for futures (e.g., BTC-USDT-SWAP)
                    exchange_symbols[symbol] = symbol
                else:
                    # Other exchanges may have different formats
                    exchange_symbols[symbol] = symbol
                    
    symbols.sort()

    # Load performance data with weight ratios
    data_dir = os.path.join(script_dir, 'data')
    os.makedirs(data_dir, exist_ok=True)
    performance_file = os.path.join(data_dir, 'strategy_performance.csv')
    
    try:
        performance_df = pd.read_csv(performance_file)
        if 'weight_ratio' not in performance_df.columns:
            print("Warning: 'weight_ratio' column not found in strategy_performance.csv. Recalculating weight ratios...")
            performance_df = calculate_weight_ratios(performance_file)
    except Exception as e:
        print(f"Error reading strategy_performance.csv: {e}")
        return

    # Create a mapping of strategy names to their weights
    strategy_weights = dict(zip(performance_df['strategy_name'], performance_df['weight_ratio']))
    
    # New logic to count strategies per symbol and calculate weighted allocations
    strategy_counts = {}
    symbol_weights = {}
    
    # Debug: Print strategy weights
    print("Strategy weights loaded:")
    print(f"  Total strategies with weights: {len(strategy_weights)}")
    print(f"  First few: {list(strategy_weights.items())[:3]}")
    
    # Track strategies found in config files versus those in performance file
    strategies_in_configs = set()
    
    for config_file in config_files:
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
            symbol = None  # Initialize symbol to None for each config file
            
            # Try to get exchange-specific symbol first
            if selected_exchange == "BYBIT":
                symbol = config.get('ASSET', {}).get('bybit_symbol')
            elif selected_exchange == "BINANCE":
                symbol = config.get('ASSET', {}).get('binance_symbol')
            elif selected_exchange == "OKX":
                symbol = config.get('ASSET', {}).get('okx_symbol')
                
            # If no exchange-specific symbol found, try to get the default symbol
            if not symbol and 'ASSET' in config and 'symbol' in config['ASSET']:
                symbol = config['ASSET']['symbol']

            strategies = config.get('STRATEGIES', [])
            if symbol:
                # Count strategies for this symbol
                strategy_counts[symbol] = strategy_counts.get(symbol, 0) + len(strategies)
                
                # Calculate total weight for this symbol's strategies
                symbol_weight = 0
                strategies_found = 0
                
                for strategy in strategies:
                    strategy_name = strategy.get('name', '')
                    strategies_in_configs.add(strategy_name)
                    weight = strategy_weights.get(strategy_name, 0)
                    if weight > 0:
                        strategies_found += 1
                    symbol_weight += weight
                
                symbol_weights[symbol] = symbol_weight
                print(f"Symbol {symbol}: {strategies_found}/{len(strategies)} strategies matched with weights, total weight: {symbol_weight:.10f}")

    # Check how many strategies were matched
    performance_strats = set(performance_df['strategy_name'])
    missing_in_config = performance_strats - strategies_in_configs
    missing_in_perf = strategies_in_configs - performance_strats
    
    if missing_in_config:
        print(f"Warning: {len(missing_in_config)} strategies in performance data not found in config files")
        print(f"First few missing: {list(missing_in_config)[:5]}")
    
    if missing_in_perf:
        print(f"Warning: {len(missing_in_perf)} strategies in config files not found in performance data")
        print(f"First few missing: {list(missing_in_perf)[:5]}")

    # Check if we have valid strategy weights
    total_weight = sum(symbol_weights.values())
    if total_weight == 0:
        print("No valid strategy weights found. Cannot calculate maximum positions.")
        print("Using equal weights for all symbols as fallback.")
        
        # Fallback to equal weighting for all symbols
        symbol_count = len(symbols)
        if symbol_count > 0:
            equal_weight = 1.0 / symbol_count
            for symbol in symbols:
                symbol_weights[symbol] = equal_weight
            total_weight = 1.0
        else:
            print("No symbols found in config files. Cannot continue.")
            return

    print(f"Account Balance: {account_balance:,.2f}")
    print(f"Strategy Weights Total: {total_weight:.10f}")

    max_positions = []
    success_count = 0
    
    # Initialize markets dict to store pre-fetched market data
    # This avoids repeated fetch_ticker calls that might cause rate limit issues
    markets = {}
    
    try:
        print("Prefetching market data to avoid rate limit issues...")
        if selected_exchange == "OKX":
            # Set the correct market type
            EXCHANGE.options['defaultType'] = 'swap'
            
        # Load markets first with better error handling
        try:
            markets = EXCHANGE.load_markets()
            print(f"Successfully loaded {len(markets)} markets from {selected_exchange}")
        except Exception as e:
            print(f"Error loading markets: {e}")
            print("Will try to process symbols individually")
    except Exception as e:
        print(f"Error during market prefetch: {e}")
    
    for symbol in symbols:
        try:
            # Use the exchange-specific symbol for ticker fetch
            ticker_symbol = exchange_symbols.get(symbol, symbol)
            print(f"Fetching ticker for {ticker_symbol}...")
            
            current_price = None
            
            # Try to find the market in prefetched markets first
            if ticker_symbol in markets:
                print(f"Using prefetched market data for {ticker_symbol}")
                if 'info' in markets[ticker_symbol] and 'last' in markets[ticker_symbol]['info']:
                    current_price = float(markets[ticker_symbol]['info']['last'])
            
            # If not found in prefetched data, try direct fetch_ticker
            if not current_price:
                if selected_exchange == "OKX":
                    # Make sure we're using swap type for perpetual futures
                    EXCHANGE.options['defaultType'] = 'swap'
                
                ticker = EXCHANGE.fetch_ticker(ticker_symbol)
                if 'last' in ticker and ticker['last'] is not None:
                    current_price = ticker['last']
            
            # Skip symbols with invalid prices (0 or None)
            if not current_price or current_price <= 0:
                print(f"Warning: Invalid price (0 or None) for {symbol}. Skipping.")
                continue
                
            count = strategy_counts.get(symbol, 0)
            weight = symbol_weights.get(symbol, 0)
            
            # Calculate allocation based on symbol's weight proportion
            allocation = account_balance * weight / total_weight
            
            # Updated max position calculation to account for leverage
            Max_Pos = allocation * leverage / current_price
            print(
                f"Symbol: {symbol}, Number of strategies: {count}, Weight: {weight:.10f}, Fund allocation: {allocation:,.2f}, Max. Pos: {Max_Pos:,.6f}")

            # Collect data for CSV
            max_positions.append({
                'symbol': symbol,
                'strategy_count': count,
                'weight': weight,
                'fund_allocation': allocation,
                'max_pos': Max_Pos
            })
            success_count += 1
        except Exception as e:
            print(f"Error fetching price for {symbol}: {e}")
            # Add debug info for failed fetches
            if selected_exchange == "OKX" and "-USDT-SWAP" not in symbol:
                print(f"  Note: For OKX, symbols should include '-USDT-SWAP' suffix for perpetual futures")
                # Try with modified symbol
                modified_symbol = f"{symbol}-USDT-SWAP"
                print(f"  Attempting with modified symbol: {modified_symbol}")
                try:
                    ticker = EXCHANGE.fetch_ticker(modified_symbol)
                    current_price = ticker['last']
                    count = strategy_counts.get(symbol, 0)
                    weight = symbol_weights.get(symbol, 0)
                    allocation = account_balance * weight / total_weight
                    Max_Pos = allocation * leverage / current_price
                    print(f"  Success with modified symbol: {modified_symbol}, Price: {current_price}, Max Pos: {Max_Pos:,.6f}")
                    max_positions.append({
                        'symbol': symbol,
                        'strategy_count': count,
                        'weight': weight,
                        'fund_allocation': allocation,
                        'max_pos': Max_Pos
                    })
                    success_count += 1
                except Exception as e2:
                    print(f"  Failed with modified symbol: {e2}")

    print(f"Successfully processed {success_count}/{len(symbols)} symbols")
    
    if not max_positions:
        print("Warning: No valid positions were calculated. Data issues or connectivity problems may exist.")
        return

    # Save results to CSV
    data_dir = os.path.join(script_dir, 'data')
    os.makedirs(data_dir, exist_ok=True)
    csv_file = os.path.join(data_dir, 'max_positions.csv')
    df = pd.DataFrame(max_positions)

    # Leave numeric values as floats without formatting for CSV
    # No need to apply any formatting as we want raw float values

    df.to_csv(csv_file, index=False)
    print(f"Results saved to {csv_file}")


def calculate_weight_ratios(performance_file):
    """
    Calculate weight ratios for strategies based on the WEIGHT method in key.yaml.
    WEIGHT options: EQUAL, SR, MDD, EXCEL
    """
    weight_method = keys.get('WEIGHT', 'EQUAL').upper()
    print(f"Using weight method: {weight_method}")
    
    # Read performance data
    performance_df = pd.read_csv(performance_file)
    
    # Store the Updated column if it exists
    has_updated_column = 'Updated' in performance_df.columns
    updated_values = None
    if has_updated_column:
        updated_values = performance_df['Updated'].copy()
    
    if weight_method == "EQUAL":
        # Equal weighting
        total_strategies = len(performance_df)
        weight_ratio = 1.0 / total_strategies if total_strategies > 0 else 0
        performance_df['weight_ratio'] = weight_ratio
        print("Applied EQUAL weighting, all strategies have the same weight.")
    
    elif weight_method == "SR":
        # Weight based on Sharpe Ratio
        # Only use strategies with valid SR values
        valid_sr_mask = performance_df['SR'].astype(str).str.strip() != ''
        has_valid_sr = valid_sr_mask.any()
        
        if has_valid_sr:
            # Convert SR column to numeric, coercing invalid values to NaN
            sr_values = pd.to_numeric(performance_df['SR'], errors='coerce')
            
            # Replace NaN with 0
            sr_values = sr_values.fillna(0)
            
            # Get minimum SR
            min_sr = sr_values.min()
            
            # Adjust values to make all positive if there are negative values
            if min_sr < 0:
                adjusted_sr = sr_values - min_sr + 0.1  # Add 0.1 to avoid zero values
            else:
                adjusted_sr = sr_values
                
            # Calculate weights - only for strategies with valid SR
            total_sr = adjusted_sr.sum()
            if total_sr > 0:
                weights = adjusted_sr / total_sr
                performance_df['weight_ratio'] = weights
            else:
                # Fallback to equal weights if all SR values are 0
                performance_df['weight_ratio'] = 1.0 / len(performance_df)
        else:
            # No valid SR values, use equal weighting
            print("No valid SR values found. Using equal weighting as fallback.")
            performance_df['weight_ratio'] = 1.0 / len(performance_df)
        
        print("Applied SR weighting, higher Sharpe Ratio strategies have higher weights.")
    
    elif weight_method == "MDD":
        # Weight based on Maximum Drawdown (inverse - smaller MDD gets higher weight)
        valid_mdd_mask = performance_df['MDD'].astype(str).str.strip() != ''
        has_valid_mdd = valid_mdd_mask.any()
        
        if has_valid_mdd:
            # Convert MDD column to numeric, coercing invalid values to NaN
            mdd_values = pd.to_numeric(performance_df['MDD'], errors='coerce')
            
            # Replace NaN with 0
            mdd_values = mdd_values.fillna(0)
            
            # Inverse MDD values (1/MDD), handle zeros
            inverse_mdd = []
            for mdd in mdd_values:
                if mdd > 0:
                    # Direct inverse relationship: 1/MDD
                    inverse_mdd.append(1.0 / mdd)
                else:
                    # Default value for zero or negative MDD
                    inverse_mdd.append(1.0)
            
            # Calculate weights
            total_inverse_mdd = sum(inverse_mdd)
            if total_inverse_mdd > 0:
                performance_df['weight_ratio'] = [val / total_inverse_mdd for val in inverse_mdd]
            else:
                # Fallback to equal weights if calculation fails
                performance_df['weight_ratio'] = 1.0 / len(performance_df)
            
            # Print the MDD and weight values for verification
            print("\nMDD Weighting Details:")
            for i, row in performance_df.iterrows():
                mdd_val = mdd_values[i] if i < len(mdd_values) else 0
                inv_mdd = inverse_mdd[i] if i < len(inverse_mdd) else 0
                weight = performance_df.at[i, 'weight_ratio']
                print(f"  {row['strategy_name']}: MDD = {mdd_val:.10f}, Inverse MDD = {inv_mdd:.10f}, Weight = {weight:.10f}")
        else:
            # No valid MDD values, use equal weighting
            print("No valid MDD values found. Using equal weighting as fallback.")
            performance_df['weight_ratio'] = 1.0 / len(performance_df)
        
        print("Applied MDD weighting, strategies with lower Maximum Drawdown have higher weights.")
    
    elif weight_method == "CR":
        # Weight based on Calmar Ratio (higher CR gets higher weight)
        valid_cr_mask = performance_df['CR'].astype(str).str.strip() != ''
        has_valid_cr = valid_cr_mask.any()
        
        if has_valid_cr:
            # Convert CR column to numeric, coercing invalid values to NaN
            cr_values = pd.to_numeric(performance_df['CR'], errors='coerce')
            
            # Replace NaN with 0
            cr_values = cr_values.fillna(0)
            
            # Handle negative or zero CR values
            adjusted_cr = []
            for cr in cr_values:
                if cr > 0:
                    adjusted_cr.append(cr)
                else:
                    # Use a small positive value for negative or zero CR
                    adjusted_cr.append(0.001)  # Small positive default
            
            # Calculate weights
            total_cr = sum(adjusted_cr)
            if total_cr > 0:
                performance_df['weight_ratio'] = [val / total_cr for val in adjusted_cr]
            else:
                # Fallback to equal weights if all CR values are 0
                performance_df['weight_ratio'] = 1.0 / len(performance_df)
            
            # Print the CR and weight values for verification
            print("\nCR Weighting Details:")
            for i, row in performance_df.iterrows():
                cr_val = cr_values[i] if i < len(cr_values) else 0
                adj_cr = adjusted_cr[i] if i < len(adjusted_cr) else 0
                weight = performance_df.at[i, 'weight_ratio']
                print(f"  {row['strategy_name']}: CR = {cr_val:.10f}, Adjusted CR = {adj_cr:.10f}, Weight = {weight:.10f}")
        else:
            # No valid CR values, use equal weighting
            print("No valid CR values found. Using equal weighting as fallback.")
            performance_df['weight_ratio'] = 1.0 / len(performance_df)
        
        print("Applied CR weighting, strategies with higher Calmar Ratio have higher weights.")
    
    elif weight_method == "EXCEL":
        # Weight based on ratio field in config_summary.xlsx
        try:
            # Read the Strategies sheet from config_summary.xlsx
            excel_file = os.path.join(script_dir, 'config_summary.xlsx')
            
            # Check if the file exists first
            if not os.path.exists(excel_file):
                print(f"Warning: Excel file {excel_file} not found. Falling back to EQUAL weighting.")
                total_strategies = len(performance_df)
                weight_ratio = 1.0 / total_strategies if total_strategies > 0 else 0
                performance_df['weight_ratio'] = weight_ratio
            else:
                try:
                    excel_df = pd.read_excel(excel_file, sheet_name='Strategies')
                    
                    # Check if the 'Ratio' column exists
                    if 'Ratio' not in excel_df.columns:
                        print("Warning: 'Ratio' column not found in Strategies sheet. Falling back to EQUAL weighting.")
                        total_strategies = len(performance_df)
                        weight_ratio = 1.0 / total_strategies if total_strategies > 0 else 0
                        performance_df['weight_ratio'] = weight_ratio
                    else:
                        # Create a mapping of strategy name to ratio
                        strategy_ratios = dict(zip(excel_df['Strategy Name'], excel_df['Ratio']))
                        
                        # Apply the ratios to the performance dataframe
                        ratios = []
                        for strategy in performance_df['strategy_name']:
                            ratio = strategy_ratios.get(strategy, 0)
                            ratios.append(float(ratio) if ratio else 0)
                        
                        # Check if any ratios are non-zero before dividing
                        total_ratio = sum(ratios)
                        if total_ratio > 0:
                            performance_df['weight_ratio'] = [ratio / total_ratio for ratio in ratios]
                        else:
                            # Fallback to equal weights if no valid ratios
                            print("Warning: No valid non-zero ratios found in Excel. Falling back to EQUAL weighting.")
                            total_strategies = len(performance_df)
                            weight_ratio = 1.0 / total_strategies if total_strategies > 0 else 0
                            performance_df['weight_ratio'] = weight_ratio
                except Exception as e:
                    print(f"Error reading Excel sheet: {e}. Falling back to EQUAL weighting.")
                    total_strategies = len(performance_df)
                    weight_ratio = 1.0 / total_strategies if total_strategies > 0 else 0
                    performance_df['weight_ratio'] = weight_ratio
                
                print("Applied EXCEL weighting based on ratios from config_summary.xlsx.")
        except Exception as e:
            print(f"Error applying EXCEL weighting: {e}. Falling back to EQUAL weighting.")
            total_strategies = len(performance_df)
            weight_ratio = 1.0 / total_strategies if total_strategies > 0 else 0
            performance_df['weight_ratio'] = weight_ratio
    
    else:
        # Unknown weighting method, fallback to EQUAL
        print(f"Unknown weighting method '{weight_method}'. Falling back to EQUAL weighting.")
        total_strategies = len(performance_df)
        weight_ratio = 1.0 / total_strategies if total_strategies > 0 else 0
        performance_df['weight_ratio'] = weight_ratio
    
    # Ensure weights sum to 1 (handle floating point precision issues)
    performance_df['weight_ratio'] = performance_df['weight_ratio'] / performance_df['weight_ratio'].sum()
    
    # Round to 10 decimal places instead of 4 for higher precision
    performance_df['weight_ratio'] = performance_df['weight_ratio'].round(10)
    
    # Restore the Updated column if it existed
    if has_updated_column:
        performance_df['Updated'] = updated_values
    
    # Save updated performance data with weight ratios
    performance_df.to_csv(performance_file, index=False)
    
    print("Weight ratios summary:")
    for _, row in performance_df.iterrows():
        print(f"  {row['strategy_name']}: {row['weight_ratio']:.10f}")
    
    print(f"Total weight: {performance_df['weight_ratio'].sum():.10f}")
    
    return performance_df


def get_rebalance_seconds(rebalance_freq):
    """
    Convert rebalance frequency string (like '5m', '1h', '2d', '1w', '1m') to seconds.
    Returns:
        int: Number of seconds between rebalances
    """
    if not rebalance_freq:
        return 24 * 60 * 60  # Default: daily (24 hours)
    
    # Extract number and unit from rebalance_freq
    match = re.match(r'(\d+)([mhdwM])', rebalance_freq)
    if not match:
        print(f"Invalid rebalance frequency format: {rebalance_freq}, using daily by default")
        return 24 * 60 * 60
    
    number, unit = match.groups()
    number = int(number)
    
    # Convert to seconds
    if unit == 'm':
        return number * 60  # minutes
    elif unit == 'h':
        return number * 60 * 60  # hours
    elif unit == 'd':
        return number * 24 * 60 * 60  # days
    elif unit == 'w':
        return number * 7 * 24 * 60 * 60  # weeks
    elif unit == 'M':
        return number * 30 * 24 * 60 * 60  # months (approximate)
    else:
        print(f"Unknown time unit: {unit}, using daily by default")
        return 24 * 60 * 60

def log_with_timestamp(message):
    """Add timestamp to log messages"""
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{current_time}] {message}")

def clear_rebalance_files():
    """
    Clear max_positions.csv and strategy_performance.csv files if they exist
    to ensure a clean rebalancing process.
    """
    data_dir = os.path.join(script_dir, 'data')
    os.makedirs(data_dir, exist_ok=True)
    
    max_positions_file = os.path.join(data_dir, 'max_positions.csv')
    performance_file = os.path.join(data_dir, 'strategy_performance.csv')
    complete_flag_file = os.path.join(data_dir, 'rebalance_complete.flag')
    
    # Create backup copies before deletion
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Remove completion flag if it exists
    if os.path.exists(complete_flag_file):
        try:
            # Create a backup of the old flag file for debugging purposes
            backup_flag_file = os.path.join(data_dir, f'rebalance_complete_backup_{timestamp}.flag')
            shutil.copy2(complete_flag_file, backup_flag_file)
            log_with_timestamp(f"Backed up previous rebalance completion flag to: {backup_flag_file}")
            
            # Now remove the flag file
            os.remove(complete_flag_file)
            log_with_timestamp("Removed rebalance completion flag for new rebalance cycle")
        except Exception as e:
            log_with_timestamp(f"Warning: Failed to remove rebalance completion flag: {e}")
    
    # Handle max_positions.csv
    if os.path.exists(max_positions_file):
        try:
            backup_file = os.path.join(data_dir, f'max_positions_backup_{timestamp}.csv')
            shutil.copy2(max_positions_file, backup_file)
            log_with_timestamp(f"Created backup of max_positions.csv at: {backup_file}")
            
            # Remove the original file
            os.remove(max_positions_file)
            log_with_timestamp("Cleared max_positions.csv for clean rebalancing")
        except Exception as e:
            log_with_timestamp(f"Warning: Failed to backup/clear max_positions.csv: {e}")
    
    # Handle strategy_performance.csv
    if os.path.exists(performance_file):
        try:
            # Read existing file to preserve strategy names and latest metrics
            performance_df = None
            try:
                performance_df = pd.read_csv(performance_file)
                backup_file = os.path.join(data_dir, f'strategy_performance_backup_{timestamp}.csv')
                shutil.copy2(performance_file, backup_file)
                log_with_timestamp(f"Created backup of strategy_performance.csv at: {backup_file}")
            except Exception as e:
                log_with_timestamp(f"Warning: Could not read/backup strategy_performance.csv: {e}")
            
            # Remove the original file
            os.remove(performance_file)
            log_with_timestamp("Cleared strategy_performance.csv for clean rebalancing")
            
            # If we could read the file earlier, create a new one preserving strategy names and metrics
            # but clearing the 'Updated' flag and weight_ratio
            if performance_df is not None and not performance_df.empty:
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                # Preserve strategy_name, SR, AR, CR, MDD, POS fields if they exist
                columns_to_keep = ['strategy_name']
                metric_columns = ['SR', 'AR', 'CR', 'MDD', 'POS']
                for col in metric_columns:
                    if col in performance_df.columns:
                        columns_to_keep.append(col)
                
                # Create a new DataFrame with only the preserved columns
                new_df = performance_df[columns_to_keep].copy()
                
                # Add timestamp and Updated columns
                new_df['timestamp'] = current_time
                new_df['Updated'] = False
                
                # Add weight_ratio column with empty values (will be recalculated)
                new_df['weight_ratio'] = ''
                
                # Save the new DataFrame
                new_df.to_csv(performance_file, index=False)
                log_with_timestamp(f"Created new strategy_performance.csv with {len(new_df)} strategies (preserved metrics but reset Updated flag and weight_ratio)")
        except Exception as e:
            log_with_timestamp(f"Warning: Failed to process strategy_performance.csv: {e}")
    
    log_with_timestamp("Rebalance file preparation complete. New rebalance cycle started.")


def create_rebalance_complete_flag():
    """
    Create a flag file indicating that rebalance process is 100% complete.
    This helps synchronize with trading scripts to ensure they don't start
    trading until rebalancing is fully finished.
    
    The flag file includes the current timestamp and rebalance cycle information
    to help trading scripts determine if they're looking at the correct cycle.
    
    IMPORTANT: This should ONLY be called when rebalance is truly 100% complete.
    """
    data_dir = os.path.join(script_dir, 'data')
    os.makedirs(data_dir, exist_ok=True)
    
    complete_flag_file = os.path.join(data_dir, 'rebalance_complete.flag')
    
    try:
        # Get timestamp with milliseconds for precise tracking
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
        
        # Create the flag file with timestamp and cycle information inside
        with open(complete_flag_file, 'w') as f:
            # Include timestamp and unique cycle identifier (using timestamp as the ID)
            cycle_id = str(int(time.time()))
            f.write(f"Rebalance completed at: {current_time}\n")
            f.write(f"Cycle ID: {cycle_id}\n")
            
            # Also include max_positions.csv modification time to help with syncing
            try:
                max_positions_file = os.path.join(data_dir, 'max_positions.csv')
                if os.path.exists(max_positions_file):
                    max_pos_mtime = os.path.getmtime(max_positions_file)
                    f.write(f"max_positions.csv timestamp: {max_pos_mtime}\n")
            except Exception as e:
                f.write(f"Error getting max_positions.csv timestamp: {e}\n")
        
        log_with_timestamp(f"★★★ REBALANCE 100% COMPLETE ★★★ (Cycle ID: {cycle_id})")
        log_with_timestamp(f"Created rebalance completion flag - Trading scripts will now use the new position sizes")
    except Exception as e:
        log_with_timestamp(f"CRITICAL ERROR: Failed to create rebalance completion flag: {e}")
        log_with_timestamp(f"Trading scripts will continue to wait until this is fixed!")


def process_rebalance():
    """Process rebalancing: update strategy performance weights and max positions"""
    log_with_timestamp("==========================================================")
    log_with_timestamp("STARTING SCHEDULED REBALANCE PROCESS (0% COMPLETE)")
    log_with_timestamp("==========================================================")
    
    # Clear existing files before starting rebalance
    clear_rebalance_files()
    
    # Count total strategies from all config files
    config_dir = os.path.join(script_dir, 'config')
    config_files = glob.glob(os.path.join(config_dir, 'config_*.yaml'))
    total_strategies = 0
    
    # Collect all strategy names
    strategy_names = []
    
    for config_file in config_files:
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
            strategies = config.get('STRATEGIES', [])
            total_strategies += len(strategies)
            for strategy in strategies:
                if 'name' in strategy:
                    strategy_names.append(strategy['name'])
    
    if total_strategies == 0:
        log_with_timestamp("No strategies found in config files. Cannot proceed with rebalance.")
        return
    
    # Sort strategy names alphabetically
    strategy_names.sort()
    
    log_with_timestamp(f"Total strategies found in config files for rebalance: {total_strategies}")
    log_with_timestamp("Progress: 10% complete - Found strategies")
    
    # Check if strategy_performance.csv exists and has matching records
    data_dir = os.path.join(script_dir, 'data')
    os.makedirs(data_dir, exist_ok=True)
    performance_file = os.path.join(data_dir, 'strategy_performance.csv')
    
    # Check if the performance file exists
    if not os.path.exists(performance_file):
        log_with_timestamp(f"Strategy performance file not found: {performance_file}. Creating new file.")
        
        # Create a new performance file with all strategies
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        performance_data = []
        
        for strategy_name in strategy_names:
            performance_data.append({
                'strategy_name': strategy_name,
                'SR': '',
                'AR': '',
                'CR': '',
                'MDD': '',
                'POS': '',
                'timestamp': current_time,
                'Updated': False
            })
        
        performance_df = pd.DataFrame(performance_data)
        performance_df.to_csv(performance_file, index=False)
        log_with_timestamp(f"Created new performance file with {len(performance_df)} strategies")
    
    # Update the performance file with any new strategies
    try:
        performance_df = pd.read_csv(performance_file)
        
        # Check if "Updated" column exists, add it if not
        if 'Updated' not in performance_df.columns:
            performance_df['Updated'] = False
            log_with_timestamp("Added 'Updated' column to existing performance file")
        
        # Get list of existing strategies
        existing_strategies = set(performance_df['strategy_name'])
        new_strategies = []
        
        # Find strategies that are not in the file
        for strategy_name in strategy_names:
            if strategy_name not in existing_strategies:
                new_strategies.append(strategy_name)
        
        # Add new strategies if any
        if new_strategies:
            log_with_timestamp(f"Adding {len(new_strategies)} new strategies to performance file")
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            for strategy_name in new_strategies:
                new_row = {
                    'strategy_name': strategy_name,
                    'SR': '',
                    'AR': '',
                    'CR': '',
                    'MDD': '',
                    'POS': '',
                    'timestamp': current_time,
                    'Updated': False
                }
                # Append new row to DataFrame
                performance_df = pd.concat([performance_df, pd.DataFrame([new_row])], ignore_index=True)
        
        # Sort by strategy_name
        performance_df = performance_df.sort_values(by='strategy_name').reset_index(drop=True)
        
        # Save the updated performance file
        performance_df.to_csv(performance_file, index=False)
        log_with_timestamp(f"Updated performance file now has {len(performance_df)} strategies")
        log_with_timestamp("Progress: 30% complete - Updated strategy_performance.csv")
        
        strategy_count = len(performance_df)
        
        if strategy_count < total_strategies:
            log_with_timestamp(f"Warning: Not all strategies are in the performance file. {strategy_count}/{total_strategies} available.")
        elif strategy_count > total_strategies:
            log_with_timestamp(f"Warning: More strategies in performance file ({strategy_count}) than in config files ({total_strategies}). This may indicate outdated records.")
    except Exception as e:
        log_with_timestamp(f"Error reading/updating strategy_performance.csv: {e}. Cannot proceed with rebalance.")
        return
    
    # Get weight method from key.yaml
    weight_method = keys.get('WEIGHT', 'EQUAL').upper()
    log_with_timestamp(f"Using weight method from key.yaml: {weight_method}")
    
    # Perform rebalance with retry logic
    rebalance_success = False
    retry_count = 0
    max_retry = 5  # Maximum number of retries for scheduled rebalance
    
    while not rebalance_success and retry_count < max_retry:
        try:
            # Calculate and update weight ratios
            log_with_timestamp("Progress: 50% complete - Calculating strategy weight ratios...")
            calculate_weight_ratios(performance_file)
            
            # Get account balance and recalculate available collaterals
            log_with_timestamp("Progress: 70% complete - Recalculating available collaterals...")
            account_balance, _, _ = get_acct_bal()
            
            # Get leverage from key.yaml
            leverage = keys.get('LEVERAGE', 1.0)
            log_with_timestamp(f"Using leverage from key.yaml: {leverage}")
            
            # Update max positions
            log_with_timestamp("Progress: 90% complete - Updating maximum positions...")
            calculate_max_pos(account_balance, leverage)
            
            log_with_timestamp("Progress: 99% complete - Final verification checks...")
            
            # Final verification to ensure max_positions.csv was actually created and contains data
            max_positions_file = os.path.join(data_dir, 'max_positions.csv')
            if not os.path.exists(max_positions_file):
                raise Exception("max_positions.csv was not created - cannot complete rebalance")
                
            try:
                max_pos_df = pd.read_csv(max_positions_file)
                if max_pos_df.empty:
                    raise Exception("max_positions.csv exists but contains no data")
                log_with_timestamp(f"Verified max_positions.csv contains {len(max_pos_df)} symbols")
            except Exception as e:
                raise Exception(f"Error verifying max_positions.csv content: {e}")
                
            # Create completion flag file to signal trading scripts
            # This should be the VERY LAST step in the rebalance process
            create_rebalance_complete_flag()
            
            log_with_timestamp("==========================================================")
            log_with_timestamp("REBALANCE PROCESS COMPLETED SUCCESSFULLY (100% COMPLETE)")
            log_with_timestamp("==========================================================")
            
            rebalance_success = True
        except Exception as e:
            retry_count += 1
            log_with_timestamp(f"Rebalance attempt {retry_count} failed: {e}")
            if retry_count < max_retry:
                log_with_timestamp(f"Retrying in 5 seconds... ({retry_count}/{max_retry})")
                time.sleep(5)
            else:
                log_with_timestamp(f"Maximum retry attempts ({max_retry}) reached. Rebalance failed.")
                log_with_timestamp("Trading scripts will continue to wait as no completion flag was created")


def main():
    """
    Main function to run initial calculation and schedule regular updates.
    """
    # Clear existing files before starting rebalance
    clear_rebalance_files()
    
    # Display margin balance details before processing
    log_with_timestamp("==========================================================")
    log_with_timestamp("STARTING INITIAL REBALANCE PROCESS (0% COMPLETE)")
    log_with_timestamp("==========================================================")
    
    log_with_timestamp("Fetching and calculating available collaterals...")
    account_balance, collateral_value, margin_details = get_acct_bal()

    # Get leverage from key.yaml instead of user input
    leverage = keys.get('LEVERAGE', 1.0)
    log_with_timestamp(f"Using leverage from key.yaml: {leverage}")
    log_with_timestamp("Progress: 10% complete - Loaded configuration")
    
    # Get rebalance frequency
    rebalance_freq = keys.get('REBALANCE_FREQ', '24h')
    log_with_timestamp(f"Using rebalance frequency from key.yaml: {rebalance_freq}")
    
    # Get weight method from key.yaml
    weight_method = keys.get('WEIGHT', 'EQUAL').upper()
    log_with_timestamp(f"Using weight method from key.yaml: {weight_method}")
    
    # Count total strategies from all config files
    config_dir = os.path.join(script_dir, 'config')
    config_files = glob.glob(os.path.join(config_dir, 'config_*.yaml'))
    total_strategies = 0
    
    # Collect all strategy names
    strategy_names = []
    
    for config_file in config_files:
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
            strategies = config.get('STRATEGIES', [])
            total_strategies += len(strategies)
            for strategy in strategies:
                if 'name' in strategy:
                    strategy_names.append(strategy['name'])
    
    if total_strategies == 0:
        log_with_timestamp("No strategies found in config files. Cannot proceed.")
        return
    
    # Sort strategy names alphabetically
    strategy_names.sort()
    
    log_with_timestamp(f"Total strategies found in config files: {total_strategies}")
    log_with_timestamp("Progress: 20% complete - Found all strategies")
    
    # Check if strategy_performance.csv exists and has matching records
    data_dir = os.path.join(script_dir, 'data')
    os.makedirs(data_dir, exist_ok=True)
    performance_file = os.path.join(data_dir, 'strategy_performance.csv')
    
    # Perform initial run
    log_with_timestamp("Performing initial calculation...")
    
    # Create or update strategy_performance.csv
    if not os.path.exists(performance_file):
        log_with_timestamp(f"Creating new strategy_performance.csv file with {len(strategy_names)} strategies")
        
        # Create dataframe with all required columns
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        performance_data = []
        
        for strategy_name in strategy_names:
            performance_data.append({
                'strategy_name': strategy_name,
                'SR': '',  # Empty string instead of space
                'AR': '',
                'CR': '',
                'MDD': '',
                'POS': '',
                'timestamp': current_time,
                'Updated': False
            })
        
        performance_df = pd.DataFrame(performance_data)
    else:
        # Read existing file and update with any new strategies
        log_with_timestamp("Reading existing strategy_performance.csv file")
        try:
            performance_df = pd.read_csv(performance_file)
            
            # Check if "Updated" column exists, add it if not
            if 'Updated' not in performance_df.columns:
                performance_df['Updated'] = False
                log_with_timestamp("Added 'Updated' column to existing performance file")
            
            # Get list of existing strategies
            existing_strategies = set(performance_df['strategy_name'])
            new_strategies = []
            
            # Find strategies that are not in the file
            for strategy_name in strategy_names:
                if strategy_name not in existing_strategies:
                    new_strategies.append(strategy_name)
            
            # Add new strategies if any
            if new_strategies:
                log_with_timestamp(f"Adding {len(new_strategies)} new strategies to performance file")
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                
                for strategy_name in new_strategies:
                    new_row = {
                        'strategy_name': strategy_name,
                        'SR': '',
                        'AR': '',
                        'CR': '',
                        'MDD': '',
                        'POS': '',
                        'timestamp': current_time,
                        'Updated': False
                    }
                    # Append new row to DataFrame
                    performance_df = pd.concat([performance_df, pd.DataFrame([new_row])], ignore_index=True)
            
            # Sort by strategy_name
            performance_df = performance_df.sort_values(by='strategy_name').reset_index(drop=True)
            
        except Exception as e:
            log_with_timestamp(f"Error reading existing performance file: {e}. Creating new file.")
            
            # Create new file with all strategies
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            performance_data = []
            
            for strategy_name in strategy_names:
                performance_data.append({
                    'strategy_name': strategy_name,
                    'SR': '',
                    'AR': '',
                    'CR': '',
                    'MDD': '',
                    'POS': '',
                    'timestamp': current_time,
                    'Updated': False
                })
            
            performance_df = pd.DataFrame(performance_data)
    
    # Save the performance file
    performance_df.to_csv(performance_file, index=False)
    log_with_timestamp(f"Saved {len(performance_df)} strategies to {performance_file}")
    log_with_timestamp("Progress: 30% complete - Updated strategy_performance.csv")
    
    # Skip waiting for all strategies to be processed if using EXCEL or EQUAL weight method
    if weight_method in ["EXCEL", "EQUAL"]:
        log_with_timestamp(f"Using {weight_method} weight method - no need to wait for all strategies to be processed")
        log_with_timestamp("Progress: 40% complete - Weight method doesn't require waiting")
    else:
        # For SR, CR, and MDD methods, wait for strategy metrics to be updated by Trade scripts
        log_with_timestamp(f"Using {weight_method} weight method - waiting for strategy metrics to be updated...")
        log_with_timestamp("Progress: 40% complete - Waiting for strategy updates")
        wait_start_time = datetime.now()
        
        # Print progress bar header
        print("\nWaiting for strategies to be updated:")

        # Add warning message about potential fallback before starting countdown
        minutes_wait = max_wait_time / 60
        print("\n")
        print("⚠️  ATTENTION  ⚠️".center(80, "-"))
        print(f"WAITING FOR STRATEGY UPDATES FOR UP TO {minutes_wait:.1f} MINUTES".center(80))
        print(f"IF NOT ALL STRATEGIES ARE UPDATED WITHIN THIS TIME,".center(80))
        print(f"WEIGHTING WILL AUTOMATICALLY FALL BACK TO 'EQUAL' METHOD".center(80)) 
        print(f"FROM '{weight_method}' METHOD".center(80))
        print("-" * 80)
        print("\n")

        fallback_to_equal = False
        
        while True:
            try:
                updated_df = pd.read_csv(performance_file)
                
                # Count strategies with Updated flag set to True
                # Convert Updated column to boolean if it's string
                if updated_df['Updated'].dtype == 'object':
                    updated_df['Updated'] = updated_df['Updated'].map({'True': True, 'False': False})
                
                strategies_updated = sum(updated_df['Updated'] == True)
                update_percent = strategies_updated / total_strategies * 100 if total_strategies > 0 else 0
                
                # Create progress bar
                bar_length = 56
                filled_length = int(bar_length * strategies_updated // total_strategies)
                bar = '█' * filled_length + ' ' * (bar_length - filled_length)
                
                # Clear previous line and print progress
                print(f"\r║ Progress [{bar}] {update_percent:.1f}% ║", end='')
                
                if strategies_updated >= total_strategies:
                    print("\n╚════════════════════════════════════════════════════════════════════════╝")
                    log_with_timestamp(f"All {strategies_updated}/{total_strategies} strategies have been updated")
                    log_with_timestamp("Progress: 50% complete - All strategies updated")
                    performance_df = updated_df
                    break
                else:
                    # Check if we've been waiting too long
                    elapsed_time = (datetime.now() - wait_start_time).total_seconds()
                    if elapsed_time > max_wait_time:
                        print("\n╚════════════════════════════════════════════════════════════════════════╝")
                        log_with_timestamp(f"Timeout after {max_wait_time} seconds. Only {strategies_updated}/{total_strategies} strategies updated.")
                        
                        # Add prominent warning message about fallback
                        minutes_waited = max_wait_time / 60
                        print("\n")
                        print("⚠️  WARNING  ⚠️".center(80, "!"))
                        print(f"TIMEOUT REACHED AFTER {minutes_waited:.1f} MINUTES OF WAITING".center(80))
                        print(f"STRATEGY WEIGHTING FALLING BACK TO 'EQUAL' METHOD".center(80))
                        print(f"INSTEAD OF '{weight_method}' METHOD".center(80))
                        print("!" * 80)
                        print("\n")
                        
                        log_with_timestamp(f"Falling back to EQUAL weight method instead of {weight_method}.")
                        log_with_timestamp("Progress: 50% complete - Falling back to EQUAL weights")
                        performance_df = updated_df
                        fallback_to_equal = True
                        break
                    
                    # Sleep briefly to avoid excessive CPU usage
                    time.sleep(1)
                    continue
            except Exception as e:
                print("\n╚════════════════════════════════════════════════════════════════════════╝")
                log_with_timestamp(f"Error checking strategy updates: {e}. Retrying in 5 seconds...")
                time.sleep(5)
        
        # If we timed out, change the weight method to EQUAL
        if fallback_to_equal:
            weight_method = "EQUAL"
            keys['WEIGHT'] = "EQUAL"
            log_with_timestamp("Weight method changed to EQUAL for this session")

    # Perform initial rebalance with retry logic
    log_with_timestamp("Progress: 60% complete - Attempting initial rebalance calculations...")
    initial_success = False
    retry_count = 0
    max_retry = 250  # Maximum retries (250 seconds)
    
    while not initial_success and retry_count < max_retry:
        try:
            # Calculate and update weight ratios in the performance file
            log_with_timestamp("Progress: 70% complete - Calculating strategy weight ratios...")
            calculate_weight_ratios(performance_file)
            
            # Recalculate available collaterals for initial balance
            log_with_timestamp("Progress: 80% complete - Recalculating available collaterals...")
            account_balance, _, _ = get_acct_bal()
            
            # Now that we have verified all strategies are processed and added weight ratios, calculate max positions
            log_with_timestamp("Progress: 90% complete - Calculating maximum positions...")
            calculate_max_pos(account_balance, leverage)
            
            # Final verification to ensure max_positions.csv was actually created and contains data
            log_with_timestamp("Progress: 99% complete - Final verification checks...")
            max_positions_file = os.path.join(data_dir, 'max_positions.csv')
            if not os.path.exists(max_positions_file):
                raise Exception("max_positions.csv was not created - cannot complete rebalance")
                
            try:
                max_pos_df = pd.read_csv(max_positions_file)
                if max_pos_df.empty:
                    raise Exception("max_positions.csv exists but contains no data")
                log_with_timestamp(f"Verified max_positions.csv contains {len(max_pos_df)} symbols")
            except Exception as e:
                raise Exception(f"Error verifying max_positions.csv content: {e}")
            
            # If we reached here without exceptions, the initial rebalance was successful
            log_with_timestamp("Initial rebalance calculations completed successfully!")
            
            # Create the rebalance completion flag file - MUST BE THE VERY LAST STEP
            create_rebalance_complete_flag()
            
            log_with_timestamp("==========================================================")
            log_with_timestamp("INITIAL REBALANCE PROCESS COMPLETED SUCCESSFULLY (100% COMPLETE)")
            log_with_timestamp("==========================================================")
            
            initial_success = True
        except Exception as e:
            retry_count += 1
            log_with_timestamp(f"Initial rebalance attempt {retry_count} failed: {e}")
            if retry_count < max_retry:
                log_with_timestamp(f"Retrying in 1 second... ({retry_count}/{max_retry})")
                time.sleep(1)
            else:
                log_with_timestamp(f"Maximum retry attempts ({max_retry}) reached. Moving to normal schedule.")
                log_with_timestamp("Trading scripts will continue to wait as no completion flag was created")
    
    # Only proceed to normal scheduling if initial rebalance succeeded
    if not initial_success:
        log_with_timestamp("Warning: Could not complete initial rebalance successfully after maximum retries.")
        log_with_timestamp("Will continue with normal schedule despite initial failure.")
    
    # Get normal rebalance frequency 
    normal_rebalance_seconds = get_rebalance_seconds(rebalance_freq)
    
    log_with_timestamp(f"Moving to normal rebalance schedule: every {rebalance_freq}")
    
    # Set next run based on normal rebalance frequency
    next_run = datetime.now() + timedelta(seconds=normal_rebalance_seconds)
    log_with_timestamp(f"Next rebalance scheduled for: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Setup scheduled rebalancing
    try:
        while True:
            current_time = datetime.now()
            
            # Check if we should run now
            if current_time >= next_run:
                process_rebalance()
                
                # Set next run time using normal frequency
                next_run = current_time + timedelta(seconds=normal_rebalance_seconds)
                log_with_timestamp(f"Next rebalance scheduled for: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Sleep to reduce CPU usage (check every minute)
            time.sleep(60)
    except KeyboardInterrupt:
        log_with_timestamp("Rebalancing process terminated by user.")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("Trading Ended!")