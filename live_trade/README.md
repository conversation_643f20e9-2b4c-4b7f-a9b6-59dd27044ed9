# Fund Management Automation

This system automates the setup and management of trading strategies for multiple funds.

## Overview

The fund management automation system:

1. Processes Excel files in the `fund/` directory with the naming convention `fund*.xlsx`
2. Creates a new folder for each fund based on the template folder
3. Copies the fund's Excel file to the new folder as `config_summary.xlsx`
4. Updates the `config/key.yaml` file with data from the key worksheet

## Components

### Fund_Manager.py

The main script that handles the fund management automation. It:

- Scans the `fund/` folder for `fund*.xlsx` files
- Clones the `template/` folder for each fund
- Copies the fund Excel file to the new folder as `config_summary.xlsx`
- Runs the Strategy_maker.py script to update configuration files

### Strategy_maker.py (Updated)

Updates the configuration based on the Excel file. Key changes:
- Added functionality to process the Key worksheet and update key.yaml
- Handles the configuration for each fund automatically

## Excel File Structure

Each fund's Excel file should have the following worksheets:

1. **Strategies**: Contains strategy configurations (name, parameters, etc.)
2. **General Config**: Contains general fund configurations
3. **Key**: Contains key-value pairs for the key.yaml file with the following structure:

| Key            | Value       |
|----------------|-------------|
| EXCHANGE       | BYBIT       |
| APIKEY         | your_api_key|
| SECRET         | your_secret |
| LIVE           | Y           |
| VIP            | 1           |
| SHIFT          | 0           |
| WEIGHT         | SHARPE      |
| LEVERAGE       | 5           |
| REBALANCE_FREQ | 10m         |
| GLASSNODE_API  | your_api_key|
| TG_CHATID      | your_chat_id|
| TG_BOT_TOKEN   | your_token  |

## Usage

Run the fund management automation:

```bash
python live_trade/Fund_Manager.py
```

This will:
1. Scan for fund Excel files
2. Create a folder for each fund
3. Update configurations for each fund

## Folder Structure

```
live_trade/
├── Fund_Manager.py       # Main fund management script
├── fund/                 # Contains fund Excel files
│   ├── fund1.xlsx
│   ├── fund2.xlsx
│   └── ...
├── template/             # Template folder to clone for each fund
│   ├── config/
│   │   ├── key.yaml
│   │   └── ...
│   ├── Strategy_maker.py
│   ├── Trade_BTC.py
│   ├── Portfolio.py
│   ├── config_summary.xlsx
│   └── ...
├── fund1/                # Generated fund folders
├── fund2/
└── ...
``` 