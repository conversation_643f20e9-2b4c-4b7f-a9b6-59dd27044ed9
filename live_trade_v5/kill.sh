#!/bin/bash

# Detect operating system
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "Detected Linux system..."
    IS_MACOS=false
elif [[ "$OSTYPE" == "darwin"* ]]; then
    echo "Detected macOS system..."
    IS_MACOS=true
else
    echo "Unsupported operating system: $OSTYPE"
    echo "This script only supports Linux and macOS."
    exit 1
fi

# Check if script is running with admin/sudo privileges
check_admin_privileges() {
    if $IS_MACOS; then
        if [ $(id -u) -ne 0 ]; then
            echo "Warning: This script may need admin privileges to kill some processes."
            echo "Consider re-running with: sudo $0"
            ADMIN_PRIVILEGES=false
        else
            echo "Running with admin privileges."
            ADMIN_PRIVILEGES=true
        fi
    fi
}

# Cleanup function to terminate all trading-related processes
cleanup_processes() {
    echo "Cleaning up existing trading processes..."
    
    # Kill all screen sessions
    echo "Terminating screen sessions..."
    
    # Get screen sessions properly without causing errors with empty session names
    SCREEN_SESSIONS=$(screen -ls 2>/dev/null | grep -E '\s+\([Dd]etached\)|\s+\([Aa]ttached\)' | awk '{print $1}')
    
    if [ -n "$SCREEN_SESSIONS" ]; then
        echo "$SCREEN_SESSIONS" | while read -r session; do
            if [ -n "$session" ]; then
                echo "Killing screen session $session"
                screen -XS "$session" quit 2>/dev/null
            fi
        done
    else
        echo "No screen sessions found."
    fi
    
    # Wait briefly for sessions to terminate
    sleep 1
    
    # Force kill actual screen processes (being precise)
    echo "Force killing screen processes..."
    
    if $IS_MACOS; then
        # macOS specific process killing
        # First, get all screen processes with full command to identify them properly
        SCREEN_PROCESSES=$(ps -ef | grep "[s]creen" | awk '{print $2 " " $8}')
        
        if [ -n "$SCREEN_PROCESSES" ]; then
            echo "Found screen processes:"
            echo "$SCREEN_PROCESSES"
            
            # Extract just the PIDs and kill them
            echo "$SCREEN_PROCESSES" | awk '{print $1}' | xargs -I{} kill -9 {} 2>/dev/null || true
            
            # Additionally kill any potentially related screen processes by name
            pkill -9 -x screen 2>/dev/null || true
        else
            echo "No screen processes found on macOS."
        fi
        
        # Kill python processes on macOS with more aggressive approach
        echo "Terminating Python processes on macOS..."
        
        # Find all Python processes
        PYTHON_PROCESSES=$(ps -ef | grep -E "[p]ython|[P]ython" | awk '{print $2 " " $8}')
        
        if [ -n "$PYTHON_PROCESSES" ]; then
            echo "Found Python processes:"
            echo "$PYTHON_PROCESSES"
            
            # Extract just the PIDs and kill them
            echo "$PYTHON_PROCESSES" | awk '{print $1}' | xargs -I{} kill -9 {} 2>/dev/null || true
            
            # Try using sudo if available
            if [ $(id -u) -eq 0 ]; then
                echo "Attempting to kill Python processes with admin privileges..."
                # Using various Python executable names to catch all variants
                sudo pkill -9 -f "[p]ython" 2>/dev/null || true
                sudo pkill -9 -f "[P]ython" 2>/dev/null || true
                sudo pkill -9 -f "python2" 2>/dev/null || true
                sudo pkill -9 -f "python3" 2>/dev/null || true
                sudo pkill -9 -f "python3.1[0-9]" 2>/dev/null || true
            else
                echo "Not running with admin privileges. Some Python processes might not be killed."
                echo "Consider re-running with sudo if processes remain."
            fi
        else
            echo "No Python processes found on macOS."
        fi
    else
        # Linux process killing
        SCREEN_PIDS=$(pgrep -x screen)
        if [ -n "$SCREEN_PIDS" ]; then
            echo "Found screen processes with PIDs: $SCREEN_PIDS"
            kill -9 $SCREEN_PIDS 2>/dev/null || true
        else
            echo "No screen processes found on Linux."
        fi
    fi
    
    # Cleanup screen sockets
    echo "Cleaning up screen socket files..."
    if $IS_MACOS; then
        # macOS specific socket cleanup
        screen -wipe >/dev/null 2>&1
        
        # Check home directory
        find "$HOME/.screen" -type s -delete 2>/dev/null || true
        rm -f "$HOME/.screen/S-$USER"/* 2>/dev/null || true
        
        # Check temp directory
        find /var/folders -name "screen" -type d 2>/dev/null | xargs -I{} find {} -type s -delete 2>/dev/null || true
        
        # Clean up potentially stale screen directories
        find /tmp/screens -type d -name "S-*" 2>/dev/null | xargs rm -rf 2>/dev/null || true
    else
        # Linux specific socket cleanup
        screen -wipe >/dev/null 2>&1
        find /tmp -name "screen" -type d 2>/dev/null | xargs -I{} find {} -type s -delete 2>/dev/null || true
        find /var/run/screen -type s -delete 2>/dev/null || true
    fi
    
    # Kill python processes related to trading templates (for both OS types)
    echo "Terminating Python trading processes..."
    pkill -9 -f "[Pp]ython.*template" || true
    
    echo "Cleanup completed."
}

# Health check function
health_check() {
    echo "Running health check..."
    
    # Check for remaining screen sessions
    if screen -ls 2>&1 | grep -q "No Sockets found"; then
        echo "Health check: No screen sessions remain."
    else
        REMAINING_SCREENS=$(screen -ls 2>&1 | grep -E '\s+\([Dd]etached\)|\s+\([Aa]ttached\)')
        if [ -n "$REMAINING_SCREENS" ]; then
            echo "Health check: Found remaining screen sessions:"
            echo "$REMAINING_SCREENS"
        else
            echo "Health check: No screen sessions remain."
        fi
    fi
    
    # Check for remaining screen processes
    if $IS_MACOS; then
        # macOS specific process check
        SCREEN_PROCESSES=$(ps -ef | grep "[s]creen" | awk '{print $2}')
        if [ -n "$SCREEN_PROCESSES" ]; then
            echo "Health check: Screen processes still running on macOS:"
            ps -ef | grep "[s]creen"
            
            # Final attempt to kill any remaining processes
            echo "Attempting final cleanup of screen processes..."
            ps -ef | grep "[s]creen" | awk '{print $2}' | xargs -I{} kill -9 {} 2>/dev/null || true
            pkill -9 SCREEN 2>/dev/null || true
        else
            echo "Health check: No screen processes found on macOS."
        fi
        
        # Check for remaining Python processes on macOS
        PYTHON_PROCESSES=$(ps -ef | grep -E "[p]ython|[P]ython")
        if [ -n "$PYTHON_PROCESSES" ]; then
            echo "Health check: Python processes still running on macOS:"
            echo "$PYTHON_PROCESSES"
            
            # Final attempt to kill any remaining Python processes
            echo "Attempting final cleanup of Python processes..."
            ps -ef | grep -E "[p]ython|[P]ython" | awk '{print $2}' | xargs -I{} kill -9 {} 2>/dev/null || true
            
            if [ $(id -u) -eq 0 ]; then
                echo "Using admin privileges for final Python process cleanup..."
                sudo pkill -9 -f "python" 2>/dev/null || true
                sudo pkill -9 -f "Python" 2>/dev/null || true
            else
                echo "Some Python processes may require admin privileges to kill."
                echo "Consider running this script with sudo if processes remain."
            fi
        else
            echo "Health check: No Python processes found on macOS."
        fi
    else
        # Linux specific process check
        SCREEN_PIDS=$(pgrep -x screen)
        if [ -n "$SCREEN_PIDS" ]; then
            echo "Health check: Screen processes still running on Linux:"
            ps -p $SCREEN_PIDS -o pid,ppid,command
        else
            echo "Health check: No screen processes found on Linux."
        fi
    fi
    
    # Check for Python processes (for both OS types)
    if pgrep -f "[Pp]ython.*template" > /dev/null; then
        echo "Health check: Python trading processes still running:"
        ps aux | grep "[Pp]ython.*template" | grep -v grep
    else
        echo "Health check: No Python trading template processes found."
    fi
}

# Check for admin privileges
check_admin_privileges

# Run cleanup 
cleanup_processes

# Wait a moment for processes to terminate
sleep 2

# Run cleanup again to catch any stragglers
cleanup_processes

# Run health check
health_check

# Final message with guidance
echo "Kill script completed."
if $IS_MACOS && [ $(id -u) -ne 0 ]; then
    echo "Note: If some processes are still running, try running this script with sudo:"
    echo "sudo $0"
fi 