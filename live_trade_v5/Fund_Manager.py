#!/usr/bin/env python3
import os
import shutil
import glob
import pandas as pd
import re
import sys
import subprocess
from pathlib import Path
from openpyxl import load_workbook


def get_fund_files(fund_dir):
    """Get all fund Excel files in the specified directory."""
    # Check if fund directory exists
    if not os.path.exists(fund_dir):
        print(f"Error: Fund directory '{fund_dir}' does not exist.")
        return []
    
    # Get all fund*.xlsx files, but exclude fund_template.xlsx
    all_fund_files = glob.glob(os.path.join(fund_dir, "fund*.xlsx"))
    fund_files = [f for f in all_fund_files if not os.path.basename(f).startswith("fund_template")]
    
    if not fund_files:
        print(f"No valid fund Excel files found in '{fund_dir}'.")
    else:
        print(f"Found {len(fund_files)} valid fund Excel files.")
    
    return fund_files


def remove_existing_fund_folders(script_dir):
    """Remove all existing fund folders in the script directory."""
    # Find all directories that match the pattern 'fund*' but exclude the 'fund' directory itself
    fund_folders = [d for d in glob.glob(os.path.join(script_dir, "fund*")) 
                   if os.path.isdir(d) and os.path.basename(d) != "fund"]
    
    if fund_folders:
        print(f"Found {len(fund_folders)} existing fund folders to remove.")
        for folder in fund_folders:
            print(f"Removing existing fund folder: {folder}")
            shutil.rmtree(folder)
        print("All existing fund folders removed.")
    else:
        print("No existing fund folders found to remove.")


def clone_template(template_dir, target_dir, fund_name):
    """Clone the template directory to a new directory with the fund name."""
    # Check if template directory exists
    if not os.path.exists(template_dir):
        print(f"Error: Template directory '{template_dir}' does not exist.")
        return False
    
    target_path = os.path.join(target_dir, fund_name)
    
    # Remove target directory if it already exists
    if os.path.exists(target_path):
        print(f"Target directory '{target_path}' already exists. Removing...")
        shutil.rmtree(target_path)
    
    # Clone template directory to target directory
    print(f"Cloning template from '{template_dir}' to '{target_path}'...")
    shutil.copytree(template_dir, target_path)
    
    return True


def update_config_summary(fund_file, target_dir, fund_name):
    """Copy fund Excel file to target directory and rename to config_summary.xlsx."""
    target_path = os.path.join(target_dir, fund_name)
    target_file = os.path.join(target_path, "config_summary.xlsx")
    
    # Check if fund file exists
    if not os.path.exists(fund_file):
        print(f"Error: Fund file '{fund_file}' does not exist.")
        return False
    
    # Check if target directory exists
    if not os.path.exists(target_path):
        print(f"Error: Target directory '{target_path}' does not exist.")
        return False
    
    # Copy fund file to target directory and rename to config_summary.xlsx
    print(f"Copying '{fund_file}' to '{target_file}'...")
    shutil.copy2(fund_file, target_file)
    
    return True


def run_strategy_maker(target_dir, fund_name):
    """Run Strategy_maker.py to update config files."""
    target_path = os.path.join(target_dir, fund_name)
    
    # Check if target directory exists
    if not os.path.exists(target_path):
        print(f"Error: Target directory '{target_path}' does not exist.")
        return False
    
    # Skip running Strategy_maker if this is the template folder
    if fund_name == "template":
        print(f"Skipping Strategy_maker.py for template folder")
        return True
    
    # Change to target directory
    original_dir = os.getcwd()
    os.chdir(target_path)
    
    try:
        # Run Strategy_maker.py to update config files
        print(f"Running Strategy_maker.py in '{target_path}'...")
        result = subprocess.run(
            [sys.executable, "Strategy_maker.py", "update_from_excel"],
            capture_output=True, 
            text=True
        )
        
        if result.returncode != 0:
            print(f"Error running Strategy_maker.py: {result.stderr}")
            return False
        
        # Print a truncated version of the output to avoid excessive logs
        output_lines = result.stdout.strip().split('\n')
        if len(output_lines) > 20:
            shortened_output = '\n'.join(output_lines[:10] + ['...'] + output_lines[-10:])
            print(f"Strategy_maker.py output (truncated):\n{shortened_output}")
        else:
            print(f"Strategy_maker.py output:\n{result.stdout}")
        
        return True
    
    finally:
        # Change back to original directory
        os.chdir(original_dir)


def main():
    """Main function to automate fund management."""
    # Get the absolute path of the script's directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Define directories with absolute paths
    fund_dir = os.path.join(script_dir, "fund")
    template_dir = os.path.join(script_dir, "template")
    
    print(f"Script directory: {script_dir}")
    print(f"Fund directory: {fund_dir}")
    print(f"Template directory: {template_dir}")
    
    # Remove all existing fund folders before processing
    remove_existing_fund_folders(script_dir)
    
    # Get all fund Excel files
    fund_files = get_fund_files(fund_dir)
    
    if not fund_files:
        print("No valid fund files found. Exiting.")
        return
    
    # Process each fund file
    for fund_file in fund_files:
        # Extract fund name from file name
        fund_name = os.path.splitext(os.path.basename(fund_file))[0]
        print(f"\nProcessing fund: {fund_name}")
        
        # Clone template directory to under script_dir (live_trade)
        if not clone_template(template_dir, script_dir, fund_name):
            continue
        
        # Update config_summary.xlsx in the new fund directory
        if not update_config_summary(fund_file, script_dir, fund_name):
            continue
        
        # Run Strategy_maker.py to update config files
        if not run_strategy_maker(script_dir, fund_name):
            continue
        
        print(f"Successfully processed fund: {fund_name}")
    
    print("\nFund management automation completed.")


if __name__ == "__main__":
    main() 