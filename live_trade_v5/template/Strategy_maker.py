import os
import yaml
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
import argparse
from datetime import datetime

def load_yaml_files(directory='config', prefix='config_'):
    """Load YAML files from the specified directory that start with the given prefix."""
    # Create directory if it doesn't exist
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"Created directory: {directory}")
        return {}
        
    yaml_files = {}
    for filename in os.listdir(directory):
        if (filename.endswith('.yaml') or filename.endswith('.yml')) and filename.startswith(prefix):
            file_path = os.path.join(directory, filename)
            try:
                with open(file_path, 'r') as file:
                    yaml_content = yaml.safe_load(file)
                    yaml_files[filename] = yaml_content
            except Exception as e:
                print(f"Error loading {filename}: {e}")
    return yaml_files

def extract_strategies(yaml_files):
    """Extract strategies from all YAML files into a DataFrame."""
    all_strategies = []
    
    for filename, content in yaml_files.items():
        asset_name = filename.split('_')[1].split('.')[0] if '_' in filename else 'Unknown'
        
        if 'STRATEGIES' in content:
            for strategy in content['STRATEGIES']:
                strategy_data = {
                    'Asset': asset_name,
                    'Strategy Name': strategy.get('name', ''),
                    'x': strategy.get('x', ''),
                    'y': strategy.get('y', ''),
                    'API': strategy.get('api', ''),
                    'Metric Key': strategy.get('metric_key', ''),
                    'API Symbol': strategy.get('api_symbol', ''),
                    'Resolution': strategy.get('resolution', ''),
                    'Model': strategy.get('model', ''),
                    'Type': strategy.get('type', ''),
                    'Style': strategy.get('style', ''),
                    'Ratio': strategy.get('ratio', '')
                }
                all_strategies.append(strategy_data)
    
    return pd.DataFrame(all_strategies)

def extract_config_settings(yaml_files):
    """Extract general configuration settings from all YAML files."""
    config_data = []
    
    for filename, content in yaml_files.items():
        asset_name = filename.split('_')[1].split('.')[0] if '_' in filename else 'Unknown'
        
        config_row = {
            'Asset': asset_name,
            'Order Type': content.get('ORDER_TYPE', ''),
            'Run Frequency (min)': content.get('RUN_FREQ', ''),
            'Precision': content.get('PRECEISION', ''),
        }
        
        # Extract asset information
        if 'ASSET' in content:
            asset_info = content['ASSET']
            since_val = asset_info.get('since', '')
            if since_val and isinstance(since_val, (int, float)):
                try:
                    since_val = datetime.fromtimestamp(int(since_val)).strftime("%Y%m%d")
                except Exception as e:
                    print(f"Error converting YAML 'Since' value {since_val} to yyyymmdd format: {e}")
            
            # Add Symbol field that can be used for all exchanges
            symbol = asset_info.get('symbol', '')
            
            config_row.update({
                'Symbol': symbol,  # Only use a single Symbol field
                'Glassnode Symbol': asset_info.get('glassnode_symbol', ''),
                'Since': since_val
            })
            
        # Extract position information if available
        if 'POSITION' in content:
            config_row['Max Position'] = content['POSITION'].get('max_pos', '')
            
        # Extract weighting method if available
        if 'WEIGHTING_METHOD' in content:
            config_row['Weighting Method'] = content.get('WEIGHTING_METHOD', '')
            
        config_data.append(config_row)
    
    return pd.DataFrame(config_data)

def format_excel(workbook):
    """Apply formatting to the Excel workbook."""
    for sheet in workbook.sheetnames:
        ws = workbook[sheet]
        
        # Format header row
        header_fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
        header_font = Font(bold=True, color="FFFFFF")
        header_border = Border(
            bottom=Side(style='medium', color='000000')
        )
        
        for cell in ws[1]:
            cell.fill = header_fill
            cell.font = header_font
            cell.border = header_border
            cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # Auto-adjust column width
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                if cell.value:
                    max_length = max(max_length, len(str(cell.value)))
            
            adjusted_width = max_length + 2
            ws.column_dimensions[column_letter].width = adjusted_width
            
        # Add alternating row colors
        for row_idx, row in enumerate(ws.iter_rows(min_row=2), start=2):
            if row_idx % 2 == 0:
                for cell in row:
                    cell.fill = PatternFill(start_color="E9EDF1", end_color="E9EDF1", fill_type="solid")

def yaml_to_excel(output_file='config_summary.xlsx', config_dir='config', prefix='config_'):
    """Convert YAML files starting with the specified prefix to a single Excel file with multiple sheets."""
    yaml_files = load_yaml_files(directory=config_dir, prefix=prefix)
    
    if not yaml_files:
        print(f"No YAML files starting with '{prefix}' found in the '{config_dir}' directory.")
        return
    
    # Create a Pandas Excel writer
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # Extract and write strategies
        strategies_df = extract_strategies(yaml_files)
        strategies_df.to_excel(writer, sheet_name='Strategies', index=False)
        
        # Extract and write general configuration
        config_df = extract_config_settings(yaml_files)
        config_df.to_excel(writer, sheet_name='General Config', index=False)
        
        # Load and process the key.yaml file if it exists
        key_file = os.path.join(config_dir, 'key.yaml')
        if os.path.exists(key_file):
            try:
                with open(key_file, 'r') as file:
                    key_data = yaml.safe_load(file) or {}
                
                # Create a DataFrame for the key data
                key_rows = []
                
                # If it's a flat dictionary, create a single row
                if all(isinstance(key, str) for key in key_data.keys()):
                    # Add all keys except sensitive ones
                    key_row = {}
                    for key, value in key_data.items():
                        # Include sensitive keys (they'll be displayed in Excel)
                        key_row[key] = value
                    key_rows.append(key_row)
                
                # Create the key DataFrame and write to Excel
                if key_rows:
                    key_df = pd.DataFrame(key_rows)
                    
                    # Ensure PASSPHRASE field exists for OKX
                    if 'EXCHANGE' in key_df.columns and key_df['EXCHANGE'].iloc[0] == 'OKX':
                        if 'PASSPHRASE' not in key_df.columns:
                            key_df['PASSPHRASE'] = ''  # Add empty column if not present
                            print("Added PASSPHRASE column for OKX authentication")
                    
                    key_df.to_excel(writer, sheet_name='key', index=False)
                    print("Added 'key' worksheet with API credentials")
            except Exception as e:
                print(f"Error processing key.yaml file: {e}")
                # Create an empty key worksheet with essential fields for OKX
                key_df = pd.DataFrame({
                    'EXCHANGE': ['OKX'],
                    'APIKEY': [''],
                    'SECRET': [''],
                    'PASSPHRASE': [''],
                    'LIVE_ACCOUNT': ['N'],
                })
                key_df.to_excel(writer, sheet_name='key', index=False)
                print("Created empty 'key' worksheet template with OKX authentication fields")
        else:
            # Create an empty key worksheet with essential fields for OKX
            key_df = pd.DataFrame({
                'EXCHANGE': ['OKX'],
                'APIKEY': [''],
                'SECRET': [''],
                'PASSPHRASE': [''],
                'LIVE_ACCOUNT': ['N'],
            })
            key_df.to_excel(writer, sheet_name='key', index=False)
            print("Created empty 'key' worksheet template with OKX authentication fields")
        
        # Get the workbook to apply formatting
        workbook = writer.book
        format_excel(workbook)
    
    print(f"Excel file created successfully: {output_file}")
    print(f"Processed {len(yaml_files)} configuration files from '{config_dir}' directory.")

def excel_to_yaml(input_file='config_summary.xlsx', output_dir='config', prefix='config_'):
    """Convert Excel file back to YAML files.
    
    This function reads the Excel file created by yaml_to_excel() and updates
    or creates corresponding YAML files based on the data in the Excel sheets.
    Only processes files that start with the specified prefix and saves them to the specified directory.
    """
    # Get the directory where this script is located
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Convert relative paths to absolute paths if needed
    if not os.path.isabs(input_file):
        input_file = os.path.join(script_dir, input_file)
    
    if not os.path.isabs(output_dir):
        output_dir = os.path.join(script_dir, output_dir)
    
    if not os.path.exists(input_file):
        print(f"Error: Excel file '{input_file}' not found.")
        return
    
    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created directory: {output_dir}")
    
    # Load existing YAML files to preserve structure and comments
    existing_yaml_files = load_yaml_files(directory=output_dir, prefix=prefix)
    
    # Read Excel sheets
    config_df = pd.read_excel(input_file, sheet_name='General Config')
    # Read strategies sheet with strings preserved (don't convert percentages)
    strategies_df = pd.read_excel(input_file, sheet_name='Strategies', converters={'Metric Key': str})
    
    # Group strategies by asset
    strategies_by_asset = {}
    for _, row in strategies_df.iterrows():
        asset = row['Asset']
        if asset not in strategies_by_asset:
            strategies_by_asset[asset] = []
        
        # Create strategy dict from row
        strategy = {
            'name': row['Strategy Name'],
            'x': row['x'],
            'y': row['y'],
            'api': row['API'],
            'metric_key': row['Metric Key'],  # This will now preserve the original format (e.g. "15%")
            'api_symbol': row['API Symbol'],
            'resolution': row['Resolution'],
            'model': row['Model'],
            'type': row['Type']
        }
        
        # Add style if it exists
        if 'Style' in row and pd.notna(row['Style']):
            strategy['style'] = row['Style']
        
        # Add Ratio if it exists
        if 'Ratio' in row and pd.notna(row['Ratio']):
            strategy['ratio'] = row['Ratio']
        
        # Add symbol if it exists in the original strategy
        if 'symbol' in row and pd.notna(row['symbol']):
            strategy['symbol'] = row['symbol']
        else:
            # Default to using the asset as the symbol if not specified
            strategy['symbol'] = asset
            
        strategies_by_asset[asset].append(strategy)
    
    # Process each asset in the config sheet
    for _, row in config_df.iterrows():
        asset = row['Asset']
        filename = f"{prefix}{asset}.yaml"
        
        # Create new YAML content or update existing
        if filename in existing_yaml_files:
            yaml_content = existing_yaml_files[filename].copy()
        else:
            yaml_content = {}
        
        # Update general configuration
        if pd.notna(row.get('Order Type')):
            yaml_content['ORDER_TYPE'] = row['Order Type']
            
        if pd.notna(row.get('Run Frequency (min)')):
            yaml_content['RUN_FREQ'] = int(row['Run Frequency (min)'])
            
        if pd.notna(row.get('Precision')):
            yaml_content['PRECISION'] = row['Precision']

        # Update asset information
        if 'ASSET' not in yaml_content:
            yaml_content['ASSET'] = {}
            
        asset_fields = {
            'Symbol': 'symbol',  # Only use a single unified Symbol field
            'Glassnode Symbol': 'glassnode_symbol',
            'Since': 'since'
        }
        
        for excel_field, yaml_field in asset_fields.items():
            if excel_field in row and pd.notna(row[excel_field]):
                value = row[excel_field]
                # Convert "Since" field from YYYYMMDD format to unix timestamp
                if yaml_field == 'since':
                    try:
                        # Ensure the value is treated as a string in YYYYMMDD format
                        if isinstance(value, (int, float)):
                            value_str = str(int(value))
                        else:
                            value_str = str(value)
                        dt = datetime.strptime(value_str, "%Y%m%d")
                        value = int(dt.timestamp())
                    except Exception as e:
                        print(f"Error converting {excel_field} value {value} to unix timestamp: {e}")
                
                yaml_content['ASSET'][yaml_field] = value
        
        # Update strategies
        if asset in strategies_by_asset:
            yaml_content['STRATEGIES'] = strategies_by_asset[asset]
        
        # Write updated YAML file
        output_path = os.path.join(output_dir, filename)
        with open(output_path, 'w') as file:
            # Add a comment at the top of the file
            file.write("# Configuration file generated/updated by Strategy_maker.py\n\n")
            yaml.dump(yaml_content, file, default_flow_style=False, sort_keys=False)
        
        print(f"Updated YAML file: {output_path}")

def preserve_yaml_format(input_file, output_file, new_data):
    """Helper function to preserve comments and formatting in YAML files."""
    # This is a placeholder for a more sophisticated implementation
    # that would preserve comments and formatting
    # For now, we'll just write the new data
    with open(output_file, 'w') as file:
        file.write("# Configuration file updated by Strategy_maker.py\n\n")
        yaml.dump(new_data, file, default_flow_style=False, sort_keys=False)

# New function to clone from Trade_BTC.py to other asset-specific trade scripts.
def clone_trade_script(excel_file='config_summary.xlsx', source_trade_script='Trade_BTC.py'):
    """Clone Trade_BTC.py to asset-specific trade scripts based on assets in the Excel file.

    This function reads the 'General Config' sheet from the Excel file and,
    for each asset (other than 'BTC') found in the Asset column, it creates
    a new file by replacing all occurrences of 'BTC' in the source file with the new asset.
    The new file is named by replacing 'BTC' in the source file's name with the asset.
    """
    # Get the directory where this script is located
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Convert relative paths to absolute paths if needed
    if not os.path.isabs(excel_file):
        excel_file = os.path.join(script_dir, excel_file)
        
    # For source_trade_script, first check if it exists in the script directory
    if not os.path.isabs(source_trade_script):
        # Check in script directory
        script_dir_path = os.path.join(script_dir, source_trade_script)
        # Check in parent directory (live_trade)
        parent_dir_path = os.path.join(os.path.dirname(script_dir), source_trade_script)
        
        if os.path.exists(script_dir_path):
            source_trade_script = script_dir_path
        elif os.path.exists(parent_dir_path):
            source_trade_script = parent_dir_path
    
    if not os.path.exists(excel_file):
        print(f"Error: Excel file '{excel_file}' not found.")
        return
    if not os.path.exists(source_trade_script):
        print(f"Error: Trade script '{source_trade_script}' not found.")
        return

    try:
        config_df = pd.read_excel(excel_file, sheet_name='General Config')
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return

    # Read the source Trade_BTC.py content
    with open(source_trade_script, 'r', encoding='utf-8') as f:
        source_content = f.read()

    for _, row in config_df.iterrows():
        asset = row['Asset']
        if asset == "BTC":
            continue  # Skip cloning for BTC since it's the source file
        # Replace "BTC" with the new asset in the contents and file name
        new_file_name = os.path.join(os.path.dirname(source_trade_script), source_trade_script.replace("BTC", asset))
        new_content = source_content.replace("BTC", asset)
        with open(new_file_name, 'w', encoding='utf-8') as nf:
            nf.write(new_content)
        print(f"Created trade script: {new_file_name}")

# New function to update run.sh with new screen commands for new assets.
def update_run_sh(excel_file='config_summary.xlsx', run_sh_path='run.sh', source_trade_script='Trade_BTC.py'):
    """Update run.sh with new screen commands based on assets in the Excel file.
    
    This function reads the 'General Config' sheet from the Excel file and updates the run.sh file by
    adding new screen commands copied from the BTC template, for each asset (other than BTC) that
    is not already present in run.sh.
    """
    if not os.path.exists(excel_file):
        print(f"Error: Excel file '{excel_file}' not found.")
        return
    try:
        config_df = pd.read_excel(excel_file, sheet_name='General Config')
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return
    if not os.path.exists(run_sh_path):
        print(f"Error: run.sh file '{run_sh_path}' not found.")
        return
    
    with open(run_sh_path, 'r') as f:
        lines = f.readlines()
    
    def create_screen_command(asset):
        asset_lower = asset.lower()
        return f"screen -dmS {asset_lower} -L -Logfile log/{asset_lower}.log python3 Trade_{asset}.py\n"
    
    existing_screens = set()
    for line in lines:
        if line.strip().startswith("screen -dmS"):
            parts = line.split()
            if len(parts) >= 3:
                existing_screens.add(parts[2].lower())
    
    new_commands = []
    for _, row in config_df.iterrows():
        asset = str(row['Asset']).strip()
        if asset.upper() == "BTC":
            continue
        asset_lower = asset.lower()
        if asset_lower not in existing_screens:
            new_commands.append(create_screen_command(asset))
    
    if new_commands:
        new_lines = []
        inserted = False
        for line in lines:
            if not inserted and line.strip().startswith("screen -ls"):
                new_lines.extend(new_commands)
                inserted = True
            new_lines.append(line)
        if not inserted:
            new_lines.extend(new_commands)
        with open(run_sh_path, 'w') as f:
            f.writelines(new_lines)
        print(f"Updated {run_sh_path} with new screen commands for assets: {', '.join([cmd.strip() for cmd in new_commands])}")
    else:
        print("No new assets found to update run.sh.")

def update_key_yaml(excel_file='config_summary.xlsx', key_file='config/key.yaml'):
    """Update the key.yaml file based on the 'key' worksheet in the Excel file.
    
    This function reads the 'key' worksheet from the Excel file and updates 
    the key.yaml file with all fields found in the Excel.
    """
    # Get the directory where this script is located
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Convert relative paths to absolute paths if needed
    if not os.path.isabs(excel_file):
        excel_file = os.path.join(script_dir, excel_file)
        
    if not os.path.isabs(key_file):
        key_file = os.path.join(script_dir, key_file)
        
    if not os.path.exists(excel_file):
        print(f"Error: Excel file '{excel_file}' not found.")
        return
        
    # Check if the Key worksheet exists in the Excel file
    try:
        key_df = pd.read_excel(excel_file, sheet_name='key')
    except Exception as e:
        print(f"Error reading 'key' worksheet from Excel file: {e}")
        return
    
    # Create the directory for key.yaml if it doesn't exist
    key_dir = os.path.dirname(key_file)
    if not os.path.exists(key_dir):
        os.makedirs(key_dir)
        print(f"Created directory: {key_dir}")
    
    # Load existing key.yaml file if it exists
    existing_keys = {}
    if os.path.exists(key_file):
        try:
            with open(key_file, 'r') as file:
                existing_keys = yaml.safe_load(file) or {}
            print(f"Loaded existing key file: {key_file}")
        except Exception as e:
            print(f"Error loading existing key file: {e}")
    else:
        print(f"Key file not found. Will create a new one at: {key_file}")
    
    # Check if the key dataframe has any data
    if key_df.empty:
        print("Warning: Key worksheet is empty. Creating an empty key file.")
        # Still proceed to create an empty file
        keys_to_save = {}
    else:
        # Check for OKX exchange entry in the dataframe
        has_okx = False
        passphrase_column = None
        
        # Check for EXCHANGE column with OKX value and PASSPHRASE column
        if 'EXCHANGE' in key_df.columns:
            has_okx = 'OKX' in key_df['EXCHANGE'].values
        
        # Look for PASSPHRASE column (case-insensitive)
        for col in key_df.columns:
            if col.upper() == 'PASSPHRASE':
                passphrase_column = col
                break
            # Also check for PASSWORD for backward compatibility
            elif col.upper() == 'PASSWORD':
                passphrase_column = col
                print(f"Found '{col}' column, will be used as PASSPHRASE for OKX")
                break
        
        # Check the structure of the key dataframe
        # If the dataframe has a 'Service' column, use the service-based structure
        if 'Service' in key_df.columns:
            # Convert the key dataframe to a dictionary - service-based structure
            keys_dict = {}
            for _, row in key_df.iterrows():
                # Only process rows with valid Service values
                if pd.isna(row.get('Service')) or str(row['Service']).strip() == '':
                    continue
                    
                service = str(row['Service']).strip()
                
                # Skip if service name is empty
                if not service:
                    continue
                    
                # Initialize the service dict if not already present
                if service not in keys_dict:
                    keys_dict[service] = {}
                
                # Special handling for OKX exchange and PASSPHRASE
                if service.upper() == 'OKX' and passphrase_column and passphrase_column in row:
                    keys_dict[service]['PASSPHRASE'] = str(row[passphrase_column])
                    
                # Process each column in the row
                for col in key_df.columns:
                    if col != 'Service' and col != passphrase_column:
                        # Only add non-null, non-NaN values
                        if not pd.isna(row.get(col)) and row.get(col) is not None:
                            # Convert to string to handle numeric values properly
                            keys_dict[service][col] = str(row[col])
        else:
            # Flat structure - assume each row is a configuration entry
            # and each column is a key
            keys_dict = {}
            for _, row in key_df.iterrows():
                for col in key_df.columns:
                    # Special handling for PASSPHRASE column if OKX exchange is present
                    if has_okx and col == passphrase_column:
                        # Make sure it's stored as PASSPHRASE (not PASSWORD)
                        if not pd.isna(row.get(col)) and row.get(col) is not None:
                            keys_dict['PASSPHRASE'] = str(row[col])
                    else:
                        # Only add non-null, non-NaN values
                        if not pd.isna(row.get(col)) and row.get(col) is not None:
                            # Convert to appropriate type
                            value = row[col]
                            if isinstance(value, (int, float)):
                                # For integers/floats, preserve the numeric type
                                keys_dict[col] = value
                            else:
                                # For other types, convert to string
                                keys_dict[col] = str(value)
        
        # Handle existing passphrase
        if existing_keys and 'PASSPHRASE' in existing_keys and 'PASSPHRASE' not in keys_dict:
            # If no new passphrase from Excel but an existing one is present, preserve it
            print("Preserving existing PASSPHRASE in key.yaml")
            keys_dict['PASSPHRASE'] = existing_keys['PASSPHRASE']
        
        # Update the existing keys with the new ones
        if existing_keys and isinstance(existing_keys, dict):
            if all(isinstance(key, str) for key in existing_keys.keys()):
                # If existing_keys is a flat dictionary, merge with keys_dict
                existing_keys.update(keys_dict)
                keys_to_save = existing_keys
            else:
                # If existing_keys has a nested structure, preserve it
                for key, value in keys_dict.items():
                    existing_keys[key] = value
                keys_to_save = existing_keys
        else:
            keys_to_save = keys_dict
        
        # Remove any empty dictionaries to clean up the YAML
        if isinstance(keys_to_save, dict):
            keys_to_save = {k: v for k, v in keys_to_save.items() if v or v == 0}
    
    # Check if we have OKX as exchange but no PASSPHRASE
    if keys_to_save.get('EXCHANGE') == 'OKX' and 'PASSPHRASE' not in keys_to_save:
        print("WARNING: OKX exchange specified but no PASSPHRASE found. OKX requires a passphrase for authenticated API calls.")
    
    # Write the keys to the key.yaml file (whether empty or with content)
    try:
        with open(key_file, 'w') as file:
            file.write("# API keys configuration file updated by Strategy_maker.py\n\n")
            yaml.dump(keys_to_save, file, default_flow_style=False, sort_keys=False)
        if keys_to_save:
            print(f"Updated key file: {key_file}")
        else:
            print(f"Created empty key file: {key_file}")
    except Exception as e:
        print(f"Error writing to key file: {e}")

if __name__ == "__main__":
    print("\nRunning Strategy Maker:")
    print("-------------------------")
    print(f"Current working directory: {os.getcwd()}")
    
    # Get the directory where this script is located
    script_dir = os.path.dirname(os.path.abspath(__file__))
    excel_file_path = os.path.join(script_dir, 'config_summary.xlsx')
    
    print(f"Looking for config_summary.xlsx in: {excel_file_path}")
    
    print("Step 1: Converting Excel to YAML files...")
    excel_to_yaml(input_file=excel_file_path, output_dir=os.path.join(script_dir, 'config'))
    
    # Look for Trade_BTC.py in both the script directory and its parent directory
    btc_script_in_script_dir = os.path.join(script_dir, 'Trade_BTC.py')
    btc_script_in_parent_dir = os.path.join(os.path.dirname(script_dir), 'Trade_BTC.py')
    
    btc_script_path = None
    if os.path.exists(btc_script_in_script_dir):
        btc_script_path = btc_script_in_script_dir
    elif os.path.exists(btc_script_in_parent_dir):
        btc_script_path = btc_script_in_parent_dir
    
    if btc_script_path:
        print("\nStep 2: Cloning Trade_BTC.py to other Assets...")
        clone_trade_script(excel_file=excel_file_path, source_trade_script=btc_script_path)
    else:
        print("\nStep 2: Skipping cloning (Trade_BTC.py not found)")
        
    print("\nStep 3: Updating key.yaml file...")
    key_file_path = os.path.join(script_dir, 'config', 'key.yaml')
    update_key_yaml(excel_file=excel_file_path, key_file=key_file_path)
    
    print("\nAll operations completed successfully.")
    print("Exiting program.")