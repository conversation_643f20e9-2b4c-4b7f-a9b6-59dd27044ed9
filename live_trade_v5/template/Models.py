import numpy as np
import pandas as pd
from scipy import stats

class Strategy:
    @staticmethod
    def ma_diff(df, window, threshold, strategy_type, style):
        ma = df['value'].rolling(window).mean()
        if style == 'reversion':
            if strategy_type == 'long_only':
                return np.where(df['value'] < ma * (1 - threshold), 1, 0)
            elif strategy_type == 'short_only':
                return np.where(df['value'] > ma * (1 + threshold), -1, 0)
            else:  # long_short
                return np.where(df['value'] < ma * (1 - threshold), 1,
                                np.where(df['value'] > ma * (1 + threshold), -1, 0))
        else:  # momentum
            if strategy_type == 'long_only':
                return np.where(df['value'] > ma * (1 + threshold), 1, 0)
            elif strategy_type == 'short_only':
                return np.where(df['value'] < ma * (1 - threshold), -1, 0)
            else:  # long_short
                return np.where(df['value'] > ma * (1 + threshold), 1,
                                np.where(df['value'] < ma * (1 - threshold), -1, 0))

    @staticmethod
    def z_score(df, window, threshold, strategy_type, style):
        roll = df['value'].rolling(window)
        z = (df['value'] - roll.mean()) / roll.std()
        if style == 'reversion':
            # Reversion: z-score 極值預期回歸
            if strategy_type == 'long_only':
                return np.where(z < -threshold, 1, 0)
            elif strategy_type == 'short_only':
                return np.where(z > threshold, -1, 0)
            else:  # long_short
                return np.where(z < -threshold, 1,
                                np.where(z > threshold, -1, 0))
        else:
            # Momentum: 跟隨 z-score 趨勢
            if strategy_type == 'long_only':
                return np.where(z > threshold, 1, 0)
            elif strategy_type == 'short_only':
                return np.where(z < -threshold, -1, 0)
            else:  # long_short
                return np.where(z > threshold, 1,
                                np.where(z < -threshold, -1, 0))

    @staticmethod
    def rsi(df, window, threshold, strategy_type, style):
        delta = df['value'].diff()
        gain = delta.clip(lower=0)
        loss = -delta.clip(upper=0)

        avg_gain = gain.rolling(window).mean()
        avg_loss = loss.rolling(window).mean()
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))

        if style == 'reversion':
            # Reversion: RSI高做空，低做多
            if strategy_type == 'long_only':
                return np.where(rsi < threshold, 1, 0)
            elif strategy_type == 'short_only':
                return np.where(rsi > (100 - threshold), -1, 0)
            else:  # long_short
                return np.where(rsi < threshold, 1,
                                np.where(rsi > (100 - threshold), -1, 0))
        else:
            # Momentum: RSI高做多，低做空
            if strategy_type == 'long_only':
                return np.where(rsi > threshold, 1, 0)
            elif strategy_type == 'short_only':
                return np.where(rsi < (100 - threshold), -1, 0)
            else:  # long_short
                return np.where(rsi > threshold, 1,
                                np.where(rsi < (100 - threshold), -1, 0))

    @staticmethod
    def minmax(df, window, threshold, strategy_type, style):
        roll = df['value'].rolling(window)
        normalized = (df['value'] - roll.min()) / (roll.max() - roll.min())

        if style == 'reversion':
            # Reversion: 價格高做空，低做多
            if strategy_type == 'long_only':
                return np.where(normalized < threshold, 1, 0)
            elif strategy_type == 'short_only':
                return np.where(normalized > (1 - threshold), -1, 0)
            else:  # long_short
                return np.where(normalized < threshold, 1,
                                np.where(normalized > (1 - threshold), -1, 0))
        else:
            # Momentum: 價格高做多，低做空
            if strategy_type == 'long_only':
                return np.where(normalized > threshold, 1, 0)
            elif strategy_type == 'short_only':
                return np.where(normalized < (1 - threshold), -1, 0)
            else:  # long_short
                return np.where(normalized > threshold, 1,
                                np.where(normalized < (1 - threshold), -1, 0))

    @staticmethod
    def robust_scaling(df, window, threshold, strategy_type, style):
        roll = df['value'].rolling(window)
        median = roll.median()
        q75 = roll.quantile(0.75)
        q25 = roll.quantile(0.25)
        iqr = q75 - q25

        scaled = (df['value'] - median) / (iqr + 1e-10)

        if style == 'reversion':
            # Reversion: 極值回歸
            if strategy_type == 'long_only':
                return np.where(scaled < -threshold, 1, 0)
            elif strategy_type == 'short_only':
                return np.where(scaled > threshold, -1, 0)
            else:  # long_short
                return np.where(scaled < -threshold, 1,
                                np.where(scaled > threshold, -1, 0))
        else:
            # Momentum: 跟隨趨勢
            if strategy_type == 'long_only':
                return np.where(scaled > threshold, 1, 0)
            elif strategy_type == 'short_only':
                return np.where(scaled < -threshold, -1, 0)
            else:  # long_short
                return np.where(scaled > threshold, 1,
                                np.where(scaled < -threshold, -1, 0))

    @staticmethod
    def ma_cross(df, fast_window, slow_window, strategy_type, style):
        fast_ma = df['value'].rolling(int(fast_window)).mean()
        slow_ma = df['value'].rolling(int(slow_window)).mean()

        if style == 'reversion':
            # Reversion: 快線低過慢線做多，高過慢線做空
            if strategy_type == 'long_only':
                return np.where(fast_ma < slow_ma, 1, 0)
            elif strategy_type == 'short_only':
                return np.where(fast_ma > slow_ma, -1, 0)
            else:  # long_short
                return np.where(fast_ma < slow_ma, 1, -1)
        else:
            # Momentum: 快線高過慢線做多，低過慢線做空
            if strategy_type == 'long_only':
                return np.where(fast_ma > slow_ma, 1, 0)
            elif strategy_type == 'short_only':
                return np.where(fast_ma < slow_ma, -1, 0)
            else:  # long_short
                return np.where(fast_ma > slow_ma, 1, -1)

    @staticmethod
    def box_cox(df, window, threshold, strategy_type, style):
        """
        使用固定 lambda 值進行真正嘅 Box-Cox 轉換
        完全向量化，無迴圈，無警告
        """
        # 確保數據為正數
        values = df['value'].copy()
        min_val = values.min()
        if min_val <= 0:
            values = values - min_val + 1e-3
        
        # 使用固定 lambda 值進行 Box-Cox 轉換
        # 常用嘅 lambda 值範圍通常係 0.1 到 0.5 之間
        # 呢度選擇 0.3 作為一個平衡值，可以根據數據特性調整
        lambda_value = 0.3
        
        # 應用 Box-Cox 轉換公式: (x^lambda - 1) / lambda
        transformed = (values ** lambda_value - 1) / lambda_value
        
        # 計算滾動窗口內嘅均值同標準差
        roll_mean = transformed.rolling(window).mean()
        roll_std = transformed.rolling(window).std()
        
        # 計算 z-score
        z = (transformed - roll_mean) / (roll_std + 1e-10)
        
        # 根據策略類型同風格生成信號
        if style == 'reversion':
            # Reversion: z-score 極值預期回歸
            if strategy_type == 'long_only':
                return np.where(z < -threshold, 1, 0)
            elif strategy_type == 'short_only':
                return np.where(z > threshold, -1, 0)
            else:  # long_short
                return np.where(z < -threshold, 1,
                                np.where(z > threshold, -1, 0))
        else:
            # Momentum: 跟隨 z-score 趨勢
            if strategy_type == 'long_only':
                return np.where(z > threshold, 1, 0)
            elif strategy_type == 'short_only':
                return np.where(z < -threshold, -1, 0)
            else:  # long_short
                return np.where(z > threshold, 1,
                                np.where(z < -threshold, -1, 0))

    @staticmethod
    def rate_of_change(df, window, threshold, strategy_type, style):
        # 計算 Rate of Change (ROC)
        # ROC = [(當前值 - n期前嘅值) / n期前嘅值] × 100
        roc = df['value'].pct_change(periods=window) * 100
        
        if style == 'reversion':
            # Reversion: ROC 極值預期回歸
            # 當 ROC 過高，預期回落，做空；當 ROC 過低，預期回升，做多
            if strategy_type == 'long_only':
                return np.where(roc < -threshold, 1, 0)
            elif strategy_type == 'short_only':
                return np.where(roc > threshold, -1, 0)
            else:  # long_short
                return np.where(roc < -threshold, 1,
                                np.where(roc > threshold, -1, 0))
        else:
            # Momentum: 跟隨 ROC 趨勢
            # 當 ROC 為正且高於閾值，表示上升趨勢強勁，做多
            # 當 ROC 為負且低於閾值，表示下降趨勢強勁，做空
            if strategy_type == 'long_only':
                return np.where(roc > threshold, 1, 0)
            elif strategy_type == 'short_only':
                return np.where(roc < -threshold, -1, 0)
            else:  # long_short
                return np.where(roc > threshold, 1,
                                np.where(roc < -threshold, -1, 0))

    @staticmethod
    def divergence(df, window, threshold, strategy_type, style):
        # 計算價格同指標嘅 Z-Score
        price_ma = df['price'].rolling(window).mean()
        price_sd = df['price'].rolling(window).std()
        price_z = (df['price'] - price_ma) / (price_sd + 1e-10)
        
        indicator_ma = df['value'].rolling(window).mean()
        indicator_sd = df['value'].rolling(window).std()
        indicator_z = (df['value'] - indicator_ma) / (indicator_sd + 1e-10)
        
        # 計算 Z-Score 差異作為背離指標
        divergence = price_z - indicator_z
        
        if style == 'reversion':
            # Reversion: 當背離過大時，預期價格會向指標方向回歸
            if strategy_type == 'long_only':
                # 當價格 Z-Score 低於指標 Z-Score 一定閾值，做多
                return np.where(divergence < -threshold, 1, 0)
            elif strategy_type == 'short_only':
                # 當價格 Z-Score 高於指標 Z-Score 一定閾值，做空
                return np.where(divergence > threshold, -1, 0)
            else:  # long_short
                return np.where(divergence < -threshold, 1,
                               np.where(divergence > threshold, -1, 0))
        else:  # momentum
            # Momentum: 當背離開始收斂時，跟隨趨勢
            # 計算背離嘅變化率
            divergence_change = divergence - divergence.shift(1)
            
            if strategy_type == 'long_only':
                # 當背離由負轉正（收斂），做多
                return np.where((divergence < 0) & (divergence_change > threshold), 1, 0)
            elif strategy_type == 'short_only':
                # 當背離由正轉負（收斂），做空
                return np.where((divergence > 0) & (divergence_change < -threshold), -1, 0)
            else:  # long_short
                return np.where((divergence < 0) & (divergence_change > threshold), 1,
                               np.where((divergence > 0) & (divergence_change < -threshold), -1, 0))

# 定義模型參數
def get_base_params():
    return {
        'MA Diff': {
            'func': Strategy.ma_diff,
            'base_window': (10, 200, 10),  # 以日為單位嘅基準範圍
            'threshold_range': np.arange(0, 0.4, 0.02)
        },
        'Z-Score': {
            'func': Strategy.z_score,
            'base_window': (10, 200, 10),
            'threshold_range': np.arange(0, 2.6, 0.25)
        },
        'RSI': {
            'func': Strategy.rsi,
            'base_window': (10, 200, 10),
            'threshold_range': np.arange(20, 90, 10)
        },
        'MinMax': {
            'func': Strategy.minmax,
            'base_window': (10, 200, 10),
            'threshold_range': np.arange(0.05, 0.96, 0.05)
        },
        'Robust Scaling': {
            'func': Strategy.robust_scaling,
            'base_window': (10, 200, 10),
            'threshold_range': np.arange(0, 2.6, 0.25)
        },
        'MA Cross': {
            'func': Strategy.ma_cross,
            'base_window': (10, 100, 10),  # 快線範圍 (fast_window)
            'base_threshold': (100, 300, 20)  # 慢線範圍 (slow_window)
        },
        'Box-Cox': {
            'func': Strategy.box_cox,
            'base_window': (10, 200, 10),
            'threshold_range': np.arange(0, 2.6, 0.25)
        },
        'Rate of Change': {
            'func': Strategy.rate_of_change,
            'base_window': (5, 100, 5),  # 較短嘅窗口範圍，因為 ROC 通常用較短嘅時間段
            'threshold_range': np.arange(1, 20, 1)  # ROC 嘅閾值通常以百分比表示
        },
        'Divergence': {
            'func': Strategy.divergence,
            'base_window': (10, 200, 10),
            'threshold_range': np.arange(0, 2.6, 0.25)
        }
    } 