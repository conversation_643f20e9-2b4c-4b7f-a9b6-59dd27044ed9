#!/bin/bash

# Detect operating system
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "Detected Linux system..."
    USE_SCREEN=true
elif [[ "$OSTYPE" == "darwin"* ]]; then
    echo "Detected macOS system..."
    # Check if screen is installed on macOS
    if ! command -v screen &> /dev/null; then
        echo "Screen is not installed. Please install it using Homebrew with: brew install screen"
        exit 1
    fi
    USE_SCREEN=true
else
    echo "Unsupported operating system: $OSTYPE"
    echo "This script only supports Linux and macOS."
    exit 1
fi

# Set Python command
PYTHON_CMD="python3 -u"

# Use the existing kill.sh script to clean up processes
cleanup_processes() {
    echo "Cleaning up existing trading processes..."
    # Call the existing kill.sh script in the same directory
    bash "$(dirname "$0")/kill.sh"
}

# Create log directory if it doesn't exist
mkdir -p log

# Run cleanup before starting
cleanup_processes

# Activate virtual environment
source venv/bin/activate 2>/dev/null || echo "Virtual environment not found. Continuing without activation."

# Step 1: Find and run Rebalance.py first
echo "====== STEP 1: Starting Rebalance.py first ======"
rebalance_files=$(find . -type f -name "Rebalance.py" -not -path "./venv/*" -not -path "*template*")

if [ -z "$rebalance_files" ]; then
    echo "No Rebalance.py file found. Exiting."
    exit 1
fi

for rebalance_script in $rebalance_files; do
    # Extract script name without path and extension
    script_name=$(basename "$rebalance_script" .py)
    
    # Create screen name with directory prefix
    dir_name=$(dirname "$rebalance_script" | sed 's/^\.\///' | tr '/' '_')
    screen_name="${dir_name}_${script_name}"
    log_file="log/${screen_name}.log"
    
    echo "Starting $screen_name..."
    
    # Start the Rebalance.py script in a new screen session with logging
    screen -dmS "$screen_name" bash -c "$PYTHON_CMD $rebalance_script 2>&1 | tee -a $log_file"
    
    echo "Started $screen_name"
done

# Wait for Rebalance.py to initialize
echo "Waiting for Rebalance.py to initialize (5 seconds)..."
sleep 5

# Step 2: Run all Trade_XXX.py files
echo "====== STEP 2: Starting all Trade_XXX.py files ======"

# Find and run all Trade_XXX.py scripts in all subdirectories using screen
find . -type f -name "Trade_*.py" -not -path "./venv/*" -not -path "*template*" | while read script; do
    # Skip scripts in template folders
    if [[ "$(dirname "$script")" == *"template"* ]]; then
        echo "Skipping script in template folder: $script"
        continue
    fi
    
    # Skip scripts with "backtesting" in their path or name
    if [[ "$script" == *"backtesting"* ]]; then
        echo "Skipping backtesting script: $script"
        continue
    fi
    
    # Extract script name without path and extension
    script_name=$(basename "$script" .py)
    
    # Create screen name with directory prefix
    dir_name=$(dirname "$script" | sed 's/^\.\///' | tr '/' '_')
    screen_name="${dir_name}_${script_name}"
    log_file="log/${screen_name}.log"
    
    # Start the Python script in a new screen session with logging
    screen -dmS "$screen_name" bash -c "$PYTHON_CMD $script 2>&1 | tee -a $log_file"
    
    echo "Started $screen_name"
    
    # Small sleep to prevent overloading the system with simultaneous starts
    sleep 1
done

# Step 3: Run all other Python scripts that are not Rebalance.py or Trade_XXX.py
echo "====== STEP 3: Starting all other Python scripts ======"

find . -type f -name "*.py" -not -path "./venv/*" -not -name "Rebalance.py" -not -name "Trade_*.py" | while read script; do
    # Skip the script if it's in the current directory
    if [[ $(dirname "$script") == "." ]]; then
        continue
    fi
    
    # Skip scripts in template folders
    if [[ "$(dirname "$script")" == *"template"* ]]; then
        echo "Skipping script in template folder: $script"
        continue
    fi
    
    # Skip scripts with "backtesting" in their path or name
    if [[ "$script" == *"backtesting"* ]]; then
        echo "Skipping backtesting script: $script"
        continue
    fi
    
    # Extract script name without path and extension
    script_name=$(basename "$script" .py)
    
    # Skip excluded files
    if [[ "$script_name" == "Models" || "$script_name" == "Strategy_maker" ]]; then
        echo "Skipping $script_name"
        continue
    fi
    
    # Create screen name with directory prefix
    dir_name=$(dirname "$script" | sed 's/^\.\///' | tr '/' '_')
    screen_name="${dir_name}_${script_name}"
    log_file="log/${screen_name}.log"
    
    # Start the Python script in a new screen session with logging
    screen -dmS "$screen_name" bash -c "$PYTHON_CMD $script 2>&1 | tee -a $log_file"
    
    echo "Started $screen_name"
done

# Display all running screen sessions
echo "====== All processes started ======"
screen -ls

echo "Startup sequence completed:"
echo "1. Rebalance.py started first and given time to initialize"
echo "2. All Trade_XXX.py scripts started next"
echo "3. All other Python scripts started last"
echo "To check status, use 'screen -ls' command."
echo "To attach to a screen session, use 'screen -r [session_name]'"