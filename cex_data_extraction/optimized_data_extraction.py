import os
import sys
import pandas as pd
import time
import datetime
import yaml
import ccxt

# Import the necessary modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from bybit_direct_api import BybitDirectAPI

# Set timestamp for January 1, 2022
TIMESTAMP_2022 = 1640995200  # 2022-01-01 00:00:00
CURRENT_TIME = int(time.time())

# Print the date range
start_date = datetime.datetime.fromtimestamp(TIMESTAMP_2022).strftime('%Y-%m-%d')
end_date = datetime.datetime.fromtimestamp(CURRENT_TIME).strftime('%Y-%m-%d')
print(f"Running optimized BTC data extraction from {start_date} to {end_date}")

# Set common parameters
SYMBOL = "BTCUSDT"
RESOLUTION = "1h"
UNDERLYING = "BTC"

# Add global credential variables at the top of the file
_API_KEY = ""
_API_SECRET = ""
_TESTNET = True

def initialize_credentials(api_key, api_secret, testnet=True):
    """Initialize credentials for API access without using key.yaml
    
    Args:
        api_key (str): The API key for Bybit
        api_secret (str): The API secret for Bybit
        testnet (bool): Whether to use testnet (default: True)
    """
    global _API_KEY, _API_SECRET, _TESTNET
    _API_KEY = api_key
    _API_SECRET = api_secret
    _TESTNET = testnet
    print(f"Optimized extractor credentials initialized (testnet: {_TESTNET})")

class OptimizedExtractor(BybitDirectAPI):
    """Enhanced data extraction with optimal performance settings"""
    
    def __init__(self, api_key=None, api_secret=None, testnet=True):
        """
        Initialize the OptimizedExtractor.
        
        Args:
            api_key (str, optional): The API key for Bybit. If None, will try to load from global or key.yaml
            api_secret (str, optional): The API secret for Bybit. If None, will try to load from global or key.yaml
            testnet (bool, optional): Whether to use testnet. Defaults to True.
        """
        # If credentials are provided, use them directly
        if api_key is not None and api_secret is not None:
            self.api_key = api_key
            self.api_secret = api_secret
            self.testnet = testnet
        # Otherwise, try to use global credentials set by initialize_credentials
        elif _API_KEY and _API_SECRET:
            self.api_key = _API_KEY
            self.api_secret = _API_SECRET
            self.testnet = _TESTNET
        # As a fallback, try to load from key.yaml
        else:
            try:
                # Try to load from key.yaml as a fallback
                current_dir = os.path.dirname(os.path.abspath(__file__))
                key_file = os.path.join(current_dir, 'config', 'key.yaml')
                
                with open(key_file) as f:
                    keys = yaml.safe_load(f)
                
                self.api_key = keys['BYBIT']['apiKey']
                self.api_secret = keys['BYBIT']['secret']
                self.testnet = keys['BYBIT']['live'] == "N"
            except Exception as e:
                print(f"Error loading API keys: {e}")
                # Default to empty credentials
                self.api_key = ""
                self.api_secret = ""
                self.testnet = True

    def extract_open_interest(self, symbol, interval="1h", start_time=None, end_time=None, progress_bar=True):
        """
        Extract historical open interest data with optimized settings
        
        Parameters:
        - symbol: Trading pair (e.g., "BTCUSDT")
        - interval: Time interval (e.g., "1h", "15m", etc.)
        - start_time: Start timestamp in seconds
        - end_time: End timestamp in seconds
        - progress_bar: Whether to display a progress bar
        
        Returns:
        - DataFrame with open interest data
        """
        endpoint = "/v5/market/open-interest"
        
        # Initialize empty list to store all data
        all_data = []
        
        # Convert timestamps to milliseconds
        start_ms = start_time * 1000 if start_time else None
        end_ms = end_time * 1000 if end_time else int(time.time() * 1000)
        
        # Track the current end time for pagination
        current_end = end_ms
        
        # OPTIMIZED: Use confirmed maximum limit of 200
        limit = 200
        
        # Variables for progress tracking
        total_records = 0
        start_extract_time = time.time()
        prev_progress_time = start_extract_time
        chunks_fetched = 0
        
        print(f"Extracting open interest data since {datetime.datetime.fromtimestamp(start_time).strftime('%Y-%m-%d') if start_time else 'inception'}")
        
        # Keep fetching data in chunks until we reach start_time
        while True:
            params = {
                "category": "linear",
                "symbol": symbol,
                "intervalTime": interval,
                "limit": limit,
                "endTime": current_end
            }
            
            # Add startTime only if provided and we're on the first request
            if start_ms and chunks_fetched == 0:
                params["startTime"] = start_ms
                
            # Make the request
            response = self._make_request("GET", endpoint, params)
            
            if not response or response.get("retCode") != 0:
                print(f"Error fetching open interest chunk: {response}")
                break
                
            data = response.get("result", {}).get("list", [])
            
            if not data:
                print("No more data available")
                break
                
            # Add the data to our collection
            all_data.extend(data)
            chunks_fetched += 1
            total_records += len(data)
            
            # Display progress every 10 chunks
            current_time = time.time()
            if progress_bar and (chunks_fetched % 10 == 0 or current_time - prev_progress_time > 5):
                elapsed = current_time - start_extract_time
                rate = total_records / elapsed if elapsed > 0 else 0
                
                if len(data) > 0:
                    oldest_ts = min(int(item["timestamp"]) for item in data)
                    oldest_date = datetime.datetime.fromtimestamp(oldest_ts/1000)
                    print(f"Progress: {chunks_fetched} chunks, {total_records} records ({rate:.1f} rec/sec), back to {oldest_date.strftime('%Y-%m-%d')}")
                    
                prev_progress_time = current_time
            
            # Get the oldest timestamp from this batch for the next request
            if len(data) > 0:
                oldest_ts = min(int(item["timestamp"]) for item in data)
                
                # If we've reached or passed the requested start time, we're done
                if start_ms and oldest_ts <= start_ms:
                    print(f"Reached requested start time")
                    break
                    
                # Set the next end time to just before the oldest record in this batch
                current_end = oldest_ts - 1
            else:
                # If no data in this batch, stop fetching
                break
                
            # OPTIMIZED: Use minimal delay to avoid rate limits
            time.sleep(0.1)
            
        # Calculate stats
        total_duration = time.time() - start_extract_time
        records_per_second = total_records / total_duration if total_duration > 0 else 0
        
        print(f"Extraction complete: {total_records} records in {total_duration:.1f} seconds ({records_per_second:.1f} rec/sec)")
            
        if not all_data:
            print("No open interest data found")
            return pd.DataFrame()
        
        # Convert to DataFrame
        df = pd.DataFrame(all_data)
        
        # Rename and convert columns
        df = df.rename(columns={
            "timestamp": "timestamp",
            "openInterest": "open_interest"
        })
        
        # Convert timestamp to datetime
        df["timestamp"] = pd.to_numeric(df["timestamp"])
        df["t"] = pd.to_datetime(df["timestamp"], unit="ms")
        
        # Sort by timestamp ascending
        df = df.sort_values("timestamp").reset_index(drop=True)
        
        # Filter by start_time if provided
        if start_time:
            df = df[df["timestamp"] >= start_ms]
            
        return df
    
    def extract_funding_rate(self, symbol, start_time=None, end_time=None, progress_bar=True):
        """
        Extract historical funding rate data with optimized settings
        
        Parameters:
        - symbol: Trading pair (e.g., "BTCUSDT")
        - start_time: Start timestamp in seconds
        - end_time: End timestamp in seconds
        - progress_bar: Whether to display a progress bar
        
        Returns:
        - DataFrame with funding rate data
        """
        endpoint = "/v5/market/funding/history"
        
        # Initialize empty list to store all data
        all_data = []
        
        # Convert timestamps to milliseconds
        start_ms = start_time * 1000 if start_time else None
        end_ms = end_time * 1000 if end_time else int(time.time() * 1000)
        
        # Track the current end time for pagination
        current_end = end_ms
        
        # OPTIMIZED: Use confirmed maximum limit of 200
        limit = 200
        
        # Variables for progress tracking
        total_records = 0
        start_extract_time = time.time()
        prev_progress_time = start_extract_time
        chunks_fetched = 0
        
        print(f"Extracting funding rate data since {datetime.datetime.fromtimestamp(start_time).strftime('%Y-%m-%d') if start_time else 'inception'}")
        
        # Keep fetching data in chunks until we reach start_time
        while True:
            params = {
                "category": "linear",
                "symbol": symbol,
                "limit": limit
            }
            
            # Add endTime parameter
            if current_end:
                params["endTime"] = current_end
                
            # Add startTime only for the first request if provided
            if start_ms and chunks_fetched == 0:
                params["startTime"] = start_ms
                
            # Make the request
            response = self._make_request("GET", endpoint, params)
            
            if not response or response.get("retCode") != 0:
                print(f"Error fetching funding rate chunk: {response}")
                break
                
            data = response.get("result", {}).get("list", [])
            
            if not data:
                print("No more funding rate data available")
                break
                
            # Add the data to our collection
            all_data.extend(data)
            chunks_fetched += 1
            total_records += len(data)
            
            # Display progress every 10 chunks
            current_time = time.time()
            if progress_bar and (chunks_fetched % 10 == 0 or current_time - prev_progress_time > 5):
                elapsed = current_time - start_extract_time
                rate = total_records / elapsed if elapsed > 0 else 0
                
                # Find the oldest timestamp in the current batch
                try:
                    oldest_ts = min(int(item.get("fundingRateTimestamp", 0)) for item in data if item.get("fundingRateTimestamp"))
                    oldest_date = datetime.datetime.fromtimestamp(oldest_ts/1000)
                    print(f"Progress: {chunks_fetched} chunks, {total_records} records ({rate:.1f} rec/sec), back to {oldest_date.strftime('%Y-%m-%d')}")
                except:
                    print(f"Progress: {chunks_fetched} chunks, {total_records} records ({rate:.1f} rec/sec)")
                    
                prev_progress_time = current_time
            
            # Get the oldest timestamp from this batch for the next request
            if len(data) > 0:
                # Find the oldest timestamp in the current batch
                try:
                    oldest_ts = min(int(item.get("fundingRateTimestamp", 0)) for item in data if item.get("fundingRateTimestamp"))
                    
                    # If we've reached or passed the requested start time, we're done
                    if start_ms and oldest_ts <= start_ms:
                        print(f"Reached requested start time")
                        break
                        
                    # Set the next end time to just before the oldest record in this batch
                    current_end = oldest_ts - 1
                except:
                    print(f"Unable to determine oldest timestamp in chunk")
                    break
            else:
                # If no data in this batch, stop fetching
                break
                
            # OPTIMIZED: Use minimal delay to avoid rate limits
            time.sleep(0.1)
            
        # Calculate stats
        total_duration = time.time() - start_extract_time
        records_per_second = total_records / total_duration if total_duration > 0 else 0
        
        print(f"Extraction complete: {total_records} records in {total_duration:.1f} seconds ({records_per_second:.1f} rec/sec)")
        
        if not all_data:
            print("No funding rate data found")
            return pd.DataFrame()
            
        # Convert to DataFrame
        df = pd.DataFrame(all_data)
        
        # Rename columns to standardize
        if not df.empty:
            column_map = {
                "symbol": "symbol",
                "fundingRate": "funding_rate",
                "fundingRateTimestamp": "timestamp"
            }
            # Only rename columns that exist
            rename_cols = {k: v for k, v in column_map.items() if k in df.columns}
            df = df.rename(columns=rename_cols)
            
            # Convert timestamp to datetime if it exists
            if "timestamp" in df.columns:
                df["timestamp"] = pd.to_numeric(df["timestamp"])
                df["t"] = pd.to_datetime(df["timestamp"], unit="ms")
                
                # Sort by timestamp ascending
                df = df.sort_values("timestamp").reset_index(drop=True)
                
                # Filter by start_time if provided
                if start_ms:
                    df = df[df["timestamp"] >= start_ms]
        
        return df

def extract_ohlcv_data(cex_symbol, data_type, resolution, since, until, api_key, api_secret, testnet=False):
    """
    Optimized extraction of OHLCV data
    
    Parameters:
    - cex_symbol: Trading pair symbol (e.g., "BTCUSDT")
    - data_type: Data type (e.g., "open", "high", "low", "close", "volume")
    - resolution: Time interval (e.g., "1h", "15m", etc.)
    - since: Start timestamp in seconds
    - until: End timestamp in seconds
    - api_key: API key
    - api_secret: API secret
    - testnet: Whether to use testnet
    
    Returns:
    - DataFrame with OHLCV data
    """
    # Create output directory if it doesn't exist
    os.makedirs("cex_data", exist_ok=True)
    
    # Calculate date range in days for filename
    days_range = int((until - since) / (24 * 60 * 60))
    
    # Initialize CCXT exchange
    exchange = ccxt.bybit({
        'apiKey': api_key,
        'secret': api_secret,
        'enableRateLimit': True,
        'options': {
            'recvWindow': 10000,
            'adjustForTimeDifference': True
        },
    })
    if testnet:
        exchange.enable_demo_trading(True)
    
    # Convert timestamps to milliseconds
    since_ms = since * 1000
    until_ms = until * 1000
    
    print(f"Extracting {cex_symbol} {data_type} data from {datetime.datetime.fromtimestamp(since).strftime('%Y-%m-%d')} to {datetime.datetime.fromtimestamp(until).strftime('%Y-%m-%d')}")
    
    # Initialize empty list for OHLCV data
    all_ohlcv = []
    
    # Variables for progress tracking
    current_start = since_ms
    start_extract_time = time.time()
    prev_progress_time = start_extract_time
    chunks_fetched = 0
    
    # OPTIMIZED: Use maximum allowed limit (1000 for OHLCV in CCXT)
    chunk_size = 1000
    
    # Keep fetching data until we reach the end time
    while current_start < until_ms:
        try:
            chunk = exchange.fetch_ohlcv(cex_symbol, resolution, since=current_start, limit=chunk_size)
            if not chunk or len(chunk) == 0:
                print("No more data available")
                break
                
            all_ohlcv.extend(chunk)
            chunks_fetched += 1
            
            # Update current_start to fetch the next chunk
            current_start = chunk[-1][0] + 1
            
            # Display progress every 10 chunks or every 5 seconds
            current_time = time.time()
            if chunks_fetched % 10 == 0 or current_time - prev_progress_time > 5:
                elapsed = current_time - start_extract_time
                records = len(all_ohlcv)
                rate = records / elapsed if elapsed > 0 else 0
                
                # Convert the timestamp of the last record to date for progress reporting
                latest_date = datetime.datetime.fromtimestamp(chunk[-1][0] / 1000)
                print(f"Progress: {chunks_fetched} chunks, {records} records ({rate:.1f} rec/sec), up to {latest_date.strftime('%Y-%m-%d')}")
                
                prev_progress_time = current_time
            
            # OPTIMIZED: Use minimal delay to avoid rate limits
            time.sleep(0.05)
            
        except Exception as e:
            print(f"Error fetching data chunk: {e}")
            # If we get an error, wait a bit longer before trying again
            time.sleep(1)
            # If we've had multiple errors, break
            if chunks_fetched > 0 and len(all_ohlcv) > 0:
                break
    
    total_records = len(all_ohlcv)
    total_duration = time.time() - start_extract_time
    records_per_second = total_records / total_duration if total_duration > 0 else 0
    
    print(f"Extraction complete: {total_records} records in {total_duration:.1f} seconds ({records_per_second:.1f} rec/sec)")
    
    if not all_ohlcv:
        print(f"Error fetching OHLCV data from {exchange.id}")
        return None
    
    # Create dataframe from OHLCV data
    df = pd.DataFrame(all_ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
    df['t'] = pd.to_datetime(df['timestamp'], unit='ms')
    
    # Save raw data to CSV
    csv_filename = f"cex_data/{cex_symbol}_{data_type}_{resolution}_{days_range}d_{pd.Timestamp.now().strftime('%Y%m%d')}.csv"
    
    # Only include timestamp, the requested data type, and t columns
    df_to_save = df[['timestamp', data_type, 't']]
    df_to_save.to_csv(csv_filename, index=False)
    print(f"Saved raw {data_type} data to {csv_filename}")
    
    # Create value dataframe using the requested data column
    df_value = pd.DataFrame({
        't': df['t'],
        'value': df[data_type]
    })
    
    return df_value

# Function to save data with year statistics
def save_with_stats(df, data_type, symbol, resolution, start_timestamp, end_timestamp):
    """Save data to CSV and print year statistics"""
    
    # Create output directory if it doesn't exist
    os.makedirs("cex_data", exist_ok=True)
    
    # Calculate date range in days for filename
    days_range = int((end_timestamp - start_timestamp) / (24 * 60 * 60))
    
    # Create filename
    csv_filename = f"cex_data/{symbol}_{data_type}_{resolution}_{days_range}d_{pd.Timestamp.now().strftime('%Y%m%d')}.csv"
    
    if 'timestamp' in df.columns and 'value' in df.columns:
        # Save CSV with timestamp and value columns
        df[['timestamp', 'value', 't']].to_csv(csv_filename, index=False)
    elif 'timestamp' in df.columns and data_type in df.columns:
        # Save CSV with timestamp and the specific data type column
        df[['timestamp', data_type, 't']].to_csv(csv_filename, index=False)
    else:
        # If we don't have the right columns, try to save what we have
        print(f"Warning: Expected columns not found. Saving all columns.")
        df.to_csv(csv_filename, index=False)
    
    print(f"Saved {data_type} data to {csv_filename}")
    
    # Print data statistics by year
    if 't' in df.columns:
        print(f"\nSummary of {data_type} data:")
        print(f"Total records: {len(df)}")
        print(f"Date range: {df['t'].min()} to {df['t'].max()}")
        
        # Count records by year
        year_counts = df['t'].dt.year.value_counts().sort_index()
        for year, count in year_counts.items():
            print(f"  {year}: {count} records")
    
    return csv_filename

# Main function to run the extraction
def main():
    print(f"Starting optimized data extraction for BTC from {start_date} to {end_date}")
    
    # Initialize results dictionary
    results = {}
    
    # Initialize the optimized extractor
    extractor = OptimizedExtractor(api_key=api_key, api_secret=api_secret, testnet=testnet)
    
    # Data types to extract
    data_types = ["open", "high", "low", "close", "volume", "open_interest", "funding_rate"]
    
    for data_type in data_types:
        try:
            print(f"\n{'='*80}")
            print(f"EXTRACTING {data_type.upper()} DATA")
            print(f"{'='*80}")
            
            if data_type == "open_interest":
                # Extract open interest using optimized method
                df_data = extractor.extract_open_interest(
                    symbol=SYMBOL,
                    interval=RESOLUTION,
                    start_time=TIMESTAMP_2022,
                    end_time=CURRENT_TIME
                )
                
                # Save with statistics
                if df_data is not None and not df_data.empty:
                    save_with_stats(
                        df_data, 
                        data_type, 
                        SYMBOL, 
                        RESOLUTION, 
                        TIMESTAMP_2022, 
                        CURRENT_TIME
                    )
                    
                    # Store results
                    results[data_type] = {
                        "status": "SUCCESS",
                        "records": len(df_data),
                        "start_date": df_data['t'].min().strftime('%Y-%m-%d %H:%M:%S'),
                        "end_date": df_data['t'].max().strftime('%Y-%m-%d %H:%M:%S'),
                        "years": sorted(df_data['t'].dt.year.unique().tolist())
                    }
                else:
                    results[data_type] = {
                        "status": "FAILED",
                        "reason": "No data returned"
                    }
                
            elif data_type == "funding_rate":
                # Extract funding rate using optimized method
                df_data = extractor.extract_funding_rate(
                    symbol=SYMBOL,
                    start_time=TIMESTAMP_2022,
                    end_time=CURRENT_TIME
                )
                
                # Save with statistics
                if df_data is not None and not df_data.empty:
                    save_with_stats(
                        df_data, 
                        data_type, 
                        SYMBOL, 
                        RESOLUTION, 
                        TIMESTAMP_2022, 
                        CURRENT_TIME
                    )
                    
                    # Store results
                    results[data_type] = {
                        "status": "SUCCESS",
                        "records": len(df_data),
                        "start_date": df_data['t'].min().strftime('%Y-%m-%d %H:%M:%S'),
                        "end_date": df_data['t'].max().strftime('%Y-%m-%d %H:%M:%S'),
                        "years": sorted(df_data['t'].dt.year.unique().tolist())
                    }
                else:
                    results[data_type] = {
                        "status": "FAILED",
                        "reason": "No data returned"
                    }
                
            else:
                # Extract OHLCV data using optimized method
                df_data = extract_ohlcv_data(
                    cex_symbol=SYMBOL,
                    data_type=data_type,
                    resolution=RESOLUTION,
                    since=TIMESTAMP_2022,
                    until=CURRENT_TIME,
                    api_key=api_key,
                    api_secret=api_secret,
                    testnet=testnet
                )
                
                if df_data is not None and not df_data.empty:
                    # Store results
                    results[data_type] = {
                        "status": "SUCCESS",
                        "records": len(df_data),
                        "start_date": df_data['t'].min().strftime('%Y-%m-%d %H:%M:%S'),
                        "end_date": df_data['t'].max().strftime('%Y-%m-%d %H:%M:%S'),
                        "years": sorted(df_data['t'].dt.year.unique().tolist())
                    }
                else:
                    results[data_type] = {
                        "status": "FAILED",
                        "reason": "No data returned"
                    }
        
        except Exception as e:
            print(f"Error extracting {data_type} data: {e}")
            results[data_type] = {
                "status": "ERROR",
                "reason": str(e)
            }
    
    # Print summary
    print("\n" + "="*100)
    print(f"SUMMARY OF OPTIMIZED BTC DATA EXTRACTION ({start_date} to {end_date})")
    print("="*100)
    print(f"{'Data Type':<15} {'Status':<10} {'Records':<10} {'Date Range':<40} {'Years Covered'}")
    print("-"*100)
    
    for data_type in data_types:
        result = results.get(data_type, {"status": "UNKNOWN"})
        status = result["status"]
        if status == "SUCCESS":
            records = str(result["records"])
            date_range = f"{result['start_date']} to {result['end_date']}"
            years = ", ".join(map(str, result["years"]))
            print(f"{data_type:<15} {'✅ ' + status:<10} {records:<10} {date_range:<40} {years}")
        else:
            reason = result.get("reason", "Unknown")
            print(f"{data_type:<15} {'❌ ' + status:<10} {'N/A':<10} {'Failed: ' + reason:<40}")
    
    print("\nExtraction complete. All data saved to the 'cex_data' folder.")
    
if __name__ == "__main__":
    main() 