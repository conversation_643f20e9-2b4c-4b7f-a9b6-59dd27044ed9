import os
import sys
import pandas as pd
import time
import datetime
import yaml

# Import necessary modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from bybit_direct_api import BybitDirectAPI

# Set the specific timestamp for 2023-01-01
specific_timestamp = 1672531200  # 2023-01-01

# Print the timestamp in human-readable format for reference
timestamp_date = datetime.datetime.fromtimestamp(specific_timestamp)
print(f"Using start timestamp: {specific_timestamp} ({timestamp_date})")

# Set common parameters
symbol = "BTCUSDT"
resolution = "1h"

print("\n=== Testing extraction for open_interest data ===")

try:
    # Load API keys
    current_dir = os.path.dirname(os.path.abspath(__file__))
    key_file = os.path.join(current_dir, 'config', 'key.yaml')
    
    with open(key_file) as f:
        keys = yaml.safe_load(f)
        print('Loading key file', key_file, '.....')
    
    api_key = keys['BYBIT']['apiKey']
    api_secret = keys['BYBIT']['secret']
    testnet = keys['BYBIT']['live'] == "N"
    
    # Initialize the Bybit API client
    bybit = BybitDirectAPI(api_key=api_key, api_secret=api_secret, testnet=testnet)
    
    # Fetch open interest data from 2023-01-01 to now
    print(f"Fetching open interest data from {timestamp_date} to now...")
    df_value = bybit.extract_and_save_data(
        data_type="open_interest",
        symbol=symbol,
        resolution=resolution,
        since=specific_timestamp,
        until=int(time.time())
    )
    
    if df_value is not None and not df_value.empty:
        print(f"\nSuccessfully extracted open interest data:")
        print(f"Data shape: {df_value.shape}")
        print(f"First few rows:\n{df_value.head(3)}")
        print(f"Last few rows:\n{df_value.tail(3)}")
        print(f"Date range: {df_value['t'].min()} to {df_value['t'].max()}")
        
        # Check for CSV file
        days_range = int((time.time() - specific_timestamp) / (24 * 60 * 60))
        csv_filename = f"cex_data/{symbol}_open_interest_{resolution}_{days_range}d_{pd.Timestamp.now().strftime('%Y%m%d')}.csv"
        
        if os.path.exists(csv_filename):
            file_size = os.path.getsize(csv_filename) / 1024  # Size in KB
            print(f"\nCSV file created: {csv_filename}")
            print(f"File size: {file_size:.2f} KB")
    else:
        print("\nFailed to extract open interest data")
except Exception as e:
    print(f"\nError in data extraction: {e}")

print("\nData extraction test complete. Check the 'cex_data' folder for the saved CSV file.")
print("\nFor open_interest data, the BybitDirectAPI implementation works as expected.") 