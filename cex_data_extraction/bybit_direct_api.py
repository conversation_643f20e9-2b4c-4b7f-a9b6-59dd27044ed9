import requests
import pandas as pd
import time
import hmac
import hashlib
import json
import os
from urllib.parse import urlencode
import datetime
import yaml

# Add global credential variables at the top of the file
_API_KEY = ""
_API_SECRET = ""
_TESTNET = True

def initialize_credentials(api_key, api_secret, testnet=True):
    """Initialize credentials for API access without using key.yaml
    
    Args:
        api_key (str): The API key for Bybit
        api_secret (str): The API secret for Bybit
        testnet (bool): Whether to use testnet (default: True)
    """
    global _API_KEY, _API_SECRET, _TESTNET
    _API_KEY = api_key
    _API_SECRET = api_secret
    _TESTNET = testnet
    print(f"Bybit Direct API credentials initialized (testnet: {_TESTNET})")

class BybitDirectAPI:
    """
    Direct implementation of Bybit API for data types not well supported by CCXT:
    - Open Interest
    - Liquidations
    - Premium Index
    """
    
    BASE_URL = "https://api.bybit.com"  # Main API endpoint
    
    def __init__(self, api_key=None, api_secret=None, testnet=True):
        """
        Initialize the Bybit API client
        
        Parameters:
        - api_key: Bybit API key
        - api_secret: Bybit API secret
        - testnet: Whether to use testnet (default: True)
        """
        self.api_key = api_key
        self.api_secret = api_secret
        
        if testnet:
            self.BASE_URL = "https://api-testnet.bybit.com"
        
        # Initialize timestamps
        self.recv_window = 5000
        self.start_timestamp = int(time.time() * 1000) - 1000
        
        # If credentials are provided, use them directly
        if api_key is not None and api_secret is not None:
            self.api_key = api_key
            self.api_secret = api_secret
            self.testnet = testnet
        # Otherwise, try to use global credentials set by initialize_credentials
        elif _API_KEY and _API_SECRET:
            self.api_key = _API_KEY
            self.api_secret = _API_SECRET
            self.testnet = _TESTNET
        # As a fallback, try to load from key.yaml
        else:
            try:
                # Try to load from key.yaml as a fallback
                current_dir = os.path.dirname(os.path.abspath(__file__))
                key_file = os.path.join(current_dir, 'config', 'key.yaml')
                
                with open(key_file) as f:
                    keys = yaml.safe_load(f)
                
                self.api_key = keys['BYBIT']['apiKey']
                self.api_secret = keys['BYBIT']['secret']
                self.testnet = keys['BYBIT']['live'] == "N"
            except Exception as e:
                print(f"Error loading API keys: {e}")
                # Default to empty credentials
                self.api_key = ""
                self.api_secret = ""
                self.testnet = True
        
        # Set the base URL based on testnet flag
        self.base_url = "https://api-testnet.bybit.com" if self.testnet else "https://api.bybit.com"
        
        # Configure session
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'X-BAPI-API-KEY': self.api_key
        })
    
    def _generate_signature(self, params, recv_window=5000):
        """Generate Bybit API signature for authentication"""
        param_str = ""
        
        # Add timestamp and recv_window to params
        timestamp = int(time.time() * 1000)
        params['timestamp'] = timestamp
        params['recv_window'] = recv_window
        
        # Sort parameters by key
        keys = sorted(params.keys())
        for key in keys:
            param_str += key + "=" + str(params[key]) + "&"
        param_str = param_str[:-1]  # Remove the last '&'
        
        # Create signature
        signature = hmac.new(
            bytes(self.api_secret, "utf-8"),
            bytes(param_str, "utf-8"),
            hashlib.sha256
        ).hexdigest()
        
        return timestamp, signature
    
    def _make_request(self, method, endpoint, params=None, auth=False):
        """Make API request to Bybit"""
        url = f"{self.BASE_URL}{endpoint}"
        headers = {
            "Content-Type": "application/json",
            "X-BAPI-API-KEY": self.api_key if auth else ""
        }
        
        # Add authentication if required
        if auth and self.api_key and self.api_secret:
            timestamp, signature = self._generate_signature(params)
            headers["X-BAPI-SIGN"] = signature
            headers["X-BAPI-SIGN-TYPE"] = "2"
            headers["X-BAPI-TIMESTAMP"] = str(timestamp)
            
        # Make request
        if method == "GET":
            if params:
                query_string = urlencode(params)
                url = f"{url}?{query_string}"
            response = requests.get(url, headers=headers)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=params)
        
        # Check response
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Error {response.status_code}: {response.text}")
            return None
    
    def get_open_interest(self, symbol, interval="1h", start_time=None, end_time=None, limit=200, category="linear"):
        """
        Get historical open interest data
        
        Parameters:
        - symbol: Trading pair (e.g., "BTCUSDT")
        - interval: Time interval (e.g., "1h", "15m", etc.)
        - start_time: Start timestamp in milliseconds
        - end_time: End timestamp in milliseconds
        - limit: Maximum number of results (default: 200, max: 200)
        - category: Product type (linear, inverse, option)
        
        Returns:
        - DataFrame with open interest data
        """
        endpoint = "/v5/market/open-interest"
        
        # Initialize empty list to store all data
        all_data = []
        
        # If no end_time is provided, use current time
        if not end_time:
            end_time = int(time.time() * 1000)
            
        # Track the current end time for pagination
        current_end = end_time
        
        # Maximum number of chunks to fetch to avoid too many requests
        # Increased from 10 to 100 to get more historical data
        max_chunks = 100  # This allows retrieving up to 20,000 records (100 chunks * 200 limit)
        chunks_fetched = 0
        
        print(f"Attempting to fetch historical open interest data since {datetime.datetime.fromtimestamp(start_time/1000 if start_time else (time.time() - 365*24*60*60)).strftime('%Y-%m-%d')}")
        
        # Keep fetching data in chunks until we reach start_time or max_chunks
        while chunks_fetched < max_chunks:
            params = {
                "category": category,
                "symbol": symbol,
                "intervalTime": interval,
                "limit": limit,
                "endTime": current_end
            }
            
            # Add startTime only if provided and we're on the first request
            if start_time and chunks_fetched == 0:
                params["startTime"] = start_time
                
            # Make the request
            response = self._make_request("GET", endpoint, params)
            
            if not response or response.get("retCode") != 0:
                print(f"Error fetching open interest chunk: {response}")
                break
                
            data = response.get("result", {}).get("list", [])
            
            if not data:
                print("No more data available")
                break
                
            # Add the data to our collection
            all_data.extend(data)
            
            print(f"Fetched chunk {chunks_fetched + 1}: {len(data)} records")
            
            # Get the oldest timestamp from this batch for the next request
            if len(data) > 0:
                oldest_ts = min(int(item["timestamp"]) for item in data)
                
                # If we've reached or passed the requested start time, we're done
                if start_time and oldest_ts <= start_time:
                    print(f"Reached requested start time")
                    break
                    
                # Set the next end time to just before the oldest record in this batch
                current_end = oldest_ts - 1
                
                # Print progress information every 10 chunks
                if chunks_fetched % 10 == 0 and chunks_fetched > 0:
                    oldest_date = datetime.datetime.fromtimestamp(oldest_ts/1000)
                    print(f"Progress: Retrieved data back to {oldest_date.strftime('%Y-%m-%d %H:%M:%S')}")
            else:
                # If no data in this batch, stop fetching
                break
                
            # Small delay to avoid rate limits - increased to handle more requests
            time.sleep(1.0)
            
            chunks_fetched += 1
            
        if not all_data:
            print("No open interest data found")
            return pd.DataFrame()
        
        print(f"Successfully fetched {len(all_data)} records of open interest data")
            
        # Convert to DataFrame
        df = pd.DataFrame(all_data)
        
        # Rename and convert columns
        df = df.rename(columns={
            "timestamp": "timestamp",
            "openInterest": "open_interest"
        })
        
        # Convert timestamp to datetime
        df["timestamp"] = pd.to_numeric(df["timestamp"])
        df["t"] = pd.to_datetime(df["timestamp"], unit="ms")
        
        # Sort by timestamp ascending
        df = df.sort_values("timestamp").reset_index(drop=True)
        
        # Filter by start_time if provided
        if start_time:
            df = df[df["timestamp"] >= start_time]
            
        return df
    
    def get_liquidations(self, symbol, start_time=None, end_time=None, limit=100, category="linear"):
        """
        Get recent liquidation orders
        
        Parameters:
        - symbol: Trading pair (e.g., "BTCUSDT")
        - start_time: Start timestamp in milliseconds
        - end_time: End timestamp in milliseconds
        - limit: Maximum number of results (default: 100)
        - category: Product type (linear, inverse, option)
        
        Returns:
        - DataFrame with liquidation data
        """
        endpoint = "/v5/market/liquidation-history"
        params = {
            "category": category,
            "symbol": symbol,
            "limit": limit
        }
        
        if start_time:
            params["startTime"] = start_time
        if end_time:
            params["endTime"] = end_time
        
        response = self._make_request("GET", endpoint, params)
        
        if not response or response.get("retCode") != 0:
            print(f"Error fetching liquidations: {response}")
            return None
        
        data = response.get("result", {}).get("list", [])
        
        if not data:
            return pd.DataFrame()
        
        # Convert to DataFrame
        df = pd.DataFrame(data)
        
        # Rename and convert columns
        if not df.empty and "updatedTime" in df.columns:
            df = df.rename(columns={
                "updatedTime": "timestamp",
                "side": "side",
                "price": "price",
                "size": "amount"
            })
            
            # Convert timestamp to datetime
            df["timestamp"] = pd.to_numeric(df["timestamp"])
            df["t"] = pd.to_datetime(df["timestamp"], unit="ms")
        
        return df
    
    def get_premium_index(self, symbol, start_time=None, end_time=None, limit=200, category="linear"):
        """
        Get premium index price
        
        Parameters:
        - symbol: Trading pair (e.g., "BTCUSDT")
        - start_time: Start timestamp in milliseconds
        - end_time: End timestamp in milliseconds
        - limit: Maximum number of results (default: 200)
        - category: Product type (linear, inverse)
        
        Returns:
        - DataFrame with premium index data
        """
        endpoint = "/v5/market/premium-index-price"
        params = {
            "category": category,
            "symbol": symbol,
            "limit": limit
        }
        
        if start_time:
            params["startTime"] = start_time
        if end_time:
            params["endTime"] = end_time
        
        response = self._make_request("GET", endpoint, params)
        
        if not response or response.get("retCode") != 0:
            print(f"Error fetching premium index: {response}")
            return None
        
        data = response.get("result", {}).get("list", [])
        
        if not data:
            return pd.DataFrame()
        
        # Convert to DataFrame
        df = pd.DataFrame(data)
        
        # Rename and convert columns
        df = df.rename(columns={
            "timestamp": "timestamp",
            "markPrice": "mark_price",
            "indexPrice": "index_price",
            "premiumIndex": "premium_index"
        })
        
        # Convert timestamp to datetime
        df["timestamp"] = pd.to_numeric(df["timestamp"])
        df["t"] = pd.to_datetime(df["timestamp"], unit="ms")
        
        return df
    
    def extract_and_save_data(self, data_type, symbol, resolution="1h", since=None, until=None, category="linear"):
        """
        Extract data using direct Bybit API and save to CSV
        
        Parameters:
        - data_type: Type of data ("open_interest", "liquidation", "premium_index", "funding_rate")
        - symbol: Trading pair (e.g., "BTCUSDT")
        - resolution: Time interval (e.g., "1h", "15m", etc.)
        - since: Start timestamp in seconds
        - until: End timestamp in seconds
        - category: Product type (linear, inverse)
        
        Returns:
        - DataFrame with requested data
        """
        # Create output directory if it doesn't exist
        os.makedirs("cex_data", exist_ok=True)
        
        # Convert timestamps to milliseconds for Bybit API
        since_ms = since * 1000 if since else None
        until_ms = until * 1000 if until else int(time.time() * 1000)
        
        # Calculate date range in days for filename
        if since:
            days_range = int((until - since) / (24 * 60 * 60))
        else:
            days_range = 30  # Default to 30 days if not specified
        
        df = None
        
        # Fetch different data types
        if data_type == "open_interest":
            print(f"Fetching {symbol} open interest data from Bybit API...")
            df = self.get_open_interest(symbol, interval=resolution, start_time=since_ms, end_time=until_ms, category=category)
            if df is not None and not df.empty:
                # Save to CSV
                csv_filename = f"cex_data/{symbol}_open_interest_{resolution}_{days_range}d_{pd.Timestamp.now().strftime('%Y%m%d')}.csv"
                # Only save timestamp, open_interest, and t columns
                df_to_save = df[['timestamp', 'open_interest', 't']]
                
                # Print summary of records by year
                if len(df_to_save) > 0:
                    print(f"\nSummary of open interest data:")
                    print(f"Total records: {len(df_to_save)}")
                    print(f"Date range: {df_to_save['t'].min()} to {df_to_save['t'].max()}")
                    
                    # Count records by year
                    year_counts = df_to_save['t'].dt.year.value_counts().sort_index()
                    for year, count in year_counts.items():
                        print(f"  {year}: {count} records")
                
                # Save to CSV
                df_to_save.to_csv(csv_filename, index=False)
                print(f"Saved open interest data to {csv_filename}")
                
                # Create value dataframe for backtesting
                df_value = pd.DataFrame({
                    't': df['t'],
                    'value': df['open_interest']
                })
                return df_value
                
        elif data_type == "funding_rate":
            print(f"Fetching {symbol} funding rate data from Bybit API...")
            df = self.get_funding_rate(symbol, start_time=since_ms, end_time=until_ms, category=category)
            if df is not None and not df.empty:
                # Save to CSV
                csv_filename = f"cex_data/{symbol}_funding_rate_{resolution}_{days_range}d_{pd.Timestamp.now().strftime('%Y%m%d')}.csv"
                
                # Only save timestamp, funding_rate, and t columns
                if all(col in df.columns for col in ['timestamp', 'funding_rate', 't']):
                    df_to_save = df[['timestamp', 'funding_rate', 't']]
                    
                    # Print summary of records by year
                    if len(df_to_save) > 0:
                        print(f"\nSummary of funding rate data:")
                        print(f"Total records: {len(df_to_save)}")
                        print(f"Date range: {df_to_save['t'].min()} to {df_to_save['t'].max()}")
                        
                        # Count records by year
                        year_counts = df_to_save['t'].dt.year.value_counts().sort_index()
                        for year, count in year_counts.items():
                            print(f"  {year}: {count} records")
                    
                    # Save to CSV
                    df_to_save.to_csv(csv_filename, index=False)
                    print(f"Saved funding rate data to {csv_filename}")
                    
                    # Create value dataframe for backtesting
                    df_value = pd.DataFrame({
                        't': df['t'],
                        'value': df['funding_rate']
                    })
                    return df_value
                else:
                    print(f"Error: Required columns missing in funding rate data")
            
        elif data_type == "liquidation":
            print(f"Fetching {symbol} liquidation data from Bybit API...")
            df = self.get_liquidations(symbol, start_time=since_ms, end_time=until_ms, category=category)
            if df is not None and not df.empty:
                # Save to CSV
                csv_filename = f"cex_data/{symbol}_liquidation_{resolution}_{days_range}d_{pd.Timestamp.now().strftime('%Y%m%d')}.csv"
                # Only save timestamp, side, amount, and t columns
                df_to_save = df[['timestamp', 'side', 'amount', 't']]
                df_to_save.to_csv(csv_filename, index=False)
                print(f"Saved liquidation data to {csv_filename}")
                
                # Create value dataframe - sum of liquidation amounts by timeframe
                if not df.empty:
                    # Resample to the specified resolution and sum the amounts
                    df.set_index('t', inplace=True)
                    resampled = df.resample(self._resolution_to_pandas(resolution))['amount'].sum().reset_index()
                    df_value = pd.DataFrame({
                        't': resampled['t'],
                        'value': resampled['amount']
                    })
                    return df_value
            
        elif data_type == "premium_index":
            print(f"Fetching {symbol} premium index data from Bybit API...")
            df = self.get_premium_index(symbol, start_time=since_ms, end_time=until_ms, category=category)
            if df is not None and not df.empty:
                # Save to CSV
                csv_filename = f"cex_data/{symbol}_premium_index_{resolution}_{days_range}d_{pd.Timestamp.now().strftime('%Y%m%d')}.csv"
                # Only save timestamp, premium_index, and t columns
                if 'premium_index' in df.columns:
                    value_col = 'premium_index'
                elif 'index_price' in df.columns:
                    value_col = 'index_price'
                else:
                    value_col = 'mark_price'
                
                df_to_save = df[['timestamp', value_col, 't']]
                df_to_save.to_csv(csv_filename, index=False)
                print(f"Saved premium index data to {csv_filename}")
                
                # Create value dataframe
                df_value = pd.DataFrame({
                    't': df['t'],
                    'value': df[value_col]
                })
                return df_value
        
        print(f"Failed to extract {data_type} data.")
        return None
    
    def _resolution_to_pandas(self, resolution):
        """Convert exchange resolution to pandas resample rule"""
        mapping = {
            '1m': '1T',
            '3m': '3T',
            '5m': '5T',
            '15m': '15T',
            '30m': '30T',
            '1h': '1H',
            '2h': '2H',
            '4h': '4H',
            '6h': '6H',
            '12h': '12H',
            '1d': '1D'
        }
        return mapping.get(resolution, '1H')
    
    def get_funding_rate(self, symbol, start_time=None, end_time=None, limit=200, category="linear"):
        """
        Get historical funding rate data directly from Bybit API
        
        Parameters:
        - symbol: Trading pair (e.g., "BTCUSDT")
        - start_time: Start timestamp in milliseconds
        - end_time: End timestamp in milliseconds
        - limit: Maximum number of results (default: 200, max: 200)
        - category: Product type (linear, inverse)
        
        Returns:
        - DataFrame with funding rate data
        """
        endpoint = "/v5/market/funding/history"
        
        # Initialize empty list to store all data
        all_data = []
        
        # If no end_time is provided, use current time
        if not end_time:
            end_time = int(time.time() * 1000)
            
        # Track the current end time for pagination
        current_end = end_time
        
        # Maximum number of chunks to fetch to avoid too many requests
        max_chunks = 100  # This allows retrieving up to 20,000 records
        chunks_fetched = 0
        
        print(f"Attempting to fetch historical funding rate data since {datetime.datetime.fromtimestamp(start_time/1000 if start_time else (time.time() - 365*24*60*60)).strftime('%Y-%m-%d')}")
        
        # Keep fetching data in chunks until we reach start_time or max_chunks
        while chunks_fetched < max_chunks:
            params = {
                "category": category,
                "symbol": symbol,
                "limit": limit
            }
            
            # Add endTime parameter
            if current_end:
                params["endTime"] = current_end
                
            # Add startTime only for the first request if provided
            if start_time and chunks_fetched == 0:
                params["startTime"] = start_time
                
            # Make the request
            response = self._make_request("GET", endpoint, params)
            
            if not response or response.get("retCode") != 0:
                print(f"Error fetching funding rate chunk: {response}")
                break
                
            data = response.get("result", {}).get("list", [])
            
            if not data:
                print("No more funding rate data available")
                break
                
            # Add the data to our collection
            all_data.extend(data)
            
            print(f"Fetched funding rate chunk {chunks_fetched + 1}: {len(data)} records")
            
            # Get the oldest timestamp from this batch for the next request
            if len(data) > 0:
                # Find the oldest timestamp in the current batch
                try:
                    oldest_ts = min(int(item.get("fundingRateTimestamp", 0)) for item in data if item.get("fundingRateTimestamp"))
                    
                    # If we've reached or passed the requested start time, we're done
                    if start_time and oldest_ts <= start_time:
                        print(f"Reached requested start time")
                        break
                        
                    # Set the next end time to just before the oldest record in this batch
                    current_end = oldest_ts - 1
                    
                    # Print progress information every 10 chunks
                    if chunks_fetched % 10 == 0 and chunks_fetched > 0:
                        oldest_date = datetime.datetime.fromtimestamp(oldest_ts/1000)
                        print(f"Progress: Retrieved funding rate data back to {oldest_date.strftime('%Y-%m-%d %H:%M:%S')}")
                except (ValueError, KeyError) as e:
                    print(f"Error processing timestamps: {e}")
                    break
            else:
                # If no data in this batch, stop fetching
                break
                
            # Small delay to avoid rate limits
            time.sleep(1.0)
            
            chunks_fetched += 1
            
        if not all_data:
            print("No funding rate data found")
            return pd.DataFrame()
        
        print(f"Successfully fetched {len(all_data)} records of funding rate data")
            
        # Convert to DataFrame
        df = pd.DataFrame(all_data)
        
        # Rename columns to standardize
        if not df.empty:
            column_map = {
                "symbol": "symbol",
                "fundingRate": "funding_rate",
                "fundingRateTimestamp": "timestamp"
            }
            # Only rename columns that exist
            rename_cols = {k: v for k, v in column_map.items() if k in df.columns}
            df = df.rename(columns=rename_cols)
            
            # Convert timestamp to datetime if it exists
            if "timestamp" in df.columns:
                df["timestamp"] = pd.to_numeric(df["timestamp"])
                df["t"] = pd.to_datetime(df["timestamp"], unit="ms")
                
                # Sort by timestamp ascending
                df = df.sort_values("timestamp").reset_index(drop=True)
                
                # Filter by start_time if provided
                if start_time:
                    df = df[df["timestamp"] >= start_time]
        
        return df


# Example usage
if __name__ == "__main__":
    # Load API keys (you should implement a proper way to securely store and retrieve them)
    # For example, load from file or environment variables
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        key_file = os.path.join(current_dir, 'config', 'key.yaml')
        
        with open(key_file) as f:
            keys = yaml.safe_load(f)
            print('Loading key file', key_file, '.....')
        
        api_key = keys['BYBIT']['apiKey']
        api_secret = keys['BYBIT']['secret']
        testnet = keys['BYBIT']['live'] == "N"
    except:
        print("Failed to load API keys from config file")
        api_key = None
        api_secret = None
        testnet = False
    
    # Initialize API client
    bybit = BybitDirectAPI(api_key=api_key, api_secret=api_secret, testnet=testnet)
    
    # Example timestamp - start from 1 month ago
    since = int(time.time()) - (30 * 24 * 60 * 60)
    
    # Test different data types
    print("\n=== Open Interest Data ===")
    df_oi = bybit.extract_and_save_data("open_interest", "BTCUSDT", resolution="1h", since=since)
    if df_oi is not None:
        print(f"Data shape: {df_oi.shape}")
        print(f"First few rows:\n{df_oi.head(3)}")
    
    print("\n=== Liquidation Data ===")
    df_liq = bybit.extract_and_save_data("liquidation", "BTCUSDT", resolution="1h", since=since)
    if df_liq is not None:
        print(f"Data shape: {df_liq.shape}")
        print(f"First few rows:\n{df_liq.head(3)}")
    
    print("\n=== Premium Index Data ===")
    df_pi = bybit.extract_and_save_data("premium_index", "BTCUSDT", resolution="1h", since=since)
    if df_pi is not None:
        print(f"Data shape: {df_pi.shape}")
        print(f"First few rows:\n{df_pi.head(3)}")
    
    print("\n=== Funding Rate Data ===")
    df_fr = bybit.get_funding_rate("BTCUSDT", start_time=since, end_time=int(time.time()), limit=200)
    if df_fr is not None:
        print(f"Data shape: {df_fr.shape}")
        print(f"First few rows:\n{df_fr.head(3)}") 