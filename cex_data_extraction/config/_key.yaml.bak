EXCHANGE: "BYBIT"  # Options: "BYBIT" or "HYPERLIQUID" or "BINANCE"
HISTORICAL_YEARS: 2
SHIFT: 1

BYBIT:
  apiKey: "1TjkjSxFO8f1Sw5m57"
  secret: "DTPF7HKGflVLjeIZM88bGCbv1KDa7Wo9Pfro"
  live: "Y"   # Live account - "Y" ; Demo acount = "N"
  vip: 2   # 0, 1, 2, 3, 4, 5, S

BINANCE:
  apiKey: ""
  secret: ""
  live: "N"  # Live account - "Y"; Test account - "N"

# mainnet: https://app.hyperliquid.xyz/trade
# testnet: https://app.hyperliquid-testnet.xyz
HYPERLIQUID:
  walletAddress: ""
  privateKey: ""
  live: "N"  # mainnet - "Y"; testnet - "N"

# Glassnode API
GLASSNODE:
  API_KEY: "***************************"

# Telegram
TELEGRAM:
  CHAT_ID: ""
  BOT_TOKEN: ""