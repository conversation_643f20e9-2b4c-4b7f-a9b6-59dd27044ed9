import requests
import pandas as pd
import numpy as np
import plotly.express as px
import seaborn as sns
import matplotlib.pyplot as plt
import time
from io import StringIO
from tqdm import tqdm
from numba import jit
import os
import warnings
from models import Strategy, get_base_params
import yaml  # install package PyYAML
import ccxt
import datetime
import sys

warnings.filterwarnings("ignore", category=UserWarning, message="FigureCanvasAgg is non-interactive, and thus cannot be shown")

os.environ['PYTHONUNBUFFERED'] = '1'

# 執行進度
PROGRESS_BAR = True     # True - 顯示 ; False - 不顯示

# 新增全局變數控制圖片儲存
SAVE_FIGURES = False  # True = 儲存圖片到文件, False = 彈出顯示圖片

# Global variables to share credentials with imported modules
BYBIT_API_KEY = ""
BYBIT_SECRET = ""
BYBIT_TESTNET = True
GLASSNODE_API_KEY = ""

def process_glassnode_data(data, metric_key=None):
    result = []
    for item in data:
        value = (
            item['v'] if 'v' in item
            else item['o'].get(metric_key) if metric_key and isinstance(item['o'], dict)
            else item['o'] if isinstance(item['o'], (int, float))
            else sum(item['o'].values()) if isinstance(item['o'], dict)
            else None
        )

        if value is not None:
            result.append({
                't': pd.to_datetime(item['t'], unit='s'),
                'value': value
            })

    return pd.DataFrame(result)

def initialize_globals():
    global keys, selected_exchange, API_KEY, config, ORDER_TYPE
    global SYMBOL, EXCHANGE, PRECISION, MIN_POS, GLASSNODE_SYMBOL, DATESTART, RUN_FREQ
    global MAX_POS, gn_data, signal_data, strategy_sr, has_run_backtesting
    global BYBIT_API_KEY, BYBIT_SECRET, BYBIT_TESTNET, GLASSNODE_API_KEY

    # Read from Excel file
    fund_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    excel_file = os.path.join(fund_dir, 'fund_bybit.xlsx')
    
    try:
        # Load the 'key' worksheet from the Excel file
        print(f'Loading credentials from {excel_file} ...')
        key_df = pd.read_excel(excel_file, sheet_name='key')
        
        # Convert DataFrame to a dictionary for easy access
        keys = {}
        
        # Read Exchange info
        exchange_row = key_df[key_df['Field'] == 'EXCHANGE']
        if not exchange_row.empty:
            selected_exchange = exchange_row['Value'].iloc[0].upper()
        else:
            selected_exchange = 'BYBIT'  # Default
            
        print(f"Selected Exchange: {selected_exchange}")
        
        # Initialize Bybit credentials
        keys['BYBIT'] = {}
        
        # Get APIKEY
        apikey_row = key_df[key_df['Field'] == 'APIKEY']
        if not apikey_row.empty:
            keys['BYBIT']['apiKey'] = apikey_row['Value'].iloc[0]
            BYBIT_API_KEY = keys['BYBIT']['apiKey']  # Set global variable
        
        # Get SECRET
        secret_row = key_df[key_df['Field'] == 'SECRET']
        if not secret_row.empty:
            keys['BYBIT']['secret'] = secret_row['Value'].iloc[0]
            BYBIT_SECRET = keys['BYBIT']['secret']  # Set global variable
        
        # Get LIVE_ACCOUNT status
        live_account_row = key_df[key_df['Field'] == 'LIVE_ACCOUNT']
        if not live_account_row.empty:
            # Convert to string 'Y' or 'N'
            is_live = str(live_account_row['Value'].iloc[0]).upper()
            keys['BYBIT']['live'] = 'Y' if is_live in ['Y', 'YES', 'TRUE', '1'] else 'N'
            BYBIT_TESTNET = not (is_live in ['Y', 'YES', 'TRUE', '1'])  # Set global variable
        else:
            keys['BYBIT']['live'] = 'N'  # Default to testnet
            BYBIT_TESTNET = True
            
        # Get VIP level (if available)
        vip_row = key_df[key_df['Field'] == 'VIP']
        if not vip_row.empty:
            keys['BYBIT']['vip'] = vip_row['Value'].iloc[0]
            
        # For backward compatibility with glassnode
        keys['GLASSNODE'] = {'API_KEY': ''}
        glassnode_row = key_df[key_df['Field'] == 'GLASSNODE_API_KEY']
        if not glassnode_row.empty:
            keys['GLASSNODE']['API_KEY'] = glassnode_row['Value'].iloc[0]
            GLASSNODE_API_KEY = keys['GLASSNODE']['API_KEY']  # Set global variable
            
        API_KEY = keys['GLASSNODE']['API_KEY']
        
        # Create a dummy key.yaml file for backward compatibility
        # with imported modules that might still be looking for it
        current_dir = os.path.dirname(os.path.abspath(__file__))
        config_dir = os.path.join(current_dir, 'config')
        os.makedirs(config_dir, exist_ok=True)
        key_yaml_path = os.path.join(config_dir, 'key.yaml')
        
        # Only create the file if it doesn't exist
        if not os.path.exists(key_yaml_path):
            # Create a dictionary with the data from Excel
            key_data = {
                'EXCHANGE': selected_exchange,
                'BYBIT': {
                    'apiKey': BYBIT_API_KEY,
                    'secret': BYBIT_SECRET,
                    'live': keys['BYBIT']['live']
                },
                'GLASSNODE': {
                    'API_KEY': GLASSNODE_API_KEY
                }
            }
            
            # Write to yaml file
            with open(key_yaml_path, 'w') as f:
                yaml.dump(key_data, f)
            print(f"Created temporary key.yaml for backward compatibility.")
        
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        print("Falling back to default configuration")
        # Fallback to defaults if Excel file can't be read
        selected_exchange = 'BYBIT'
        keys = {
            'BYBIT': {
                'apiKey': '',
                'secret': '',
                'live': 'N'
            },
            'GLASSNODE': {
                'API_KEY': ''
            }
        }
        BYBIT_API_KEY = ''
        BYBIT_SECRET = ''
        BYBIT_TESTNET = True
        GLASSNODE_API_KEY = ''
        API_KEY = ''

def initialize_bybit_exchange():
    """
    Initialize and return a configured Bybit exchange instance.
    Returns:
        ccxt.bybit: Configured Bybit exchange instance
    """
    EXCHANGE = ccxt.bybit({
        'apiKey': BYBIT_API_KEY,  # Use global variable
        'secret': BYBIT_SECRET,   # Use global variable
        'enableRateLimit': True,
        'options': {
            'recvWindow': 10000,
            'adjustForTimeDifference': True
        },
    })
    if BYBIT_TESTNET:  # Use global variable
        EXCHANGE.enable_demo_trading(True)
    return EXCHANGE


# Initialize globals only at runtime, not at module import time
# initialize_globals()
# initialize_bybit_exchange()

# Make a monkey patch for modules that import key.yaml directly
def monkey_patch_module_imports():
    # This function monkey patches imported modules to use our global credentials
    try:
        # Import the modules
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        try:
            import optimized_data_extraction
            import bybit_direct_api
            
            # Patch the optimized_data_extraction module
            if hasattr(optimized_data_extraction, 'initialize_credentials'):
                optimized_data_extraction.initialize_credentials(
                    api_key=BYBIT_API_KEY,
                    api_secret=BYBIT_SECRET,
                    testnet=BYBIT_TESTNET
                )
            
            # Patch the bybit_direct_api module
            if hasattr(bybit_direct_api, 'initialize_credentials'):
                bybit_direct_api.initialize_credentials(
                    api_key=BYBIT_API_KEY,
                    api_secret=BYBIT_SECRET,
                    testnet=BYBIT_TESTNET
                )
                
            print("Successfully initialized modules with credentials from Excel file")
            
        except ImportError as e:
            print(f"Warning: Could not import all modules for patching: {e}")
            
    except Exception as e:
        print(f"Error in monkey patching: {e}")

pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)

# Constants
# STRATEGY_TYPES = ['long_only', 'short_only', 'long_short']
STRATEGY_TYPES = ['long_short']
STRATEGY_TYPE_DESC = {'long_only': 'Long Only', 'short_only': 'Short Only', 'long_short': 'Long-Short'}
# STRATEGY_STYLES = ['momentum', 'reversion']
STRATEGY_STYLES = ['momentum']
STRATEGY_STYLE_DESC = {'momentum': 'Momentum', 'reversion': 'Mean Reversion'}

# Full backtesting period
# since = 1589126400  # 2020 May 11
# since = 1589126400
# until = int(time.time())  # until now

# 全局變數設定
SHIFT_PERIODS = 1  # 預設值為 1，可以根據需要調整

# 設定walk-forward比例
DEFAULT_TRAIN_RATIO = 0.7  # default 70% training period, 30% testing period

# resolution: 1m,3m,5m,15m,30m,1h,2h,4h,6h,12h,1d
# resolution = "1h"
resolution = "1h"

# input api url and metric key
# api_url = "https://api.glassnode.com/v1/metrics/market/price_usd_close"
api_url = "cex_close"  # Changed back to cex_close for BTC close price data
# metric_key = "2020"
metric_key = "/"
underlying = "BTC"  # Changed to BTC
cex_symbol = "BTCUSDT"  # Set to BTCUSDT for BTC
# Calculate since timestamp (2 years ago by default)
# since = int(time.time()) - (2 * 365 * 24 * 60 * 60)  # 2 years ago in seconds
since = 1640995200  # January 1, 2022 00:00:00 UTC

print(f"Using since date: {datetime.datetime.fromtimestamp(since).strftime('%Y-%m-%d %H:%M:%S')}")

factor_name = api_url.split('/')[-1].replace('_', ' ').title()

# Data fetching moved to extract_and_save_data function
# This will now happen in the __main__ section at the bottom of the file

# Initialize Global Variables
# These will be properly defined in the __main__ section when the script is run directly
df = None
df_value = None
df_price = None

# Commented out previous data fetching code

# Fetch OHLCV data from Bybit

# if api_url == 'cex':
#     EXCHANGE = initialize_bybit_exchange()
#     
#     # Calculate timestamp for x years ago
#     end_time = int(time.time() * 1000)  # Current time in milliseconds
#     start_time = end_time - (data_year_ago * 365 * 24 * 60 * 60 * 1000)  #x years ago in milliseconds
#     
#     # Initialize empty list to store all OHLCV data
#     all_ohlcv = []
#     
#     # Fetch data in chunks to handle potential limits
#     current_start = start_time
#     print(current_start)
#     while current_start < end_time:
#         try:
#             chunk = EXCHANGE.fetch_ohlcv(cex_symbol, resolution, since=current_start, limit=1000)
#             if not chunk or len(chunk) == 0:
#                 break
#                 
#             all_ohlcv.extend(chunk)
#             current_start = chunk[-1][0] + 1  # Start from next timestamp after last data point
#             
#             # Add a small delay to avoid rate limits
#             time.sleep(0.1)
#             
#         except Exception as e:
#             print(f"Error fetching data chunk: {e}")
#             break
#     
#     if not all_ohlcv:
#         print(f"Error fetching OHLCV data from Bybit")
#     else:
#         print(f"Successfully fetched {len(all_ohlcv)} data points from Bybit")
# 
#     # Create dataframe from OHLCV data
#     df = pd.DataFrame(all_ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
#     df['t'] = pd.to_datetime(df['timestamp'], unit='ms')
# 
#     # Create value dataframe (using close price as the value)
#     df_value = pd.DataFrame({
#         't': df['t'],
#         'value': df['close']
#     })
# 
#     print(f"Data range: {df_value['t'].min()} to {df_value['t'].max()}")
# 
#     # Create price dataframe (also using close price)
#     df_price = pd.DataFrame({
#         't': df['t'],
#         'value': df['close']
#     })
# else:
#     res = requests.get(api_url,
#                        params={"a": underlying, "s": since, "u": until, "api_key": API_KEY, "i": resolution})
#     data = res.json()
# 
#     df_value = process_glassnode_data(data, metric_key=metric_key)
# 
#     # underlying
#     res = requests.get("https://api.glassnode.com/v1/metrics/market/price_usd_close",
#                        params={"a": underlying, "s": since, "u": until, "api_key": API_KEY, "i": resolution})
#     data = res.json()
#     df_price = process_glassnode_data(data)


# 數據處理
def prepare_data(df_value, df_price):
    df = pd.merge(df_value, df_price, how='inner', on='t')
    df = df.rename(columns={'value_x': 'value', 'value_y': 'price'})
    df['percentage_change'] = df['price'].pct_change()
    df['bnh_cumu'] = df['percentage_change'].cumsum()
    return df

# resolution: 1m,3m,5m,15m,30m,1h,2h,4h,6h,12h,1d
def get_time_variable(resolution):
    return {
        '1d': 365,           # 365 periods per year
        '24h': 365,          # 365 periods per year
        '12h': 365 * 2,      # 730 periods per year
        '6h': 365 * 4,       # 1460 periods per year
        '4h': 365 * 6,       # 2190 periods per year
        '2h': 365 * 12,      # 4380 periods per year
        '1h': 365 * 24,      # 8760 periods per year
        '15m': 365 * 24 * 4, # 35040 periods per year
        '10m': 365 * 24 * 6, # 52560 periods per year
        '5m': 365 * 24 * 12, # 105120 periods per year
        '3m': 365 * 24 * 20, # 175200 periods per year
        '1m': 365 * 24 * 60  # 525600 periods per year
    }.get(resolution, 365)  # Default to daily if resolution not found


def bt_analysis(pos_col, strategy_type):
    # 每次分析都用新嘅 DataFrame，避免受之前嘅策略影響
    temp_df = df.copy()

    temp_df['pos_t-1'] = temp_df[pos_col].shift(SHIFT_PERIODS)
    temp_df['trade'] = abs(temp_df['pos_t-1'] - temp_df[pos_col])
    temp_df['pnl'] = temp_df['pos_t-1'] * temp_df['percentage_change'] - temp_df['trade'] * 0.05 / 100
    temp_df['cumu'] = temp_df['pnl'].cumsum()
    temp_df['dd'] = temp_df['cumu'].cummax() - temp_df['cumu']

    ar = round(temp_df['pnl'].mean() * time_variable, 3)
    sr = round(temp_df['pnl'].mean() / temp_df['pnl'].std() * np.sqrt(time_variable), 3) if temp_df[
                                                                                                 'pnl'].std() != 0 else np.nan
    mdd = temp_df['dd'].max()
    cr = round(ar / mdd, 3) if mdd != 0 else np.nan

    return pd.Series([sr, cr, mdd, ar], index=['sr', 'cr', 'mdd', 'ar'])


def optimize_strategy(strategy_func, window_range, threshold_range, strategy_type, style='momentum'):
    results = []
    total_iterations = len(window_range) * len(threshold_range)

    # 獲取當前正在處理嘅模型名稱
    model_name = None
    for name, params in base_params.items():
        if params['func'] == strategy_func:
            model_name = name
            break
    
    # 用 tqdm 包裝雙重循環，顯示模型名稱
    progress_desc = f"優化 {model_name} ({STRATEGY_TYPE_DESC[strategy_type]}, {STRATEGY_STYLE_DESC[style]})"
    with tqdm(total=total_iterations, desc=progress_desc, disable=not PROGRESS_BAR) as pbar:
        for window in window_range:
            for threshold in threshold_range:
                try:
                    df['pos'] = strategy_func(df, window, threshold, strategy_type, style)
                    if df['pos'].isna().all():
                        pbar.update(1)
                        continue
                    result = bt_analysis('pos', strategy_type)
                    results.append([window, threshold] + list(result))
                except Exception as e:
                    pass
                pbar.update(1)

    return pd.DataFrame(results, columns=['window', 'threshold', 'sr', 'cr', 'mdd', 'ar'])


def get_window_range(resolution, base_range):
    """根據 resolution 調整 window range"""
    multiplier = {
        '24h': 1,      # 1 period per day
        '12h': 2,      # 2 periods per day
        '6h': 4,       # 4 periods per day
        '4h': 6,       # 6 periods per day
        '2h': 12,      # 12 periods per day
        '1h': 24,      # 24 periods per day
        '15m': 96,     # 24 * 4 periods per day
        '10m': 144,    # 24 * 6 periods per day
        '5m': 288,     # 24 * 12 periods per day
        '3m': 480,     # 24 * 20 periods per day
        '1m': 1440,    # 24 * 60 periods per day
    }.get(resolution, 1)

    return np.arange(
        base_range[0] * multiplier,
        base_range[1] * multiplier,
        base_range[2] * multiplier
    )


########### optimasation Zone ###########
# 從models.py導入base_params
base_params = get_base_params()

strategy_params = {
    name: {
        'func': params['func'],
        'window_range': get_window_range(resolution, params['base_window']),
        'threshold_range': (
            get_window_range(resolution, params['base_threshold'])
            if name == 'MA Cross'
            else params['threshold_range']
        )
    }
    for name, params in base_params.items()
}

# 喺回測分析之前加入呢啲代碼
# Note: We'll only prepare_data inside __main__, not at the module level
# This line is removed: df = prepare_data(df_value, df_price) 
time_variable = get_time_variable(resolution)

# 策略類型嘅說明 (全局變量)
STRATEGY_TYPE_DESC = {
    'long_only': 'Long Only',
    'short_only': 'Short Only',
    'long_short': 'Long-Short'
}


def run_strategy(mode='optimize', strategy_name=None, window=None, threshold=None, strategy_type='long_only',
                 style='momentum', train_ratio=DEFAULT_TRAIN_RATIO):
    if mode == 'optimize':
        results = {}
        best_results = []
        all_good_factors = []

        # 先做 momentum
        # print("\n=== Momentum Strategies ===")
        for strategy_type in STRATEGY_TYPES:
            # print(f"\n=== Optimizing Momentum {STRATEGY_TYPE_DESC[strategy_type]} ===")
            total_models = len(strategy_params)
            with tqdm(total=total_models, desc=f"處理 Momentum {STRATEGY_TYPE_DESC[strategy_type]} 策略", disable=not PROGRESS_BAR) as model_pbar:
                for name, params in strategy_params.items():
                    # print(f"\n開始優化 {name} 策略...")
                    result_df = optimize_strategy(
                        params['func'],
                        params['window_range'],
                        params['threshold_range'],
                        strategy_type,
                        'momentum'
                    ).sort_values('sr', ascending=False)

                    results[f"{name}_{strategy_type}_momentum"] = result_df

                    if not result_df.empty:
                        best_row = result_df.iloc[0]
                        best_results.append({
                            'Strategy': f"{name} (Momentum {STRATEGY_TYPE_DESC[strategy_type]})",
                            'Window': best_row['window'],
                            'Threshold': best_row['threshold'],
                            'sr': best_row['sr'],
                            'cr': best_row['cr'],
                            'mdd': best_row['mdd'],
                            'ar': best_row['ar']
                        })
                    model_pbar.update(1)

        # 再做 reversion
        print("\n=== Mean Reversion Strategies ===")
        for strategy_type in STRATEGY_TYPES:
            # print(f"\n=== Optimizing Mean Reversion {STRATEGY_TYPE_DESC[strategy_type]} ===")
            total_models = len(strategy_params)
            with tqdm(total=total_models, desc=f"處理 Mean Reversion {STRATEGY_TYPE_DESC[strategy_type]} 策略", disable=not PROGRESS_BAR) as model_pbar:
                for name, params in strategy_params.items():
                    # print(f"\n開始優化 {name} 策略...")
                    result_df = optimize_strategy(
                        params['func'],
                        params['window_range'],
                        params['threshold_range'],
                        strategy_type,
                        'reversion'
                    ).sort_values('sr', ascending=False)

                    results[f"{name}_{strategy_type}_reversion"] = result_df

                    if not result_df.empty:
                        best_row = result_df.iloc[0]
                        best_results.append({
                            'Strategy': f"{name} (Mean Reversion {STRATEGY_TYPE_DESC[strategy_type]})",
                            'Window': best_row['window'],
                            'Threshold': best_row['threshold'],
                            'sr': best_row['sr'],
                            'cr': best_row['cr'],
                            'mdd': best_row['mdd'],
                            'ar': best_row['ar']
                        })
                    model_pbar.update(1)

        # 先顯示所有策略嘅最佳結果同好Factor總結
        print("\n=== 所有策略嘅最佳結果 (按 Sharpe Ratio 排序) ===")
        if best_results:
            best_results_df = pd.DataFrame(best_results)
            best_results_df['Window'] = best_results_df['Window'].astype(int)
            best_results_df = best_results_df[['Strategy', 'sr', 'cr', 'mdd', 'ar', 'Window', 'Threshold']]
            best_results_df = best_results_df.sort_values('sr', ascending=False)

            # 打印最佳結果
            print_formatted_results(best_results_df)
        else:
            print("\n未找到任何結果")

        # 畫熱力圖並收集好factors
        plot_titles = [
            ('momentum', 'long_only', 'Momentum Long Only'),
            ('momentum', 'short_only', 'Momentum Short Only'),
            ('momentum', 'long_short', 'Momentum Long-Short'),
            ('reversion', 'long_only', 'Mean Reversion Long Only'),
            ('reversion', 'short_only', 'Mean Reversion Short Only'),
            ('reversion', 'long_short', 'Mean Reversion Long-Short')
        ]

        # 先收集所有好factors（唔顯示熱力圖）
        for style, strategy_type, title in plot_titles:
            current_results = {k: v for k, v in results.items()
                               if strategy_type in k and style in k}
            if current_results:
                good_factors = collect_good_factors(current_results, title)  # 新函數，只收集好factors
                if good_factors:
                    all_good_factors.extend(good_factors)

        # 顯示好Factor總結
        if all_good_factors:
            print("\n=== 好Factor總結 (按 Sharpe Ratio 排序) ===")
            print("條件: Sharpe > 1.5, CR > 1.8, CR > Sharpe")
            df_summary = pd.DataFrame(all_good_factors)
            df_summary['Window'] = df_summary['Window'].astype(int)
            df_summary = df_summary[['Strategy', 'sr', 'cr', 'mdd', 'ar', 'Window', 'Threshold']]
            df_summary = df_summary.sort_values('sr', ascending=False)

            # 打印好Factor總結
            print_formatted_results(df_summary)
        else:
            print("\n冇搵到好嘅factors (Sharpe > 1.5, CR > 1.8, CR > Sharpe)")

        # 最後顯示熱力圖
        for style, strategy_type, title in plot_titles:
            current_results = {k: v for k, v in results.items()
                               if strategy_type in k and style in k}
            if current_results:
                plot_strategy_heatmaps(current_results, title, print_summary=False)

    elif mode == 'walkforward':
        results = {}
        all_good_factors = []

        # 分割數據為訓練集同測試集
        split_idx = int(len(df) * train_ratio)
        train_df = df.iloc[:split_idx].copy()
        test_df = df.iloc[split_idx:].copy()

        # 先做 momentum
        for strategy_type in STRATEGY_TYPES:
            total_models = len(strategy_params)
            with tqdm(total=total_models, desc=f"Walk-Forward: Momentum {STRATEGY_TYPE_DESC[strategy_type]}", disable=not PROGRESS_BAR) as model_pbar:
                for name, params in strategy_params.items():
                    # 優化期結果
                    train_results = walkforward_optimize_strategy(
                        params['func'],
                        params['window_range'],
                        params['threshold_range'],
                        strategy_type,
                        'momentum',
                        train_df,
                        period_type="Training Period"
                    )

                    # 測試期結果
                    test_results = walkforward_optimize_strategy(
                        params['func'],
                        params['window_range'],
                        params['threshold_range'],
                        strategy_type,
                        'momentum',
                        test_df,
                        period_type="Testing Period"
                    )

                    # 合併結果
                    combined_results = walkforward_combine_results(train_results, test_results)

                    if not combined_results.empty:
                        results[f"{name}_{strategy_type}_momentum"] = combined_results
                    
                    model_pbar.update(1)

        # 再做 reversion
        for strategy_type in STRATEGY_TYPES:
            total_models = len(strategy_params)
            with tqdm(total=total_models, desc=f"Walk-Forward: Mean Reversion {STRATEGY_TYPE_DESC[strategy_type]}", disable=not PROGRESS_BAR) as model_pbar:
                for name, params in strategy_params.items():
                    # 優化期結果
                    train_results = walkforward_optimize_strategy(
                        params['func'],
                        params['window_range'],
                        params['threshold_range'],
                        strategy_type,
                        'reversion',
                        train_df,
                        period_type="Training Period"
                    )

                    # 測試期結果
                    test_results = walkforward_optimize_strategy(
                        params['func'],
                        params['window_range'],
                        params['threshold_range'],
                        strategy_type,
                        'reversion',
                        test_df,
                        period_type="Testing Period"
                    )

                    # 合併結果
                    combined_results = walkforward_combine_results(train_results, test_results)

                    if not combined_results.empty:
                        results[f"{name}_{strategy_type}_reversion"] = combined_results

                    model_pbar.update(1)

        # 畫熱力圖並收集好factors
        plot_titles = [
            ('momentum', 'long_only', 'Momentum Long Only'),
            ('momentum', 'short_only', 'Momentum Short Only'),
            ('momentum', 'long_short', 'Momentum Long-Short'),
            ('reversion', 'long_only', 'Mean Reversion Long Only'),
            ('reversion', 'short_only', 'Mean Reversion Short Only'),
            ('reversion', 'long_short', 'Mean Reversion Long-Short')
        ]

        # 先收集所有好factors（唔顯示熱力圖）
        for style, strategy_type, title in plot_titles:
            current_results = {k: v for k, v in results.items()
                               if strategy_type in k and style in k}
            if current_results:
                good_factors = walkforward_collect_good_factors(current_results, title)
                if good_factors:
                    all_good_factors.extend(good_factors)

        # 顯示好Factor總結
        if all_good_factors:
            print("\n=== Walk Forward 好Factor總結 (按 Sharpe Ratio 排序) ===")
            print("條件: 優化期同測試期都要 SR>1.5, CR>1.8, CR>SR")
            df_summary = pd.DataFrame(all_good_factors)
            df_summary['Window'] = df_summary['Window'].astype(int)
            df_summary = df_summary[['Strategy', 'train_sr', 'train_cr', 'test_sr', 'test_cr', 'Window', 'Threshold']]
            df_summary = df_summary.sort_values('train_sr', ascending=False)

            # 打印好Factor總結
            walkforward_print_formatted_results(df_summary)
        else:
            print("\n冇搵到好嘅factors (優化期同測試期都要 SR>1.5, CR>1.8, CR>SR)")

        # 最後顯示熱力圖
        for style, strategy_type, title in plot_titles:
            current_results = {k: v for k, v in results.items()
                               if strategy_type in k and style in k}
            if current_results:
                walkforward_plot_strategy_heatmaps(current_results, title, print_summary=False, train_df=train_df,
                                                   test_df=test_df)

    elif mode == 'backtest':
        if any(x is None for x in [strategy_name, window, threshold]):
            raise ValueError("Backtest mode 需要提供 strategy_name, window 同 threshold")
        plot_backtest_results(strategy_name, window, threshold, strategy_type, style, train_ratio)


# 修改熱力圖函數
def plot_strategy_heatmaps(results, title_suffix, print_summary=True):
    # 確保關閉所有現有圖形，避免之前嘅圖形影響
    plt.close('all')

    num_strategies = len(results)
    rows = (num_strategies + 2) // 3
    cols = min(3, num_strategies)

    fig = plt.figure(figsize=(17, 5 * rows))

    title_text = f'Factor: {factor_name}'
    if metric_key and metric_key != "/":
        title_text += f' ({metric_key})'

    fig.text(0.5, 0.95, title_text, ha='center', va='bottom', fontsize=16, weight='bold')
    fig.text(0.5, 0.92, f'Strategy Type: {title_suffix}', ha='center', va='bottom', fontsize=14)

    # 加入紅框條件提示 footnote - 移到最右上角
    footnote_text = "Red box: SR>1.5, CR>1.8, CR>SR"
    fig.text(0.99, 0.99, footnote_text, ha='right', va='top', fontsize=10, style='italic')

    good_factors = []  # 用嚟收集好嘅factors

    for idx, (name, result) in enumerate(results.items()):
        if result.empty:
            continue

        # 檢查結果 DataFrame 中是否有 'sr_train' 欄位，如果沒有則使用 'sr'
        sr_col = 'sr_train' if 'sr_train' in result.columns else 'sr'
        cr_col = 'cr_train' if 'cr_train' in result.columns else 'cr'
        mdd_col = 'mdd_train' if 'mdd_train' in result.columns else 'mdd'
        ar_col = 'ar_train' if 'ar_train' in result.columns else 'ar'

        ax = plt.subplot(rows, cols, idx + 1)
        pivot_table = result.assign(threshold=result['threshold'].round(3)).pivot(
            index='window', columns='threshold', values=sr_col
        )

        # 創建一個同樣大細嘅DataFrame嚟標記好嘅factors
        good_factors_mask = pd.DataFrame(False, index=pivot_table.index, columns=pivot_table.columns)

        # 檢查每個位置係咪好factor
        for window in pivot_table.index:
            for threshold in pivot_table.columns:
                # 首先確保呢個位置有sr值
                if pd.isna(pivot_table.loc[window, threshold]):
                    continue

                sr = pivot_table.loc[window, threshold]

                # 搵返對應嘅CR值，加入錯誤處理
                matching_rows = result[(result['window'] == window) &
                                       (result['threshold'] == threshold)]

                if matching_rows.empty or pd.isna(matching_rows[cr_col].iloc[0]):
                    continue

                cr = matching_rows[cr_col].iloc[0]
                mdd = matching_rows[mdd_col].iloc[0]
                ar = matching_rows[ar_col].iloc[0]

                # 判斷係咪好factor (SR > 1.5, CR > 1.8, CR > SR)
                if sr > 1.5 and cr > 1.8 and cr > sr:
                    good_factors_mask.loc[window, threshold] = True
                    # 提取策略類型同風格
                    name_parts = name.split('_')
                    strategy_base_name = name_parts[0]

                    # 正確處理策略類型
                    if 'long_only' in name:
                        strategy_type = 'long_only'
                    elif 'short_only' in name:
                        strategy_type = 'short_only'
                    else:
                        strategy_type = 'long_short'

                    # 正確處理策略風格
                    strategy_style = 'momentum' if 'momentum' in name else 'reversion'

                    # 組合完整策略名稱
                    full_strategy_name = f"{strategy_base_name} ({STRATEGY_STYLE_DESC[strategy_style]} {STRATEGY_TYPE_DESC[strategy_type]})"

                    good_factors.append({
                        'Strategy': full_strategy_name,
                        'Window': window,
                        'Threshold': threshold,
                        'sr': sr,
                        'cr': cr,
                        'mdd': mdd,
                        'ar': ar
                    })

        # 畫熱力圖
        sns.heatmap(pivot_table, ax=ax, cmap='Greens', center=0,
                    annot=True, fmt='.2f', annot_kws={'size': 7}, cbar=False)

        # 為好嘅factors加紅框
        for window in pivot_table.index:
            for threshold in pivot_table.columns:
                if good_factors_mask.loc[window, threshold]:
                    ax.add_patch(plt.Rectangle(
                        (good_factors_mask.columns.get_loc(threshold),
                         good_factors_mask.index.get_loc(window)),
                        1, 1, fill=False, edgecolor='red', linewidth=2
                    ))

        strategy_name = name.split('_')[0]
        ax.set_title(strategy_name, pad=10)  # 只顯示策略名
        ax.set_xlabel('Threshold')
        ax.set_ylabel('Window')
        ax.tick_params(labelsize=7)
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

    plt.tight_layout()
    plt.subplots_adjust(top=0.88)

    # 根據 SAVE_FIGURES 設置決定是儲存圖片還是顯示圖片
    if SAVE_FIGURES:
        # 創建 backtest_result 目錄（如果不存在）
        os.makedirs("backtest_result", exist_ok=True)
        
        # 創建特定因子嘅目錄
        factor_dir = f"backtest_result/{factor_name.replace(' ', '_')}"
        os.makedirs(factor_dir, exist_ok=True)
        
        # 添加好 factors 數量指標
        good_factor_count = len(good_factors)
        good_factor_indicator = f"_{good_factor_count}_good_factors" if good_factor_count > 0 else ""
        
        # 添加 metric_key 到文件名（如果有）
        metric_suffix = f"_{metric_key}" if metric_key and metric_key != "/" else ""
        
        # 策略類型同風格嘅簡短命名
        short_title = title_suffix.replace('Momentum', 'M').replace('Reversion', 'R').replace('Long Only', 'L').replace('Short Only', 'S').replace('Long-Short', 'LS')
        
        # 創建詳細嘅文件名
        filename = f"{factor_dir}/heatmap_{underlying}_{resolution}{metric_suffix}_{short_title.replace(' ', '_').replace('-', '_')}{good_factor_indicator}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close(fig)
        print(f"儲存熱力圖為 {filename}")
    else:
        # 顯示圖片
        plt.show()

    # 改為返回good_factors而唔係print
    if not print_summary:
        return good_factors

    # 如果需要print，就保持原有嘅print邏輯
    if good_factors:
        print("\n=== 好Factor總結 (按 Sharpe Ratio 排序) ===")

        # 將好factors轉換成DataFrame並排序
        df_summary = pd.DataFrame(good_factors)
        df_summary = df_summary[['Strategy', 'sr', 'cr', 'mdd', 'ar', 'Window', 'Threshold']]
        df_summary = df_summary.sort_values('sr', ascending=False)

        # 設定header格式
        header_formats = {
            'Strategy': '{:<40}',
            'sr': '{:<8}',
            'cr': '{:<8}',
            'mdd': '{:<8}',
            'ar': '{:<8}',
            'Window': '{:<8}',
            'Threshold': '{:<8}'
        }

        # 設定數據格式
        data_formats = {
            'Strategy': '{:<40}',
            'sr': '{:<8.3f}',
            'cr': '{:<8.3f}',
            'mdd': '{:<8.3f}',
            'ar': '{:<8.3f}',
            'Window': '{:<8d}',
            'Threshold': '{:<8.3f}'
        }

        headers = ['Strategy', 'sr', 'cr', 'mdd', 'ar', 'Window', 'Threshold']
        print(''.join(header_formats[col].format(col) for col in headers))
        print('-' * 88)

        for _, row in df_summary.iterrows():
            print(''.join(data_formats[col].format(row[col]) for col in headers))
    else:
        print("\n冇搵到好嘅factors (sr>1.5，cr>1.8，同時cr>sr)")

    return good_factors  # 都係要返回good_factors


######### Backtest Zone ###########
def plot_backtest_results(strategy_name, window, threshold, strategy_type, style, train_ratio=DEFAULT_TRAIN_RATIO):
    # 確保關閉所有現有圖形，避免之前嘅圖形影響
    plt.close('all')

    # 創建新圖形
    plt.figure(figsize=(12, 8))

    temp_df = df.copy()

    # 只計算指定嘅策略風格
    temp_df['pos'] = strategy_params[strategy_name]['func'](
        temp_df,
        window,
        threshold,
        strategy_type,
        style
    )

    pos_t_1 = temp_df['pos'].shift(SHIFT_PERIODS)
    trade = abs(pos_t_1 - temp_df['pos'])
    pnl = pos_t_1 * temp_df['percentage_change'] - trade * 0.05 / 100
    temp_df['cumu'] = pnl.cumsum()
    temp_df['bnh_cumu'] = temp_df['percentage_change'].cumsum()

    # Calculate performance metrics for full period
    sr = round(pnl.mean() / pnl.std() * np.sqrt(time_variable), 3) if pnl.std() != 0 else np.nan
    ar = round(pnl.mean() * time_variable, 3)
    dd = temp_df['cumu'].cummax() - temp_df['cumu']
    mdd = round(dd.max(), 6)
    cr = round(ar / mdd, 3) if mdd != 0 else np.nan

    # 分割數據為 Training Period 同 Testing Period，使用傳入嘅 train_ratio 參數
    train_size = int(len(temp_df) * train_ratio)
    train_df = temp_df.iloc[:train_size].copy()
    test_df = temp_df.iloc[train_size:].copy()

    # 計算 Training Period 嘅表現指標
    train_pnl = train_df['percentage_change'] * train_df['pos'].shift(SHIFT_PERIODS) - abs(
        train_df['pos'].shift(SHIFT_PERIODS) - train_df['pos']) * 0.05 / 100
    train_sr = round(train_pnl.mean() / train_pnl.std() * np.sqrt(time_variable), 3) if train_pnl.std() != 0 else np.nan
    train_ar = round(train_pnl.mean() * time_variable, 3)
    train_dd = train_df['cumu'].cummax() - train_df['cumu']
    train_mdd = round(train_dd.max(), 6)
    train_cr = round(train_ar / train_mdd, 3) if train_mdd != 0 else np.nan

    # 計算 Testing Period 嘅表現指標
    test_pnl = test_df['percentage_change'] * test_df['pos'].shift(SHIFT_PERIODS) - abs(
        test_df['pos'].shift(SHIFT_PERIODS) - test_df['pos']) * 0.05 / 100
    test_sr = round(test_pnl.mean() / test_pnl.std() * np.sqrt(time_variable), 3) if test_pnl.std() != 0 else np.nan
    test_ar = round(test_pnl.mean() * time_variable, 3)
    test_dd = test_df['cumu'].cummax() - test_df['cumu']
    test_mdd = round(test_dd.max(), 6)
    test_cr = round(test_ar / test_mdd, 3) if test_mdd != 0 else np.nan

    # 創建 Training Period 同 Testing Period 嘅獨立 equity curve
    # 將 Training Period 嘅 equity curve 重置為從 0 開始
    train_df['train_cumu'] = train_pnl.cumsum()

    # 將 Testing Period 嘅 equity curve 重置為從 0 開始
    test_df['test_cumu'] = test_pnl.cumsum()

    # 為 Testing Period 創建一個由零開始嘅 Buy & Hold curve
    test_df['test_bnh_cumu'] = test_df['percentage_change'].cumsum()

    # 使用 Matplotlib 創建圖表
    plt.plot(temp_df['t'], temp_df['cumu'], label=f'Full Period (SR={sr}, CR={cr})', color='blue', linewidth=2)

    # 繪製 Training Period 曲線
    plt.plot(train_df['t'], train_df['train_cumu'], label=f'Training Period (SR={train_sr}, CR={train_cr})',
             color='green', linewidth=2)

    # 繪製 Testing Period 曲線
    plt.plot(test_df['t'], test_df['test_cumu'], label=f'Testing Period (SR={test_sr}, CR={test_cr})', color='red',
             linewidth=2)

    # 繪製 Testing Period 嘅 Buy & Hold 曲線
    plt.plot(test_df['t'], test_df['test_bnh_cumu'], label='Testing Period Buy & Hold', color='orange', linestyle='--')

    # 繪製全時段 Buy & Hold 曲線
    plt.plot(temp_df['t'], temp_df['bnh_cumu'], label='Full Period Buy & Hold', color='gray', linestyle='--')

    # 添加垂直線標記訓練期和測試期的分界
    split_date = train_df['t'].iloc[-1]
    plt.axvline(x=split_date, color='black', linestyle='--')

    # 添加背景色區分訓練期和測試期
    plt.axvspan(temp_df['t'].iloc[0], split_date, alpha=0.1, color='green')
    plt.axvspan(split_date, temp_df['t'].iloc[-1], alpha=0.1, color='red')

    # 添加圖例到左上角
    plt.legend(loc='upper left')

    # 準備時間段資訊同表現指標
    period_info = (
        f"Training: {train_df['t'].iloc[0].strftime('%Y-%m-%d')} to {train_df['t'].iloc[-1].strftime('%Y-%m-%d')} ({train_ratio * 100:.0f}%) | "
        f"Testing: {test_df['t'].iloc[0].strftime('%Y-%m-%d')} to {test_df['t'].iloc[-1].strftime('%Y-%m-%d')} ({(1 - train_ratio) * 100:.0f}%)"
    )

    metrics_info = (
        f"Training: SR={train_sr}, CR={train_cr}, MDD={train_mdd:.4f}, AR={train_ar} | "
        f"Testing: SR={test_sr}, CR={test_cr}, MDD={test_mdd:.4f}, AR={test_ar} | "
        f"Full: SR={sr}, CR={cr}, MDD={mdd:.4f}, AR={ar}"
    )

    # 添加標題 (靠左)，包括時間段資訊同表現指標，並將 factor 名稱加粗
    # 將 "Factor:" 和 factor 名稱加粗，使用更大的字體大小
    factor_title = f'Factor: {factor_name}'
    if metric_key and metric_key != "/":
        factor_title += f' ({metric_key})'

    style_desc = 'Momentum' if style == 'momentum' else 'Mean Reversion'

    # 先添加 factor 標題，使用較大的字體
    plt.suptitle(factor_title, fontsize=16, fontweight='bold', x=0.047, y=0.97, ha='left')

    # 再添加其他資訊，使用正常大小的字體
    plt.title(
        f'{strategy_name} ({style_desc} {STRATEGY_TYPE_DESC[strategy_type]}) | Window={window}, Threshold={threshold}\n'
        f'{period_info}\n{metrics_info}',
        fontsize=10, loc='left')

    plt.xlabel('Date')
    plt.ylabel('Cumulative Return')
    plt.grid(True, alpha=0.3)

    # 調整日期格式
    plt.gcf().autofmt_xdate()

    # 調整佈局，確保有足夠空間顯示圖表
    plt.tight_layout()

    # 根據 SAVE_FIGURES 設置決定是儲存圖片還是顯示圖片
    if SAVE_FIGURES:
        # 創建 backtest_result 目錄（如果不存在）
        os.makedirs("backtest_result", exist_ok=True)
        
        # 創建特定因子嘅目錄
        factor_dir = f"backtest_result/{factor_name.replace(' ', '_')}"
        os.makedirs(factor_dir, exist_ok=True)
        
        # 添加 metric_key 到文件名（如果有）
        metric_suffix = f"_{metric_key}" if metric_key and metric_key != "/" else ""
        
        # 簡短命名方案
        style_short = "M" if style == "momentum" else "R"
        type_short = {"long_only": "L", "short_only": "S", "long_short": "LS"}[strategy_type]
        
        # 儲存圖片到因子目錄
        filename = f"{factor_dir}/backtest_{strategy_name}_{underlying}_{resolution}{metric_suffix}_{style_short}_{type_short}_w{window}_t{threshold}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"儲存回測結果圖為 {filename}")
    else:
        # 顯示圖片
        plt.show()


def print_formatted_results(df):
    """統一格式化打印結果"""
    header_formats = {
        'Strategy': '{:<40}',
        'sr': '{:<8}',
        'cr': '{:<8}',
        'mdd': '{:<8}',
        'ar': '{:<8}',
        'Window': '{:<8}',
        'Threshold': '{:<8}'
    }

    data_formats = {
        'Strategy': '{:<40}',
        'sr': '{:<8.3f}',
        'cr': '{:<8.3f}',
        'mdd': '{:<8.3f}',
        'ar': '{:<8.3f}',
        'Window': '{:<8d}',
        'Threshold': '{:<8.3f}'
    }

    headers = ['Strategy', 'sr', 'cr', 'mdd', 'ar', 'Window', 'Threshold']
    print(''.join(header_formats[col].format(col) for col in headers))
    print('-' * 88)

    for _, row in df.iterrows():
        print(''.join(data_formats[col].format(row[col]) for col in headers))


def collect_good_factors(results, title_suffix):
    """只收集好factors，唔顯示熱力圖"""
    good_factors = []

    for name, result in results.items():
        if result.empty:
            continue

        # 檢查結果 DataFrame 中是否有 'sr_train' 欄位，如果沒有則使用 'sr'
        sr_col = 'sr_train' if 'sr_train' in result.columns else 'sr'
        cr_col = 'cr_train' if 'cr_train' in result.columns else 'cr'
        mdd_col = 'mdd_train' if 'mdd_train' in result.columns else 'mdd'
        ar_col = 'ar_train' if 'ar_train' in result.columns else 'ar'

        pivot_table = result.assign(threshold=result['threshold'].round(3)).pivot(
            index='window', columns='threshold', values=sr_col
        )

        for window in pivot_table.index:
            for threshold in pivot_table.columns:
                if pd.isna(pivot_table.loc[window, threshold]):
                    continue

                sr = pivot_table.loc[window, threshold]
                matching_rows = result[(result['window'] == window) &
                                       (result['threshold'] == threshold)]

                if matching_rows.empty:
                    continue

                cr = matching_rows[cr_col].iloc[0]
                mdd = matching_rows[mdd_col].iloc[0]
                ar = matching_rows[ar_col].iloc[0]

                if sr > 1.5 and cr > 1.8 and cr > sr:
                    strategy_base_name = name.split('_')[0]
                    strategy_type = ('long_only' if 'long_only' in name
                                     else 'short_only' if 'short_only' in name
                    else 'long_short')
                    strategy_style = 'momentum' if 'momentum' in name else 'reversion'

                    full_strategy_name = f"{strategy_base_name} ({STRATEGY_STYLE_DESC[strategy_style]} {STRATEGY_TYPE_DESC[strategy_type]})"

                    good_factors.append({
                        'Strategy': full_strategy_name,
                        'Window': window,
                        'Threshold': threshold,
                        'sr': sr,
                        'cr': cr,
                        'mdd': mdd,
                        'ar': ar
                    })

    return good_factors


def walk_forward_analysis(strategy_name, window, threshold, strategy_type='long_only', style='momentum',
                          n_segments=5, train_ratio=DEFAULT_TRAIN_RATIO):
    """
    執行 Walk Forward Analysis

    參數:
    - strategy_name: 策略名稱
    - window: 窗口大小
    - threshold: 閾值
    - strategy_type: 策略類型 ('long_only', 'short_only', 'long_short')
    - style: 策略風格 ('momentum', 'reversion')
    - n_segments: 分段數量
    - train_ratio: 訓練期佔每個分段嘅百分比
    """
    # 複製原始數據
    temp_df = df.copy()

    # 計算每個分段嘅大小
    segment_size = len(temp_df) // n_segments

    # 儲存每個測試期嘅結果
    test_results = []

    # 創建一個新嘅 DataFrame 嚟儲存所有測試期嘅結果
    wfa_df = pd.DataFrame()

    # 計算測試期佔比
    test_pct = 1 - train_ratio

    # 遍歷每個分段
    for i in range(n_segments):
        # 計算當前分段嘅起始同結束索引
        start_idx = i * segment_size
        end_idx = (i + 1) * segment_size if i < n_segments - 1 else len(temp_df)

        # 計算優化期同測試期嘅分界點
        split_idx = start_idx + int((end_idx - start_idx) * train_ratio)

        # 分割優化期同測試期
        train_df = temp_df.iloc[start_idx:split_idx].copy()
        test_df = temp_df.iloc[split_idx:end_idx].copy()

        # 喺優化期入面搵最佳參數 (呢度可以加入優化邏輯，或者直接用傳入嘅參數)
        # 簡化起見，我哋直接用傳入嘅參數

        # 喺測試期入面應用策略
        test_df['pos'] = strategy_params[strategy_name]['func'](
            test_df, window, threshold, strategy_type, style
        )

        # 計算測試期嘅表現
        test_df['pos_t-1'] = test_df['pos'].shift(SHIFT_PERIODS)
        test_df['trade'] = abs(test_df['pos_t-1'] - test_df['pos'])
        test_df['pnl'] = test_df['pos_t-1'] * test_df['percentage_change'] - test_df['trade'] * 0.05 / 100
        test_df['cumu'] = test_df['pnl'].cumsum()

        # 計算測試期嘅表現指標
        sr = round(test_df['pnl'].mean() / test_df['pnl'].std() * np.sqrt(time_variable), 3) if test_df[
                                                                                                    'pnl'].std() != 0 else np.nan
        ar = round(test_df['pnl'].mean() * time_variable, 3)
        dd = test_df['cumu'].cummax() - test_df['cumu']
        mdd = round(dd.max(), 6)
        cr = round(ar / mdd, 3) if mdd != 0 else np.nan

        # 儲存測試期嘅結果
        test_results.append({
            'Segment': i + 1,
            'Start Date': test_df['t'].iloc[0].strftime('%Y-%m-%d'),
            'End Date': test_df['t'].iloc[-1].strftime('%Y-%m-%d'),
            'SR': sr,
            'CR': cr,
            'MDD': mdd,
            'AR': ar
        })

        # 將測試期嘅結果加入到 wfa_df
        test_df['segment'] = i + 1
        wfa_df = pd.concat([wfa_df, test_df[['t', 'cumu', 'segment']]])

    # 顯示每個測試期嘅結果
    results_df = pd.DataFrame(test_results)
    print("\n=== Walk Forward Analysis 結果 ===")
    print(f"策略: {strategy_name} ({STRATEGY_STYLE_DESC[style]} {STRATEGY_TYPE_DESC[strategy_type]})")
    print(f"Window: {window}, Threshold: {threshold}")
    print(f"訓練期佔比: {train_ratio * 100:.0f}%, 測試期佔比: {test_pct * 100:.0f}%")
    print("\n")
    print(results_df.to_string(index=False))

    # 計算平均表現
    avg_sr = results_df['SR'].mean()
    avg_cr = results_df['CR'].mean()
    avg_mdd = results_df['MDD'].mean()
    avg_ar = results_df['AR'].mean()

    print("\n=== 平均表現 ===")
    print(f"平均 SR: {avg_sr:.3f}")
    print(f"平均 CR: {avg_cr:.3f}")
    print(f"平均 MDD: {avg_mdd:.6f}")
    print(f"平均 AR: {avg_ar:.3f}")

    # 畫出每個測試期嘅累積回報
    fig = px.line(wfa_df, x='t', y='cumu', color='segment',
                  title=f'Walk Forward Analysis - {strategy_name} ({STRATEGY_STYLE_DESC[style]} {STRATEGY_TYPE_DESC[strategy_type]})')
    fig.update_layout(
        xaxis_title='日期',
        yaxis_title='累積回報',
        legend_title='分段'
    )
    fig.show()

    return results_df, wfa_df


def walkforward_optimize_strategy(strategy_func, window_range, threshold_range, strategy_type, style, data_df,
                                  period_type="Training Period"):
    """為 walkforward 模式優化策略"""
    results = []
    total_iterations = len(window_range) * len(threshold_range)

    # 獲取當前正在處理嘅模型名稱
    model_name = None
    for name, params in base_params.items():
        if params['func'] == strategy_func:
            model_name = name
            break
    
    # 用 tqdm 包裝雙重循環，顯示模型名稱同係優化期定係測試期
    progress_desc = f"{period_type} {model_name} ({STRATEGY_TYPE_DESC[strategy_type]}, {STRATEGY_STYLE_DESC[style]})"
    with tqdm(total=total_iterations, desc=progress_desc, disable=not PROGRESS_BAR) as pbar:
        for window in window_range:
            for threshold in threshold_range:
                try:
                    # 使用傳入嘅 data_df 而唔係全局嘅 df
                    temp_df = data_df.copy()
                    temp_df['pos'] = strategy_func(temp_df, window, threshold, strategy_type, style)

                    if temp_df['pos'].isna().all():
                        pbar.update(1)
                        continue

                    # 計算表現指標
                    temp_df['pos_t-1'] = temp_df['pos'].shift(SHIFT_PERIODS)
                    temp_df['trade'] = abs(temp_df['pos_t-1'] - temp_df['pos'])
                    temp_df['pnl'] = temp_df['pos_t-1'] * temp_df['percentage_change'] - temp_df['trade'] * 0.05 / 100
                    temp_df['cumu'] = temp_df['pnl'].cumsum()
                    temp_df['dd'] = temp_df['cumu'].cummax() - temp_df['cumu']

                    ar = round(temp_df['pnl'].mean() * time_variable, 3)
                    sr = round(temp_df['pnl'].mean() / temp_df['pnl'].std() * np.sqrt(time_variable), 3) if temp_df[
                                                                                                                'pnl'].std() != 0 else np.nan
                    mdd = temp_df['dd'].max()
                    cr = round(ar / mdd, 3) if mdd != 0 else np.nan

                    results.append([window, threshold, sr, cr, mdd, ar])
                except Exception as e:
                    pass
                pbar.update(1)

    return pd.DataFrame(results, columns=['window', 'threshold', 'sr', 'cr', 'mdd', 'ar'])


def walkforward_combine_results(train_results, test_results):
    """合併優化期同測試期嘅結果"""
    if train_results.empty or test_results.empty:
        return pd.DataFrame()

    # 合併兩個 DataFrame，使用 window 同 threshold 作為 key
    merged = pd.merge(
        train_results,
        test_results,
        on=['window', 'threshold'],
        suffixes=('_train', '_test')
    )

    return merged


def walkforward_collect_good_factors(results, title_suffix):
    """收集 walkforward 模式嘅好 factors"""
    good_factors = []

    for name, result in results.items():
        if result.empty:
            continue

        # 創建 pivot table 用於熱力圖
        pivot_table = result.assign(threshold=result['threshold'].round(3)).pivot(
            index='window', columns='threshold', values='sr_train'
        )

        for window in pivot_table.index:
            for threshold in pivot_table.columns:
                if pd.isna(pivot_table.loc[window, threshold]):
                    continue

                # 獲取優化期同測試期嘅指標
                matching_rows = result[(result['window'] == window) &
                               (result['threshold'] == threshold)]

                if matching_rows.empty:
                    continue

                train_sr = matching_rows['sr_train'].iloc[0]
                train_cr = matching_rows['cr_train'].iloc[0]
                test_sr = matching_rows['sr_test'].iloc[0]
                test_cr = matching_rows['cr_test'].iloc[0]

                # 判斷係咪好 factor (優化期同測試期都要 SR>1.5, CR>1.8, CR>SR)
                if (train_sr > 1.5 and train_cr > 1.8 and train_cr > train_sr and
                        test_sr > 1.5 and test_cr > 1.8 and test_cr > test_sr):
                    strategy_base_name = name.split('_')[0]
                    strategy_type = ('long_only' if 'long_only' in name
                                     else 'short_only' if 'short_only' in name
                    else 'long_short')
                    strategy_style = 'momentum' if 'momentum' in name else 'reversion'

                    full_strategy_name = f"{strategy_base_name} ({STRATEGY_STYLE_DESC[strategy_style]} {STRATEGY_TYPE_DESC[strategy_type]})"

                    good_factors.append({
                        'Strategy': full_strategy_name,
                        'Window': window,
                        'Threshold': threshold,
                        'train_sr': train_sr,
                        'train_cr': train_cr,
                        'train_mdd': matching_rows['mdd_train'].iloc[0],
                        'train_ar': matching_rows['ar_train'].iloc[0],
                        'test_sr': test_sr,
                        'test_cr': test_cr,
                        'test_mdd': matching_rows['mdd_test'].iloc[0],
                        'test_ar': matching_rows['ar_test'].iloc[0]
                    })

    return good_factors


def walkforward_print_formatted_results(df):
    """格式化打印 walkforward 結果"""
    header_formats = {
        'Strategy': '{:<40}',
        'train_sr': '{:<8}',
        'train_cr': '{:<8}',
        'test_sr': '{:<8}',
        'test_cr': '{:<8}',
        'Window': '{:<8}',
        'Threshold': '{:<8}'
    }

    data_formats = {
        'Strategy': '{:<40}',
        'train_sr': '{:<8.3f}',
        'train_cr': '{:<8.3f}',
        'test_sr': '{:<8.3f}',
        'test_cr': '{:<8.3f}',
        'Window': '{:<8d}',
        'Threshold': '{:<8.3f}'
    }

    headers = ['Strategy', 'train_sr', 'train_cr', 'test_sr', 'test_cr', 'Window', 'Threshold']
    print(''.join(header_formats[col].format(col) for col in headers))
    print('-' * 88)

    for _, row in df.iterrows():
        print(''.join(data_formats[col].format(row[col]) for col in headers))


def walkforward_plot_strategy_heatmaps(results, title_suffix, print_summary=True, train_df=None, test_df=None):
    """為 walkforward 模式畫熱力圖"""
    # 確保關閉所有現有圖形，避免之前嘅圖形影響
    plt.close('all')

    num_strategies = len(results)
    rows = (num_strategies + 2) // 3
    cols = min(3, num_strategies)

    fig = plt.figure(figsize=(17, 5 * rows))

    title_text = f'Factor: {factor_name} - Walk Forward Analysis'
    if metric_key and metric_key != "/":
        title_text += f' ({metric_key})'

    fig.text(0.5, 0.95, title_text, ha='center', va='bottom', fontsize=16, weight='bold')

    # 加入優化期同測試期嘅日期 (英文版)
    if train_df is not None and test_df is not None:
        train_period = f"Training Period: {train_df['t'].iloc[0].strftime('%Y-%m-%d')} to {train_df['t'].iloc[-1].strftime('%Y-%m-%d')}"
        test_period = f"Testing Period: {test_df['t'].iloc[0].strftime('%Y-%m-%d')} to {test_df['t'].iloc[-1].strftime('%Y-%m-%d')}"
        period_text = f"{train_period} | {test_period}"
        fig.text(0.5, 0.92, period_text, ha='center', va='bottom', fontsize=12)
        fig.text(0.5, 0.89, f'Strategy Type: {title_suffix}', ha='center', va='bottom', fontsize=14)
    else:
        fig.text(0.5, 0.92, f'Strategy Type: {title_suffix}', ha='center', va='bottom', fontsize=14)

    # 加入紅框條件提示 footnote (英文版) - 移到最右上角
    footnote_text = "Red box: Both training and testing periods meet SR>1.5, CR>1.8, CR>SR"
    fig.text(0.99, 0.99, footnote_text, ha='right', va='top', fontsize=10, style='italic')

    good_factors = []  # 用嚟收集好嘅factors

    for idx, (name, result) in enumerate(results.items()):
        if result.empty:
            continue
            
        ax = plt.subplot(rows, cols, idx + 1)
        
        # 將 threshold 四捨五入到 3 位小數，確保一致性
        result['threshold_rounded'] = result['threshold'].round(3)
        
        # 使用四捨五入後嘅 threshold 創建 pivot table
        pivot_table = result.pivot(
            index='window', columns='threshold_rounded', values='sr_train'
        )

        # 創建一個同樣大細嘅DataFrame嚟標記好嘅factors
        good_factors_mask = pd.DataFrame(False, index=pivot_table.index, columns=pivot_table.columns)
        
        # 檢查每個位置係咪好factor - 使用更穩健嘅方法
        for _, row in result.iterrows():
            window = row['window']
            threshold_rounded = row['threshold_rounded']
            
            # 確保 window 同 threshold_rounded 喺 pivot_table 嘅索引同列中
            if window in pivot_table.index and threshold_rounded in pivot_table.columns:
                train_sr = row['sr_train']
                train_cr = row['cr_train']
                test_sr = row['sr_test']
                test_cr = row['cr_test']
                
                # 判斷係咪好 factor (優化期同測試期都要 SR>1.5, CR>1.8, CR>SR)
                if (train_sr > 1.5 and train_cr > 1.8 and train_cr > train_sr and
                    test_sr > 1.5 and test_cr > 1.8 and test_cr > test_sr):
                    
                    # 標記為好 factor
                    good_factors_mask.loc[window, threshold_rounded] = True
                    
                    # 提取策略類型同風格
                    name_parts = name.split('_')
                    strategy_base_name = name_parts[0]
                    
                    # 正確處理策略類型
                    if 'long_only' in name:
                        strategy_type = 'long_only'
                    elif 'short_only' in name:
                        strategy_type = 'short_only'
                    else:
                        strategy_type = 'long_short'
                    
                    # 正確處理策略風格
                    strategy_style = 'momentum' if 'momentum' in name else 'reversion'
                    
                    # 組合完整策略名稱
                    full_strategy_name = f"{strategy_base_name} ({STRATEGY_STYLE_DESC[strategy_style]} {STRATEGY_TYPE_DESC[strategy_type]})"
                    
                    # 添加到好 factors 列表
                    good_factors.append({
                        'Strategy': full_strategy_name,
                        'Window': window,
                        'Threshold': row['threshold'],  # 使用原始 threshold 值
                        'train_sr': train_sr,
                        'train_cr': train_cr,
                        'train_mdd': row['mdd_train'],
                        'train_ar': row['ar_train'],
                        'test_sr': test_sr,
                        'test_cr': test_cr,
                        'test_mdd': row['mdd_test'],
                        'test_ar': row['ar_test']
                    })

        # 畫熱力圖 (顯示優化期嘅 SR)
        sns.heatmap(pivot_table, ax=ax, cmap='Greens', center=0,
                    annot=True, fmt='.2f', annot_kws={'size': 7}, cbar=False)
        
        # 為好嘅factors加紅框 (優化期同測試期都要符合條件)
        for i, window in enumerate(pivot_table.index):
            for j, threshold in enumerate(pivot_table.columns):
                if good_factors_mask.loc[window, threshold]:
                    ax.add_patch(plt.Rectangle(
                        (j, i),  # 使用索引位置而唔係值
                        1, 1, fill=False, edgecolor='red', linewidth=2
                    ))

        strategy_name = name.split('_')[0]
        ax.set_title(strategy_name, pad=10)  # 只顯示策略名
        ax.set_xlabel('Threshold')
        ax.set_ylabel('Window')
        ax.tick_params(labelsize=7)
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

    plt.tight_layout()
    plt.subplots_adjust(top=0.86)
    
    # 根據 SAVE_FIGURES 設置決定是儲存圖片還是顯示圖片
    if SAVE_FIGURES:
        # 創建 backtest_result 目錄（如果不存在）
        os.makedirs("backtest_result", exist_ok=True)
        
        # 創建特定因子嘅目錄
        factor_dir = f"backtest_result/{factor_name.replace(' ', '_')}"
        os.makedirs(factor_dir, exist_ok=True)
        
        # 添加好 factors 數量指標
        good_factor_count = len(good_factors)
        good_factor_indicator = f"_{good_factor_count}_good_factors" if good_factor_count > 0 else ""
        
        # 添加 metric_key 到文件名（如果有）
        metric_suffix = f"_{metric_key}" if metric_key and metric_key != "/" else ""
        
        # 策略類型同風格嘅簡短命名
        short_title = title_suffix.replace('Momentum', 'M').replace('Reversion', 'R').replace('Long Only', 'L').replace('Short Only', 'S').replace('Long-Short', 'LS')
        
        # 創建詳細嘅文件名
        filename = f"{factor_dir}/wf_heatmap_{underlying}_{resolution}{metric_suffix}_{short_title.replace(' ', '_').replace('-', '_')}{good_factor_indicator}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close(fig)
        print(f"儲存 Walk Forward 熱力圖為 {filename}")
    else:
        # 顯示圖片
        plt.show()

    return good_factors


def extract_ohlcv_data(exchange, cex_symbol, data_type, resolution, since, until):
    """
    Optimized extraction of OHLCV data with incremental updates
    
    Parameters:
    - exchange: CCXT exchange instance
    - cex_symbol: Trading pair symbol (e.g., "BTCUSDT")
    - data_type: Data type (e.g., "open", "high", "low", "close", "volume")
    - resolution: Time interval (e.g., "1h", "15m", etc.)
    - since: Start timestamp in seconds
    - until: End timestamp in seconds
    
    Returns:
    - DataFrame with OHLCV data
    """
    # Initialize empty list for OHLCV data
    all_ohlcv = []
    
    # Extract asset name from the symbol (e.g., "BTC" from "BTCUSDT")
    asset = cex_symbol.replace('USDT', '')
    
    # Create output directory if it doesn't exist
    os.makedirs("cex_data", exist_ok=True)
    
    # New naming convention: {asset}_{data_type}_{timeframe}.csv
    csv_filename = f"cex_data/{asset}_{data_type}_{resolution}.csv"
    
    # Check if file exists to implement incremental updates
    start_time = since
    if os.path.exists(csv_filename):
        try:
            print(f"Found existing data file: {csv_filename}")
            existing_df = pd.read_csv(csv_filename)
            
            if 't' in existing_df.columns:
                existing_df['t'] = pd.to_datetime(existing_df['t'])
                
                # Get the latest timestamp from existing data
                latest_time = existing_df['t'].max()
                
                # Convert to Unix timestamp in seconds
                latest_unix = int(latest_time.timestamp())
                
                # Start downloading from the latest timestamp + 1 timeframe
                if latest_unix > since:
                    # Add a small buffer to avoid duplicate records
                    time_buffer = 3600  # 1 hour buffer for safety
                    start_time = latest_unix + time_buffer
                    
                    print(f"Incremental update: Existing data ends at {latest_time}")
                    print(f"Will download data from {datetime.datetime.fromtimestamp(start_time)}")
                
                # If the existing data already covers the requested period
                if latest_unix >= until:
                    print(f"Existing data already up to date. No new data to download.")
                    
                    # Convert existing data to the expected format and return
                    df_value = pd.DataFrame({
                        't': existing_df['t'],
                        'value': existing_df[data_type] if data_type in existing_df.columns else existing_df['value']
                    })
                    return df_value
            
            print(f"Starting incremental update from {datetime.datetime.fromtimestamp(start_time)}")
        except Exception as e:
            print(f"Error reading existing file: {e}")
            print("Will download full dataset")
            # If there's an error with the existing file, start from the original since time
            start_time = since
    
    # Convert timestamps to milliseconds
    since_ms = start_time * 1000
    until_ms = until * 1000
    
    # Ensure the symbol has USDT suffix
    if not cex_symbol.endswith('USDT'):
        cex_symbol = f"{cex_symbol}USDT"
    
    print(f"Extracting {cex_symbol} {data_type} data from {datetime.datetime.fromtimestamp(start_time).strftime('%Y-%m-%d')} to {datetime.datetime.fromtimestamp(until).strftime('%Y-%m-%d')}")
    
    # Variables for progress tracking
    current_start = since_ms
    start_extract_time = time.time()
    prev_progress_time = start_extract_time
    chunks_fetched = 0
    total_records = 0
    
    # OPTIMIZED: Use maximum allowed limit (1000 for OHLCV in CCXT)
    chunk_size = 1000
    
    # Keep fetching data until we reach the end time
    while current_start < until_ms:
        try:
            chunk = exchange.fetch_ohlcv(cex_symbol, resolution, since=current_start, limit=chunk_size)
            if not chunk or len(chunk) == 0:
                print("No more data available")
                break
                
            all_ohlcv.extend(chunk)
            chunks_fetched += 1
            total_records += len(chunk)
            
            # Update current_start to fetch the next chunk
            current_start = chunk[-1][0] + 1
            
            # Display progress every 10 chunks or every 5 seconds
            current_time = time.time()
            if chunks_fetched % 10 == 0 or current_time - prev_progress_time > 5:
                elapsed = current_time - start_extract_time
                rate = total_records / elapsed if elapsed > 0 else 0
                
                # Convert the timestamp of the last record to date for progress reporting
                latest_date = datetime.datetime.fromtimestamp(chunk[-1][0] / 1000)
                print(f"Progress: {chunks_fetched} chunks, {total_records} records ({rate:.1f} rec/sec), up to {latest_date.strftime('%Y-%m-%d')}")
                
                prev_progress_time = current_time
            
            # OPTIMIZED: Use minimal delay to avoid rate limits
            time.sleep(0.05)
            
        except Exception as e:
            print(f"Error fetching data chunk: {e}")
            # If we get an error, wait a bit longer before trying again
            time.sleep(1)
            # If we've had multiple errors, break
            if chunks_fetched > 0 and len(all_ohlcv) > 0:
                break
    
    total_duration = time.time() - start_extract_time
    records_per_second = total_records / total_duration if total_duration > 0 else 0
    
    print(f"Extraction complete: {total_records} records in {total_duration:.1f} seconds ({records_per_second:.1f} rec/sec)")
    
    if not all_ohlcv and not os.path.exists(csv_filename):
        print(f"Error fetching OHLCV data from {exchange.id}")
        return None
    
    if all_ohlcv:
        # Create dataframe from OHLCV data
        new_df = pd.DataFrame(all_ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        new_df['t'] = pd.to_datetime(new_df['timestamp'], unit='ms')
    
        # If we're doing an incremental update, combine with existing data
        if os.path.exists(csv_filename) and start_time > since:
            try:
                existing_df = pd.read_csv(csv_filename)
                
                # Ensure 't' column is datetime
                if 't' in existing_df.columns:
                    existing_df['t'] = pd.to_datetime(existing_df['t'])
                
                # Prepare for merging
                if data_type not in existing_df.columns and 'value' in existing_df.columns:
                    # If the existing file uses 'value' as the column name
                    combined_df = pd.DataFrame({
                        'timestamp': existing_df['timestamp'] if 'timestamp' in existing_df.columns else existing_df['t'].astype(int) // 10**6,
                        data_type: existing_df['value'],
                        't': existing_df['t']
                    })
                    
                else:
                    # If the existing file already has the data_type column
                    combined_df = existing_df
                
                # Add the new data
                combined_df = pd.concat([
                    combined_df,
                    pd.DataFrame({
                        'timestamp': new_df['timestamp'],
                        data_type: new_df[data_type],
                        't': new_df['t']
                    })
                ])
                
                # Remove duplicates based on timestamp
                combined_df = combined_df.drop_duplicates(subset=['timestamp']).reset_index(drop=True)
                
                # Sort by timestamp
                combined_df = combined_df.sort_values('timestamp').reset_index(drop=True)
                
                # Save the combined data
                combined_df.to_csv(csv_filename, index=False)
                print(f"Updated {csv_filename} with {len(new_df)} new records. Total: {len(combined_df)} records.")
                
                # Create value dataframe using the requested data column
                df_value = pd.DataFrame({
                    't': combined_df['t'],
                    'value': combined_df[data_type]
                })
                
            except Exception as e:
                print(f"Error combining with existing data: {e}")
                # Fall back to using just the new data
                df_value = pd.DataFrame({
                    't': new_df['t'],
                    'value': new_df[data_type]
                })
                
                # Save just the new data
                new_df[['timestamp', data_type, 't']].to_csv(csv_filename, index=False)
                print(f"Saved {len(new_df)} records to {csv_filename} (overwriting existing file due to error).")
        
        else:
            # For new files or full downloads
            df_value = pd.DataFrame({
                't': new_df['t'],
                'value': new_df[data_type]
            })
            
            # Save the data
            new_df[['timestamp', data_type, 't']].to_csv(csv_filename, index=False)
            print(f"Saved {len(new_df)} records to {csv_filename}")
    
    return df_value


def extract_and_save_data(api_url, metric_key, underlying="BTC", cex_symbol=None, resolution="1h", since=None, api_key=None):
    """
    Extract and save data from either CEX or Glassnode API.
    
    Args:
        api_url: API URL or CEX data type (e.g., 'cex_close')
        metric_key: Metric key for Glassnode API
        underlying: Underlying asset (e.g., 'BTC')
        cex_symbol: Trading pair symbol for CEX data (e.g., 'BTCUSDT')
        resolution: Timeframe resolution (e.g., '1h', '4h', '1d')
        since: Start timestamp in milliseconds
        api_key: API key for Glassnode
        
    Returns:
        pd.DataFrame: DataFrame with timestamp and value columns
    """
    try:
        # Initialize exchange for CEX data
        exchange = initialize_bybit_exchange()
        
        # Handle CEX data types
        if api_url.startswith('cex_'):
            data_type = api_url.split('_')[1]  # Extract data type (e.g., 'close' from 'cex_close')
            
            # Load General Config to get symbol mapping
            try:
                config_df = pd.read_excel('fund/fund_bybit.xlsx', sheet_name='General Config')
                # Create a mapping of asset to full symbol
                symbol_map = dict(zip(config_df['Asset'], config_df['Symbol']))
                
                # Set default symbol if not provided
                if not cex_symbol:
                    cex_symbol = underlying
                
                # Get the full symbol from the config
                if cex_symbol in symbol_map:
                    cex_symbol = symbol_map[cex_symbol]
                else:
                    print(f"Warning: Symbol {cex_symbol} not found in General Config. Using as is.")
            except Exception as e:
                print(f"Error loading General Config: {e}")
                # Fallback to default behavior if config loading fails
                if not cex_symbol:
                    cex_symbol = f"{underlying}USDT"
                
            # Extract data
            df = extract_ohlcv_data(exchange, cex_symbol, data_type, resolution, since, None)
            
            if df.empty:
                print(f"No data found for {cex_symbol} {data_type} {resolution}")
                return pd.DataFrame({'t': [], 'value': []})
                
            return df
            
        # Handle Glassnode API
        else:
            # Construct API URL
            url = f"{api_url}?a={underlying}&i={resolution}"
            if since:
                url += f"&s={since}"
            if api_key:
                url += f"&api_key={api_key}"
                
            # Fetch data
            response = requests.get(url)
            if response.status_code != 200:
                print(f"Error fetching data from Glassnode: {response.status_code}")
                return pd.DataFrame({'t': [], 'value': []})
                
            # Process data
            data = response.json()
            df = process_glassnode_data(data, metric_key)
            
            # Save raw data if needed
            if not df.empty:
                os.makedirs("glassnode_data", exist_ok=True)
                metric_name = api_url.split('/')[-1]
                raw_filename = f"glassnode_data/{underlying}_{metric_name}_{resolution}_{pd.Timestamp.now().strftime('%Y%m%d')}_raw.csv"
                df.to_csv(raw_filename, index=False)
            
            return df
            
    except Exception as e:
        print(f"Error extracting data: {str(e)}")
        return pd.DataFrame({'t': [], 'value': []})


def fetch_price_data(exchange, symbol, resolution, since_ms, until_ms):
    """Helper function to fetch price data for backtesting"""
    try:
        # Initialize empty list for OHLCV data
        all_ohlcv = []
        current_start = since_ms
        
        # Fetch in chunks
        while current_start < until_ms:
            chunk = exchange.fetch_ohlcv(symbol, resolution, since=current_start, limit=1000)
            if not chunk or len(chunk) == 0:
                break
                
            all_ohlcv.extend(chunk)
            current_start = chunk[-1][0] + 1  # Start from next timestamp after last data point
            
            # Add a small delay to avoid rate limits
            time.sleep(0.1)
        
        if not all_ohlcv:
            return None
        
        # Create dataframe
        df = pd.DataFrame(all_ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df['t'] = pd.to_datetime(df['timestamp'], unit='ms')
        
        # Return price dataframe
        return pd.DataFrame({
            't': df['t'],
            'value': df['close']
        })
        
    except Exception as e:
        print(f"Error fetching price data: {e}")
        return None


def resolution_to_pandas(resolution):
    """Convert CCXT resolution to pandas resample rule"""
    mapping = {
        '1m': '1T',
        '3m': '3T',
        '5m': '5T',
        '15m': '15T',
        '30m': '30T',
        '1h': '1H',
        '2h': '2H',
        '4h': '4H',
        '6h': '6H',
        '12h': '12H',
        '1d': '1D'
    }
    return mapping.get(resolution, '1H')


# 在文件底部添加以下代碼，確保獨立運行時能正常顯示圖表
if __name__ == "__main__":
    # Add imports at the beginning of execution
    try:
        from optimized_data_extraction import OptimizedExtractor
        from bybit_direct_api import BybitDirectAPI
    except ImportError:
        print("Warning: OptimizedExtractor or BybitDirectAPI modules not found. Will use basic data extraction.")
    
    # Initialize globals before using them
    initialize_globals()
    
    # Patch modules to use our credentials
    monkey_patch_module_imports()
    
    # Print since date for user information
    print(f"Using since date: {datetime.datetime.fromtimestamp(since).strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Running optimized {underlying} data extraction from {datetime.datetime.fromtimestamp(since).strftime('%Y-%m-%d')} to {datetime.datetime.fromtimestamp(int(time.time())).strftime('%Y-%m-%d')}")
    
    # Get the data from either CEX or Glassnode
    print(f"Running backtest for {underlying} ({cex_symbol}) since {datetime.datetime.fromtimestamp(since).strftime('%Y-%m-%d %H:%M:%S')}")
    
    df_value, df_price = extract_and_save_data(
        api_url=api_url,
        metric_key=metric_key,
        underlying=underlying,
        cex_symbol=cex_symbol,
        resolution=resolution,
        since=since,
        api_key=GLASSNODE_API_KEY  # Use global variable
    )
    
    # Proceed with backtesting only if data was successfully retrieved
    if df_value is not None and df_price is not None and not df_value.empty and not df_price.empty:
        # Display data summary
        print(f"\nSuccessfully retrieved {len(df_value)} records for {underlying}")
        print(f"Date range: {df_value['t'].min()} to {df_value['t'].max()}")
        
        # Count records by year
        year_counts = df_value['t'].dt.year.value_counts().sort_index()
        for year, count in year_counts.items():
            print(f"  {year}: {count} records")
        
        # Prepare the data
        df = prepare_data(df_value, df_price)
        
        # Run the strategy
        run_strategy(mode='optimize')
        # run_strategy(mode='walkforward')
        # run_strategy(mode='backtest', strategy_name='MinMax', strategy_type='long_short', style='momentum', window=5760, threshold=0.4)
    else:
        print("Data extraction failed. Cannot proceed with backtesting.")


    # strategy name: 'MA Diff', 'Z-Score', 'RSI', 'MinMax', 'Robust Scaling', 'MA Cross', 'Box-Cox', 'Rate of Change', 'Divergence'
    # strategy_Type: 'long_only', 'short_only', 'long_short'
    # style: 'momentum', 'reversion'
    # mode: 'optimize', 'backtest', 'walkforward'