import requests
import pandas as pd
import numpy as np
import plotly.express as px
import seaborn as sns
import matplotlib.pyplot as plt
import time
from io import StringIO
from tqdm import tqdm
from numba import jit
import os
import warnings
from models import Strategy, get_base_params
from datetime import datetime
import statsmodels.api as sm  # Added for alpha/beta calculation
from scipy import stats  # Added for regression analysis
import inspect

warnings.filterwarnings("ignore", category=UserWarning, message="FigureCanvasAgg is non-interactive, and thus cannot be shown")
warnings.filterwarnings("ignore", ".*tight_layout.*")

os.environ['PYTHONUNBUFFERED'] = '1'

# 執行進度
PROGRESS_BAR = True     # True - 顯示 ; False - 不顯示
SAVE_FIGURES = True  # True = 儲存圖片到文件, False = 彈出顯示圖片
USE_WINDOW_MULTIPLIER = True # Set to False to use exact window ranges regardless of resolution

# 追蹤當前處理的策略資訊，用於生成唯一圖像檔名
current_strategy_name = None
current_strategy_style = None
current_strategy_type = None
current_window = None
current_threshold = None

def process_glassnode_data(data, metric_key=None):
    result = []
    for item in data:
        value = (
            item['v'] if 'v' in item
            else item['o'].get(metric_key) if metric_key and isinstance(item['o'], dict)
            else item['o'] if isinstance(item['o'], (int, float))
            else sum(item['o'].values()) if isinstance(item['o'], dict)
            else None
        )

        if value is not None:
            result.append({
                't': pd.to_datetime(item['t'], unit='s'),
                'value': value
            })

    return pd.DataFrame(result)

pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)

# Constants
API_KEY = '2qQyTVjIsPnKqL8I33jJnLLs67I'
STRATEGY_TYPES = ['long_only', 'short_only', 'long_short']
STRATEGY_TYPE_DESC = {'long_only': 'Long Only', 'short_only': 'Short Only', 'long_short': 'Long-Short'}
STRATEGY_STYLES = ['momentum', 'reversion']
STRATEGY_STYLE_DESC = {'momentum': 'Momentum', 'reversion': 'Reversion'}

# Full backtesting period
since = 1589155200  # 2020 May 11
until = int(time.time())  # until now

# Dynamic shift periods based on resolution
def get_shift_periods(resolution):
    """根據時間框架設置 shift_periods"""
    if resolution == '24h':
        return 1  # 日線策略用 shift 1
    elif resolution == '1h':
        return 2  # 小時線策略用 shift 2
    elif resolution == '10m':
        return 6  # 10分鐘線策略用 shift 6
    else:
        # 默認值
        return 1

# 設定walk-forward比例
DEFAULT_TRAIN_RATIO = 0.7  # default 70% training period, 30% testing period

resolution = "1h"

# input api url and metric key
api_url = "https://api.glassnode.com/v1/metrics/derivatives/futures_volume_sell_daily_sum"
metric_key = "/"
underlying = "SUI"

factor_name = api_url.split('/')[-1].replace('_', ' ').title()
res = requests.get(api_url,
                   params={"a": underlying, "s": since, "u": until, "api_key": API_KEY, "i": resolution})
data = res.json()

df_value = process_glassnode_data(data, metric_key=metric_key)

# underlying
res = requests.get("https://api.glassnode.com/v1/metrics/market/price_usd_close",
                   params={"a": underlying, "s": since, "u": until, "api_key": API_KEY, "i": resolution})
data = res.json()
df_price = process_glassnode_data(data)


# 數據處理
def prepare_data(df_value, df_price):
    df = pd.merge(df_value, df_price, how='inner', on='t')
    df = df.rename(columns={'value_x': 'value', 'value_y': 'price'})
    df['percentage_change'] = df['price'].pct_change()
    df['bnh_cumu'] = df['percentage_change'].cumsum()
    return df


def get_time_variable(resolution):
    return {
        '24h': 365,
        '1h': 365 * 24,
        '10m': 365 * 24 * 6
    }.get(resolution, 365)


def calculate_long_short_pnl(df, pos_col='pos'):
    """
    Calculate separate long and short P&L components from a combined position column.
    
    Args:
        df: DataFrame with position and price data
        pos_col: Column name for positions
        
    Returns:
        DataFrame with added long and short positions and P&L components
    """
    result_df = df.copy()
    
    # Create long-only positions (only keep positive positions)
    result_df['long_pos'] = result_df[pos_col].copy()
    result_df.loc[result_df['long_pos'] <= 0, 'long_pos'] = 0
    
    # Create short-only positions (only keep negative positions, convert to positive for calculations)
    result_df['short_pos'] = result_df[pos_col].copy()
    result_df.loc[result_df['short_pos'] >= 0, 'short_pos'] = 0
    result_df['short_pos'] = result_df['short_pos'].abs()  # Convert to positive for calculations
    
    # Calculate P&L components
    # Long component
    long_pos_t_1 = result_df['long_pos'].shift(SHIFT_PERIODS)
    long_trade = abs(long_pos_t_1 - result_df['long_pos'])
    long_pnl = long_pos_t_1 * result_df['percentage_change'] - long_trade * 0.05 / 100
    result_df['long_cumu'] = long_pnl.cumsum()
    
    # Short component (negate percentage change for short positions)
    short_pos_t_1 = result_df['short_pos'].shift(SHIFT_PERIODS)
    short_trade = abs(short_pos_t_1 - result_df['short_pos'])
    short_pnl = short_pos_t_1 * (-result_df['percentage_change']) - short_trade * 0.05 / 100
    result_df['short_cumu'] = short_pnl.cumsum()
    
    # Calculate metrics for long component
    if not long_pnl.empty and long_pnl.std() != 0:
        long_sr = round(long_pnl.mean() / long_pnl.std() * np.sqrt(time_variable), 3)
    else:
        long_sr = np.nan
    long_ar = round(long_pnl.mean() * time_variable, 3) if not long_pnl.empty else np.nan
    
    # Calculate metrics for short component
    if not short_pnl.empty and short_pnl.std() != 0:
        short_sr = round(short_pnl.mean() / short_pnl.std() * np.sqrt(time_variable), 3)
    else:
        short_sr = np.nan
    short_ar = round(short_pnl.mean() * time_variable, 3) if not short_pnl.empty else np.nan
    
    # Store metrics in the DataFrame for later access
    result_df['long_sr'] = long_sr
    result_df['long_ar'] = long_ar
    result_df['short_sr'] = short_sr
    result_df['short_ar'] = short_ar
    
    return result_df


def bt_analysis(pos_col, strategy_type):
    # 每次分析都用新嘅 DataFrame，避免受之前嘅策略影響
    temp_df = df.copy()

    temp_df['pos_t-1'] = temp_df[pos_col].shift(SHIFT_PERIODS)
    temp_df['trade'] = abs(temp_df['pos_t-1'] - temp_df[pos_col])
    temp_df['pnl'] = temp_df['pos_t-1'] * temp_df['percentage_change'] - temp_df['trade'] * 0.05 / 100
    temp_df['cumu'] = temp_df['pnl'].cumsum()
    temp_df['dd'] = temp_df['cumu'].cummax() - temp_df['cumu']

    ar = round(temp_df['pnl'].mean() * time_variable, 3)
    sr = round(temp_df['pnl'].mean() / temp_df['pnl'].std() * np.sqrt(time_variable), 3) if temp_df[
                                                                                                 'pnl'].std() != 0 else np.nan
    mdd = temp_df['dd'].max()
    cr = round(ar / mdd, 3) if mdd != 0 else np.nan

    return pd.Series([sr, cr, mdd, ar], index=['sr', 'cr', 'mdd', 'ar'])


def optimize_strategy(strategy_func, window_range, threshold_range, strategy_type, style='momentum'):
    results = []
    total_iterations = len(window_range) * len(threshold_range)

    # 獲取當前正在處理嘅模型名稱
    model_name = None
    for name, params in base_params.items():
        if params['func'] == strategy_func:
            model_name = name
            break
    
    # 用 tqdm 包裝雙重循環，顯示模型名稱
    progress_desc = f"優化 {model_name} ({STRATEGY_TYPE_DESC[strategy_type]}, {STRATEGY_STYLE_DESC[style]})"
    with tqdm(total=total_iterations, desc=progress_desc, disable=not PROGRESS_BAR) as pbar:
        for window in window_range:
            for threshold in threshold_range:
                try:
                    # 暫存 df 嘅 pos 欄位，避免累積影響
                    temp_df = df.copy()
                    temp_df['pos'] = strategy_func(temp_df, window, threshold, strategy_type, style)
                    
                    # 檢查策略係咪產生有效嘅仓位
                    if temp_df['pos'].isna().all():
                        pbar.update(1)
                        continue
                        
                    # 使用 temp_df 而唔係全局 df 嚟計算表現指標
                    temp_df['pos_t-1'] = temp_df['pos'].shift(SHIFT_PERIODS)
                    temp_df['trade'] = abs(temp_df['pos_t-1'] - temp_df['pos'])
                    temp_df['pnl'] = temp_df['pos_t-1'] * temp_df['percentage_change'] - temp_df['trade'] * 0.05 / 100
                    temp_df['cumu'] = temp_df['pnl'].cumsum()
                    temp_df['dd'] = temp_df['cumu'].cummax() - temp_df['cumu']

                    ar = round(temp_df['pnl'].mean() * time_variable, 3)
                    sr = round(temp_df['pnl'].mean() / temp_df['pnl'].std() * np.sqrt(time_variable), 3) if temp_df['pnl'].std() != 0 else np.nan
                    mdd = temp_df['dd'].max()
                    cr = round(ar / mdd, 3) if mdd != 0 else np.nan
                    
                    results.append([window, threshold, sr, cr, mdd, ar])
                except Exception as e:
                    pass
                pbar.update(1)

    # 創建結果 DataFrame
    result_df = pd.DataFrame(results, columns=['window', 'threshold', 'sr', 'cr', 'mdd', 'ar'])
    
    return result_df


def get_window_range(resolution, base_range):
    """根據 resolution 調整 window range"""
    # Only apply multiplier if USE_WINDOW_MULTIPLIER is True
    if USE_WINDOW_MULTIPLIER:
        multiplier = {
            '24h': 1,  # 改為 1，唔再乘以 24
            '1h': 24,
            '10m': 24 * 6
        }.get(resolution, 1)
    else:
        multiplier = 1  # No multiplier if the flag is False

    return np.arange(
        base_range[0] * multiplier,
        base_range[1] * multiplier,
        base_range[2] * multiplier
    )


########### optimasation Zone ###########
# 從models.py導入base_params
base_params = get_base_params()

strategy_params = {
    name: {
        'func': params['func'],
        'window_range': get_window_range(resolution, params['base_window']),
        'threshold_range': (
            get_window_range(resolution, params['base_threshold'])
            if name == 'MA Cross'
            else params['threshold_range']
        )
    }
    for name, params in base_params.items()
}

# 喺回測分析之前加入呢啲代碼
df = prepare_data(df_value, df_price)
time_variable = get_time_variable(resolution)

# 策略類型嘅說明 (全局變量)
STRATEGY_TYPE_DESC = {
    'long_only': 'Long Only',
    'short_only': 'Short Only',
    'long_short': 'Long-Short'
}


def run_strategy(mode='optimize', strategy_name=None, window=None, threshold=None, strategy_type='long_only',
                 style='momentum', train_ratio=DEFAULT_TRAIN_RATIO):
    # 確保根據 resolution 正確設置 SHIFT_PERIODS
    global SHIFT_PERIODS
    SHIFT_PERIODS = get_shift_periods(resolution)

    if mode == 'optimize':
        results = {}
        best_results = []
        all_good_factors = []

        # 先做 momentum
        # print("\n=== Momentum Strategies ===")
        for strategy_type in STRATEGY_TYPES:
            # print(f"\n=== Optimizing Momentum {STRATEGY_TYPE_DESC[strategy_type]} ===")
            total_models = len(strategy_params)
            with tqdm(total=total_models, desc=f"處理 Momentum {STRATEGY_TYPE_DESC[strategy_type]} 策略", disable=not PROGRESS_BAR) as model_pbar:
                for name, params in strategy_params.items():
                    # print(f"\n開始優化 {name} 策略...")
                    result_df = optimize_strategy(
                        params['func'],
                        params['window_range'],
                        params['threshold_range'],
                        strategy_type,
                        'momentum'
                    ).sort_values('sr', ascending=False)

                    results[f"{name}_{strategy_type}_momentum"] = result_df

                    if not result_df.empty:
                        best_row = result_df.iloc[0]
                        best_results.append({
                            'Strategy': f"{name} (Momentum {STRATEGY_TYPE_DESC[strategy_type]})",
                            'Window': best_row['window'],
                            'Threshold': best_row['threshold'],
                            'sr': best_row['sr'],
                            'cr': best_row['cr'],
                            'mdd': best_row['mdd'],
                            'ar': best_row['ar']
                        })
                    model_pbar.update(1)

        # 再做 reversion
        # print("\n=== Reversion Strategies ===")
        for strategy_type in STRATEGY_TYPES:
            # print(f"\n=== Optimizing Reversion {STRATEGY_TYPE_DESC[strategy_type]} ===")
            total_models = len(strategy_params)
            with tqdm(total=total_models, desc=f"處理 Reversion {STRATEGY_TYPE_DESC[strategy_type]} 策略", disable=not PROGRESS_BAR) as model_pbar:
                for name, params in strategy_params.items():
                    # print(f"\n開始優化 {name} 策略...")
                    result_df = optimize_strategy(
                        params['func'],
                        params['window_range'],
                        params['threshold_range'],
                        strategy_type,
                        'reversion'
                    ).sort_values('sr', ascending=False)

                    results[f"{name}_{strategy_type}_reversion"] = result_df

                    if not result_df.empty:
                        best_row = result_df.iloc[0]
                        best_results.append({
                            'Strategy': f"{name} (Reversion {STRATEGY_TYPE_DESC[strategy_type]})",
                            'Window': best_row['window'],
                            'Threshold': best_row['threshold'],
                            'sr': best_row['sr'],
                            'cr': best_row['cr'],
                            'mdd': best_row['mdd'],
                            'ar': best_row['ar']
                        })
                    model_pbar.update(1)

        # 先顯示所有策略嘅最佳結果同好Factor總結
        print("\n=== 所有策略嘅最佳結果 (按 Sharpe Ratio 排序) ===")
        if best_results:
            best_results_df = pd.DataFrame(best_results)
            best_results_df['Window'] = best_results_df['Window'].astype(int)
            best_results_df = best_results_df[['Strategy', 'sr', 'cr', 'mdd', 'ar', 'Window', 'Threshold']]
            best_results_df = best_results_df.sort_values('sr', ascending=False)

            # 打印最佳結果
            print_formatted_results(best_results_df)
        else:
            print("\n未找到任何結果")

        # 畫熱力圖並收集好factors
        plot_titles = [
            ('momentum', 'long_only', 'Momentum Long Only'),
            ('momentum', 'short_only', 'Momentum Short Only'),
            ('momentum', 'long_short', 'Momentum Long-Short'),
            ('reversion', 'long_only', 'Reversion Long Only'),
            ('reversion', 'short_only', 'Reversion Short Only'),
            ('reversion', 'long_short', 'Reversion Long-Short')
        ]

        # 先收集所有好factors（唔顯示熱力圖）
        for style, strategy_type, title in plot_titles:
            current_results = {k: v for k, v in results.items()
                               if strategy_type in k and style in k}
            if current_results:
                good_factors = collect_good_factors(current_results, title)  # 新函數，只收集好factors
                if good_factors:
                    all_good_factors.extend(good_factors)

        # 顯示好Factor總結
        if all_good_factors:
            print("\n=== 好Factor總結 (按 Sharpe Ratio 排序) ===")
            print("條件: Sharpe > 1.5, CR > 1.8, CR > Sharpe")
            df_summary = pd.DataFrame(all_good_factors)
            df_summary['Window'] = df_summary['Window'].astype(int)
            df_summary = df_summary[['Strategy', 'sr', 'cr', 'mdd', 'ar', 'Window', 'Threshold']]
            df_summary = df_summary.sort_values('sr', ascending=False)

            # 打印好Factor總結
            print_formatted_results(df_summary)
        else:
            print("\n冇搵到好嘅factors (Sharpe > 1.5, CR > 1.8, CR > Sharpe)")

        # 最後顯示熱力圖
        for style, strategy_type, title in plot_titles:
            current_results = {k: v for k, v in results.items()
                               if strategy_type in k and style in k}
            if current_results:
                plot_strategy_heatmaps(current_results, title, print_summary=False)

    elif mode == 'walkforward':
        results = {}
        all_good_factors = []

        # 分割數據為訓練集同測試集
        split_idx = int(len(df) * train_ratio)
        train_df = df.iloc[:split_idx].copy()
        test_df = df.iloc[split_idx:].copy()

        # 先做 momentum
        for strategy_type in STRATEGY_TYPES:
            total_models = len(strategy_params)
            with tqdm(total=total_models, desc=f"Walk-Forward: Momentum {STRATEGY_TYPE_DESC[strategy_type]}", disable=not PROGRESS_BAR) as model_pbar:
                for name, params in strategy_params.items():
                    # 優化期結果
                    train_results = walkforward_optimize_strategy(
                        params['func'],
                        params['window_range'],
                        params['threshold_range'],
                        strategy_type,
                        'momentum',
                        train_df,
                        period_type="Training Period"
                    )

                    # 測試期結果
                    test_results = walkforward_optimize_strategy(
                        params['func'],
                        params['window_range'],
                        params['threshold_range'],
                        strategy_type,
                        'momentum',
                        test_df,
                        period_type="Testing Period"
                    )

                    # 合併結果
                    combined_results = walkforward_combine_results(train_results, test_results)

                    if not combined_results.empty:
                        results[f"{name}_{strategy_type}_momentum"] = combined_results
                    
                    model_pbar.update(1)

        # 再做 reversion
        for strategy_type in STRATEGY_TYPES:
            total_models = len(strategy_params)
            with tqdm(total=total_models, desc=f"Walk-Forward: Reversion {STRATEGY_TYPE_DESC[strategy_type]}", disable=not PROGRESS_BAR) as model_pbar:
                for name, params in strategy_params.items():
                    # 優化期結果
                    train_results = walkforward_optimize_strategy(
                        params['func'],
                        params['window_range'],
                        params['threshold_range'],
                        strategy_type,
                        'reversion',
                        train_df,
                        period_type="Training Period"
                    )

                    # 測試期結果
                    test_results = walkforward_optimize_strategy(
                        params['func'],
                        params['window_range'],
                        params['threshold_range'],
                        strategy_type,
                        'reversion',
                        test_df,
                        period_type="Testing Period"
                    )

                    # 合併結果
                    combined_results = walkforward_combine_results(train_results, test_results)

                    if not combined_results.empty:
                        results[f"{name}_{strategy_type}_reversion"] = combined_results
                    
                    model_pbar.update(1)

        # 畫熱力圖並收集好factors
        plot_titles = [
            ('momentum', 'long_only', 'Momentum Long Only'),
            ('momentum', 'short_only', 'Momentum Short Only'),
            ('momentum', 'long_short', 'Momentum Long-Short'),
            ('reversion', 'long_only', 'Reversion Long Only'),
            ('reversion', 'short_only', 'Reversion Short Only'),
            ('reversion', 'long_short', 'Reversion Long-Short')
        ]

        # 先收集所有好factors（唔顯示熱力圖）
        for style, strategy_type, title in plot_titles:
            current_results = {k: v for k, v in results.items()
                               if strategy_type in k and style in k}
            if current_results:
                good_factors = walkforward_collect_good_factors(current_results, title)
                if good_factors:
                    all_good_factors.extend(good_factors)

        # 顯示好Factor總結
        if all_good_factors:
            print("\n=== Walk Forward 好Factor總結 (按 Sharpe Ratio 排序) ===")
            print("條件: 優化期同測試期都要 SR>1.5, CR>1.8, CR>SR")
            df_summary = pd.DataFrame(all_good_factors)
            df_summary['Window'] = df_summary['Window'].astype(int)
            df_summary = df_summary[['Strategy', 'train_sr', 'train_cr', 'test_sr', 'test_cr', 'Window', 'Threshold']]
            df_summary = df_summary.sort_values('train_sr', ascending=False)

            # 打印好Factor總結
            walkforward_print_formatted_results(df_summary)
        else:
            print("\n冇搵到好嘅factors (優化期同測試期都要 SR>1.5, CR>1.8, CR>SR)")

        # 最後顯示熱力圖
        for style, strategy_type, title in plot_titles:
            current_results = {k: v for k, v in results.items()
                               if strategy_type in k and style in k}
            if current_results:
                walkforward_plot_strategy_heatmaps(current_results, title, print_summary=False, train_df=train_df,
                                                   test_df=test_df)

    elif mode == 'backtest':
        if any(x is None for x in [strategy_name, window, threshold]):
            raise ValueError("Backtest mode 需要提供 strategy_name, window 同 threshold")
        plot_backtest_results(strategy_name, window, threshold, strategy_type, style, train_ratio)


# 修改熱力圖函數
def plot_strategy_heatmaps(results, title_suffix, print_summary=True):
    # 確保關閉所有現有圖形，避免之前嘅圖形影響
    plt.close('all')

    num_strategies = len(results)
    rows = (num_strategies + 2) // 3
    cols = min(3, num_strategies)

    fig = plt.figure(figsize=(17, 5 * rows))

    title_text = f'Factor: {factor_name}'
    if metric_key and metric_key != "/":
        title_text += f' ({metric_key})'

    fig.text(0.5, 0.95, title_text, ha='center', va='bottom', fontsize=16, weight='bold')
    fig.text(0.5, 0.92, f'Strategy Type: {title_suffix}', ha='center', va='bottom', fontsize=14)

    # 加入紅框條件提示 footnote - 移到最右上角
    footnote_text = "Red box: SR>1.5, CR>1.8, CR>SR | Blue box: Short-only with SR>1.2, CR>1.5, CR>SR"
    fig.text(0.99, 0.99, footnote_text, ha='right', va='top', fontsize=10, style='italic')

    good_factors = []  # 用嚟收集好嘅factors
    
    # 檢查是否有非空結果
    has_valid_results = False

    for idx, (name, result) in enumerate(results.items()):
        if result.empty:
            continue

        # 檢查結果 DataFrame 中是否有 'sr_train' 欄位，如果沒有則使用 'sr'
        sr_col = 'sr_train' if 'sr_train' in result.columns else 'sr'
        cr_col = 'cr_train' if 'cr_train' in result.columns else 'cr'
        mdd_col = 'mdd_train' if 'mdd_train' in result.columns else 'mdd'
        ar_col = 'ar_train' if 'ar_train' in result.columns else 'ar'
        
        # 檢查是否為短倉策略
        is_short_only = 'short_only' in name
        
        # 提取策略基本名稱和類型
        name_parts = name.split('_')
        strategy_base_name = name_parts[0]
        
        # 判斷策略類型
        if 'long_only' in name:
            strategy_type = 'long_only'
        elif 'short_only' in name:
            strategy_type = 'short_only'
        else:
            strategy_type = 'long_short'
            
        # 判斷策略風格
        strategy_style = 'momentum' if 'momentum' in name else 'reversion'

        ax = plt.subplot(rows, cols, idx + 1)
        
        try:
            pivot_table = result.assign(threshold=result['threshold'].round(3)).pivot(
                index='window', columns='threshold', values=sr_col
            )
            
            # 檢查是否成功創建 pivot table
            if pivot_table.empty:
                print(f"警告: 策略 {name} 無法創建有效的熱力圖表格，跳過")
                continue
                
            has_valid_results = True
        except Exception as e:
            print(f"創建熱力圖表格時出錯 (策略: {name}): {str(e)}")
            continue

        # 創建兩個同樣大細嘅DataFrame嚟標記好嘅factors
        good_factors_mask = pd.DataFrame(False, index=pivot_table.index, columns=pivot_table.columns)
        short_only_good_factors_mask = pd.DataFrame(False, index=pivot_table.index, columns=pivot_table.columns)

        # 檢查每個位置係咪好factor
        for window in pivot_table.index:
            for threshold in pivot_table.columns:
                # 首先確保呢個位置有sr值
                if pd.isna(pivot_table.loc[window, threshold]):
                    continue

                sr = pivot_table.loc[window, threshold]

                # 搵返對應嘅CR值，加入錯誤處理
                matching_rows = result[(result['window'] == window) &
                                       (result['threshold'] == threshold)]

                if matching_rows.empty or pd.isna(matching_rows[cr_col].iloc[0]):
                    continue

                cr = matching_rows[cr_col].iloc[0]
                mdd = matching_rows[mdd_col].iloc[0]
                ar = matching_rows[ar_col].iloc[0]

                # 判斷係咪標準好factor (SR > 1.5, CR > 1.8, CR > SR)
                if sr > 1.5 and cr > 1.8 and cr > sr:
                    good_factors_mask.loc[window, threshold] = True

                    # 組合完整策略名稱
                    full_strategy_name = f"{strategy_base_name} ({STRATEGY_STYLE_DESC[strategy_style]} {STRATEGY_TYPE_DESC[strategy_type]})"

                    good_factors.append({
                        'Strategy': full_strategy_name,
                        'Window': window,
                        'Threshold': threshold,
                        'sr': sr,
                        'cr': cr,
                        'mdd': mdd,
                        'ar': ar
                    })
                
                # 判斷係咪特殊短倉好factor (SR > 1.2, CR > 1.5, CR > SR)
                elif is_short_only and sr > 1.2 and cr > 1.5 and cr > sr:
                    short_only_good_factors_mask.loc[window, threshold] = True
                    
                    # 組合完整策略名稱，加上星號標記
                    full_strategy_name = f"{strategy_base_name} ({STRATEGY_STYLE_DESC[strategy_style]} {STRATEGY_TYPE_DESC[strategy_type]}) *"

                    good_factors.append({
                        'Strategy': full_strategy_name,
                        'Window': window,
                        'Threshold': threshold,
                        'sr': sr,
                        'cr': cr,
                        'mdd': mdd,
                        'ar': ar,
                        'short_only_special': True  # 特殊短倉標誌
                    })

        # 畫熱力圖
        sns.heatmap(pivot_table, ax=ax, cmap='Greens', center=0,
                    annot=True, fmt='.2f', annot_kws={'size': 7}, cbar=False)

        # 為好嘅factors加紅框
        for i, window in enumerate(pivot_table.index):
            for j, threshold in enumerate(pivot_table.columns):
                if good_factors_mask.loc[window, threshold]:
                    # 使用 get_loc 獲取正確的索引位置
                    window_idx = pivot_table.index.get_loc(window)
                    threshold_idx = pivot_table.columns.get_loc(threshold)
                    
                    ax.add_patch(plt.Rectangle(
                        (threshold_idx, window_idx),
                        1, 1, fill=False, edgecolor='red', linewidth=2
                    ))
                
                # 為短倉好factors加藍框
                if short_only_good_factors_mask.loc[window, threshold]:
                    # 使用 get_loc 獲取正確的索引位置
                    window_idx = pivot_table.index.get_loc(window)
                    threshold_idx = pivot_table.columns.get_loc(threshold)
                    
                    ax.add_patch(plt.Rectangle(
                        (threshold_idx, window_idx),
                        1, 1, fill=False, edgecolor='blue', linewidth=2
                    ))

        strategy_name = name.split('_')[0]
        ax.set_title(strategy_name, pad=10)  # 只顯示策略名
        ax.set_xlabel('Threshold')
        ax.set_ylabel('Window')
        ax.tick_params(labelsize=7)
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

    plt.tight_layout(pad=3.0, h_pad=2.0, w_pad=2.0)
    plt.subplots_adjust(top=0.86)
    
    # 如果沒有有效結果，顯示警告並返回
    if not has_valid_results:
        print(f"警告: 喺 {title_suffix} 策略類型中冇搵到有效結果，唔會生成熱力圖")
        plt.close(fig)
        return []
    
    # 根據 SAVE_FIGURES 設置決定是儲存圖片還是顯示圖片
    if SAVE_FIGURES:
        # 創建 backtest_result 目錄（如果不存在）
        os.makedirs("backtest_result", exist_ok=True)
        
        # 創建特定因子嘅目錄
        factor_dir = f"backtest_result/{factor_name.replace(' ', '_')}"
        os.makedirs(factor_dir, exist_ok=True)
        
        # 添加好 factors 數量指標
        good_factor_count = len(good_factors)
        good_factor_indicator = f"_{good_factor_count}_good_factors" if good_factor_count > 0 else ""
        
        # 添加 metric_key 到文件名（如果有）
        metric_suffix = f"_{metric_key}" if metric_key and metric_key != "/" else ""
        
        # 策略類型同風格嘅簡短命名
        short_title = title_suffix.replace('Momentum', 'M').replace('Reversion', 'R').replace('Long Only', 'L').replace('Short Only', 'S').replace('Long-Short', 'LS')
        
        # 生成唯一檔案名（添加時間戳）- 使用毫秒級時間戳確保唯一性
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S%f")[:17]
        
        # 儲存圖片到因子目錄
        filename = f"{factor_dir}/heatmap_{underlying}_{resolution}{metric_suffix}_{short_title.replace(' ', '_').replace('-', '_')}{good_factor_indicator}_{timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close(fig)
        print(f"儲存熱力圖為 {filename}")
    else:
        # 顯示圖片
        plt.show()

    # 改為返回good_factors而唔係print
    if not print_summary:
        return good_factors

    # 如果需要print，就保持原有嘅print邏輯
    if good_factors:
        print("\n=== 好Factor總結 (按 Sharpe Ratio 排序) ===")

        # 將好factors轉換成DataFrame並排序
        df_summary = pd.DataFrame(good_factors)
        df_summary = df_summary[['Strategy', 'sr', 'cr', 'mdd', 'ar', 'Window', 'Threshold']]
        df_summary = df_summary.sort_values('sr', ascending=False)

        # 設定header格式
        header_formats = {
            'Strategy': '{:<40}',
            'sr': '{:<8}',
            'cr': '{:<8}',
            'mdd': '{:<8}',
            'ar': '{:<8}',
            'Window': '{:<8}',
            'Threshold': '{:<8}'
        }

        # 設定數據格式
        data_formats = {
            'Strategy': '{:<40}',
            'sr': '{:<8.3f}',
            'cr': '{:<8.3f}',
            'mdd': '{:<8.3f}',
            'ar': '{:<8.3f}',
            'Window': '{:<8d}',
            'Threshold': '{:<8.3f}'
        }

        headers = ['Strategy', 'sr', 'cr', 'mdd', 'ar', 'Window', 'Threshold']
        print(''.join(header_formats[col].format(col) for col in headers))
        print('-' * 88)

        for _, row in df_summary.iterrows():
            print(''.join(data_formats[col].format(row[col]) for col in headers))
    else:
        print("\n冇搵到好嘅factors (sr>1.5，cr>1.8，同時cr>sr)")

    return good_factors  # 都係要返回good_factors


######### Backtest Zone ###########
def plot_backtest_results(strategy_name, window, threshold, strategy_type, style, train_ratio=DEFAULT_TRAIN_RATIO):
    try:
        # 確保關閉所有現有圖形，避免之前嘅圖形影響
        plt.close('all')

        # 更新全局策略信息變量（用於圖像文件名）
        global current_strategy_name, current_strategy_style, current_strategy_type, current_window, current_threshold
        current_strategy_name = strategy_name
        current_strategy_style = style
        current_strategy_type = strategy_type
        current_window = window
        current_threshold = threshold

        # 設置 SHIFT_PERIODS 根據 resolution
        global SHIFT_PERIODS
        SHIFT_PERIODS = get_shift_periods(resolution)

        # 分割數據為 Training Period 同 Testing Period，使用傳入嘅 train_ratio 參數
        train_size = int(len(df) * train_ratio)
        train_df = df.iloc[:train_size].copy()
        test_df = df.iloc[train_size:].copy()

        # 獨立計算訓練期策略信號
        train_df['pos'] = strategy_params[strategy_name]['func'](
            train_df,
            window,
            threshold,
            strategy_type,
            style
        )

        # 獨立計算測試期策略信號
        test_df['pos'] = strategy_params[strategy_name]['func'](
            test_df,
            window,
            threshold,
            strategy_type,
            style
        )
        
        pos_col = 'pos'

        # 合併完整的數據集，用於計算全時段表現
        temp_df = pd.concat([train_df, test_df])
        
        # 計算交易統計
        temp_df['pos_t-1'] = temp_df[pos_col].shift(SHIFT_PERIODS)
        temp_df['trade'] = abs(temp_df['pos_t-1'] - temp_df[pos_col])
        pnl = temp_df['pos_t-1'] * temp_df['percentage_change'] - temp_df['trade'] * 0.05 / 100
        temp_df['pnl'] = pnl  # 添加 pnl 到 DataFrame
        temp_df['cumu'] = pnl.cumsum()
        temp_df['bnh_cumu'] = temp_df['percentage_change'].cumsum()
        
        # 計算交易統計
        total_trades = temp_df['trade'].sum()
        trades_per_year = total_trades / (len(temp_df) / time_variable)

        # 計算全時段表現指標
        sr = round(pnl.mean() / pnl.std() * np.sqrt(time_variable), 3) if pnl.std() != 0 else np.nan
        ar = round(pnl.mean() * time_variable, 3)
        dd = temp_df['cumu'].cummax() - temp_df['cumu']
        mdd = round(dd.max(), 6)
        cr = round(ar / mdd, 3) if mdd != 0 else np.nan
        
        # 計算 Buy & Hold 嘅表現指標
        bnh_pnl = temp_df['percentage_change']
        bnh_sr = round(bnh_pnl.mean() / bnh_pnl.std() * np.sqrt(time_variable), 3) if bnh_pnl.std() != 0 else np.nan
        bnh_ar = round(bnh_pnl.mean() * time_variable, 3)
        bnh_dd = temp_df['bnh_cumu'].cummax() - temp_df['bnh_cumu']
        bnh_mdd = round(bnh_dd.max(), 6)
        bnh_cr = round(bnh_ar / bnh_mdd, 3) if bnh_mdd != 0 else np.nan
        
        # 計算 alpha, beta, r_squared 及 p-value (更新)
        alpha, beta, r_squared, alpha_p_value, beta_p_value = calculate_alpha_beta(temp_df)
        alpha = round(alpha, 3)
        beta = round(beta, 3)
        r_squared = round(r_squared, 3)
        alpha_p_value = round(alpha_p_value, 3)
        beta_p_value = round(beta_p_value, 3)
        
        # 計算長短倉分量
        temp_df = calculate_long_short_pnl(temp_df, pos_col)
            
        # 獲取長倉分量指標
        long_sr = temp_df['long_sr'].iloc[0] if not temp_df.empty else np.nan
        long_ar = temp_df['long_ar'].iloc[0] if not temp_df.empty else np.nan
        long_dd = temp_df['long_cumu'].cummax() - temp_df['long_cumu']
        long_mdd = round(long_dd.max(), 6) if not long_dd.empty else 0
        long_cr = round(long_ar / long_mdd, 3) if long_mdd != 0 else np.nan
            
        # 獲取短倉分量指標
        short_sr = temp_df['short_sr'].iloc[0] if not temp_df.empty else np.nan
        short_ar = temp_df['short_ar'].iloc[0] if not temp_df.empty else np.nan
        short_dd = temp_df['short_cumu'].cummax() - temp_df['short_cumu']
        short_mdd = round(short_dd.max(), 6) if not short_dd.empty else 0
        short_cr = round(short_ar / short_mdd, 3) if short_mdd != 0 else np.nan

        # 計算訓練期交易統計
        train_trades = train_df['trade'].sum() if 'trade' in train_df.columns else temp_df['trade'][:train_size].sum()
        train_trades_per_year = train_trades / (len(train_df) / time_variable)
        
        # 計算訓練期表現指標
        train_df['pos_t-1'] = train_df['pos'].shift(SHIFT_PERIODS)
        train_df['trade'] = abs(train_df['pos_t-1'] - train_df['pos'])
        train_pnl = train_df['pos_t-1'] * train_df['percentage_change'] - train_df['trade'] * 0.05 / 100
        train_df['pnl'] = train_pnl  # 添加 pnl 到 DataFrame
        train_df['cumu'] = train_pnl.cumsum()
        train_df['train_cumu'] = train_pnl.cumsum()
        train_sr = round(train_pnl.mean() / train_pnl.std() * np.sqrt(time_variable), 3) if train_pnl.std() != 0 else np.nan
        train_ar = round(train_pnl.mean() * time_variable, 3)
        train_dd = train_df['cumu'].cummax() - train_df['cumu']
        train_mdd = round(train_dd.max(), 6)
        train_cr = round(train_ar / train_mdd, 3) if train_mdd != 0 else np.nan
        
        # 計算訓練期 alpha, beta, r_squared 及 p-value (更新)
        train_alpha, train_beta, train_r_squared, train_alpha_p_value, train_beta_p_value = calculate_alpha_beta(train_df)
        train_alpha = round(train_alpha, 3)
        train_beta = round(train_beta, 3)
        train_r_squared = round(train_r_squared, 3)
        train_alpha_p_value = round(train_alpha_p_value, 3)
        train_beta_p_value = round(train_beta_p_value, 3)

        # 計算測試期交易統計
        test_trades = test_df['trade'].sum() if 'trade' in test_df.columns else temp_df['trade'][train_size:].sum()
        test_trades_per_year = test_trades / (len(test_df) / time_variable)
        
        # 計算測試期表現指標
        test_df['pos_t-1'] = test_df['pos'].shift(SHIFT_PERIODS)
        test_df['trade'] = abs(test_df['pos_t-1'] - test_df['pos'])
        test_pnl = test_df['pos_t-1'] * test_df['percentage_change'] - test_df['trade'] * 0.05 / 100
        test_df['pnl'] = test_pnl  # 添加 pnl 到 DataFrame
        test_df['cumu'] = test_pnl.cumsum()
        test_df['test_cumu'] = test_pnl.cumsum()
        test_sr = round(test_pnl.mean() / test_pnl.std() * np.sqrt(time_variable), 3) if test_pnl.std() != 0 else np.nan
        test_ar = round(test_pnl.mean() * time_variable, 3)
        test_dd = test_df['cumu'].cummax() - test_df['cumu']
        test_mdd = round(test_dd.max(), 6)
        test_cr = round(test_ar / test_mdd, 3) if test_mdd != 0 else np.nan
        
        # 為 Testing Period 創建一個由零開始嘅 Buy & Hold curve
        test_df['test_bnh_cumu'] = test_df['percentage_change'].cumsum()
        
        # 計算測試期 Buy & Hold 嘅表現指標
        test_bnh_pnl = test_df['percentage_change']
        test_bnh_sr = round(test_bnh_pnl.mean() / test_bnh_pnl.std() * np.sqrt(time_variable), 3) if test_bnh_pnl.std() != 0 else np.nan
        test_bnh_ar = round(test_bnh_pnl.mean() * time_variable, 3)
        test_bnh_dd = test_df['test_bnh_cumu'].cummax() - test_df['test_bnh_cumu']
        test_bnh_mdd = round(test_bnh_dd.max(), 6)
        test_bnh_cr = round(test_bnh_ar / test_bnh_mdd, 3) if test_bnh_mdd != 0 else np.nan
        
        # 計算測試期 alpha, beta, r_squared 及 p-value (更新)
        test_alpha, test_beta, test_r_squared, test_alpha_p_value, test_beta_p_value = calculate_alpha_beta(test_df)
        test_alpha = round(test_alpha, 3)
        test_beta = round(test_beta, 3)
        test_r_squared = round(test_r_squared, 3)
        test_alpha_p_value = round(test_alpha_p_value, 3)
        test_beta_p_value = round(test_beta_p_value, 3)

        # 準備時間段資訊同表現指標
        period_info = (
            f"Training: {train_df['t'].iloc[0].strftime('%Y-%m-%d')} to {train_df['t'].iloc[-1].strftime('%Y-%m-%d')} ({train_ratio * 100:.0f}%) | "
            f"Testing: {test_df['t'].iloc[0].strftime('%Y-%m-%d')} to {test_df['t'].iloc[-1].strftime('%Y-%m-%d')} ({(1 - train_ratio) * 100:.0f}%)"
        )

        # 添加交易統計到時間指標（分成三行）
        time_metrics = (
            f"Training: SR={train_sr}, CR={train_cr}, MDD={train_mdd:.4f}, AR={train_ar}, Trades={train_trades:.0f}, Trades/Yr={train_trades_per_year:.1f}\n"
            f"Testing: SR={test_sr}, CR={test_cr}, MDD={test_mdd:.4f}, AR={test_ar}, Trades={test_trades:.0f}, Trades/Yr={test_trades_per_year:.1f}\n"
            f"Full: SR={sr}, CR={cr}, MDD={mdd:.4f}, AR={ar}, Trades={total_trades:.0f}, Trades/Yr={trades_per_year:.1f}"
        )
        
        # 添加長短倉分量指標
        component_metrics = (
            f"Long: SR={long_sr}, CR={long_cr}, MDD={long_mdd:.4f}, AR={long_ar}\n"
            f"Short: SR={short_sr}, CR={short_cr}, MDD={short_mdd:.4f}, AR={short_ar}\n"
            f"Trades: {total_trades:.0f}, Trades/Yr: {trades_per_year:.1f}"
        )
        
        # Factor title - 加入品種和時間周期
        factor_title = f'Factor: {factor_name} | Asset: {underlying} | Resolution: {resolution}'
        if metric_key and metric_key != "/":
            factor_title += f' | Metric: {metric_key}'

        style_desc = 'Momentum' if style == 'momentum' else 'Mean Reversion'
        
        # 標記訓練期和測試期的分界
        split_date = train_df['t'].iloc[-1]
        
        # 設置字體大小參數
        AXIS_LABEL_SIZE = 8  # 軸標籤字體大小
        TICK_LABEL_SIZE = 8  # 刻度標籤字體大小
        ANNOTATION_FONT_SIZE = 8  # 註釋字體大小
        LEGEND_FONT_SIZE = 10  # 圖例字體大小
        TITLE_FONT_SIZE = 10  # 標題字體大小
        
        # 根據策略類型決定圖表大小
        if strategy_type in ['long_only', 'short_only']:
            # 對於長倉或短倉策略，使用較窄的圖表
            fig = plt.figure(figsize=(14, 11))
        else:
            # 對於長短倉策略，保持原有的較寬圖表
            fig = plt.figure(figsize=(22, 11))
        
        # 根據策略類型決定布局
        if strategy_type in ['long_only', 'short_only']:
            # 對於 long_only 或 short_only 策略，使用 1x2 布局（移除 long-short component 圖表）
            gs = fig.add_gridspec(2, 1, height_ratios=[3, 1], hspace=0.05)
            
            # 上方：Equity Curve 圖表（佔據整個寬度）
            ax1 = fig.add_subplot(gs[0, 0])
            
            # 下方：Position 倉位圖表
            ax3 = fig.add_subplot(gs[1, 0])
            
            # 創建空的 ax2 和 ax4 以避免後面的代碼出錯
            ax2 = None
            ax4 = None
        else:
            # 對於 long-short 策略，保持原有的 2x2 布局
            gs = fig.add_gridspec(2, 2, height_ratios=[3, 1], width_ratios=[1, 1], hspace=0.05, wspace=0.1)
            
            # 左上：Equity Curve 圖表
            ax1 = fig.add_subplot(gs[0, 0])
            
            # 右上：Long-Short Component 圖表
            ax2 = fig.add_subplot(gs[0, 1])
            
            # 左下：Position 倉位圖表
            ax3 = fig.add_subplot(gs[1, 0])
            
            # 右下：Long-Short Position 倉位圖表
            ax4 = fig.add_subplot(gs[1, 1])
        
        # ===== 左上/上方：主策略表現（Equity Curve）=====
        # 確保Y軸有足夠的顯示範圍
        all_cumu_values = []
        if not temp_df['cumu'].empty:
            all_cumu_values.extend(temp_df['cumu'].tolist())
        if not train_df['train_cumu'].empty:
            all_cumu_values.extend(train_df['train_cumu'].tolist())
        if not test_df['test_cumu'].empty:
            all_cumu_values.extend(test_df['test_cumu'].tolist())
        if not temp_df['bnh_cumu'].empty:
            all_cumu_values.extend(temp_df['bnh_cumu'].tolist())
        if not test_df['test_bnh_cumu'].empty:
            all_cumu_values.extend(test_df['test_bnh_cumu'].tolist())

        # 過濾NaN值
        all_cumu_values = [v for v in all_cumu_values if not pd.isna(v)]

        if all_cumu_values:
            y_min = min(all_cumu_values)
            y_max = max(all_cumu_values)
            # 擴大範圍
            y_range = y_max - y_min
            y_min = y_min - y_range * 0.1
            y_max = y_max + y_range * 0.1
            ax1.set_ylim(y_min, y_max)

        # 繪製全時段 Buy & Hold 曲線（灰色）
        ax1.plot(temp_df['t'], temp_df['bnh_cumu'], 
               label=f'Full Period Buy & Hold (SR={bnh_sr}, CR={bnh_cr}, MDD={bnh_mdd:.4f}, AR={bnh_ar})', 
               color='gray', linewidth=1.5, linestyle='--')
        
        # 繪製全時段回報曲線（帶 alpha/beta/p-value 在標籤中）(更新)
        ax1.plot(temp_df['t'], temp_df['cumu'], 
               label=f'Full Period (SR={sr}, CR={cr}, α={alpha} (p={alpha_p_value}), β={beta} (p={beta_p_value}), R²={r_squared})', 
               color='blue', linewidth=1.5)
        
        # 繪製訓練期回報曲線（帶 alpha/beta/p-value）(更新)
        ax1.plot(train_df['t'], train_df['train_cumu'], 
               label=f'Training Period (SR={train_sr}, CR={train_cr}, α={train_alpha} (p={train_alpha_p_value}), β={train_beta} (p={train_beta_p_value}), R²={train_r_squared})',
               color='green', linewidth=1.5)
        
        # 繪製測試期回報曲線（帶 alpha/beta/p-value）(更新)
        ax1.plot(test_df['t'], test_df['test_cumu'], 
               label=f'Testing Period (SR={test_sr}, CR={test_cr}, α={test_alpha} (p={test_alpha_p_value}), β={test_beta} (p={test_beta_p_value}), R²={test_r_squared})', 
               color='red', linewidth=1.5)
                 
        # 添加測試期 Buy & Hold 曲線（只包含測試期）
        ax1.plot(test_df['t'], test_df['test_bnh_cumu'], 
                label=f'Testing Period Buy & Hold (SR={test_bnh_sr}, CR={test_bnh_cr}, MDD={test_bnh_mdd:.4f}, AR={test_bnh_ar})', 
                color='orange', linewidth=1.5, linestyle='--')
        
        # 添加垂直線標記訓練期和測試期的分界
        ax1.axvline(x=split_date, color='black', linestyle='--')
        
        # 添加背景色區分訓練期和測試期
        ax1.axvspan(temp_df['t'].iloc[0], split_date, alpha=0.1, color='green')
        ax1.axvspan(split_date, temp_df['t'].iloc[-1], alpha=0.1, color='red')
        
        # 設置標題、標籤和圖例
        ax1.set_title(
            f'{strategy_name} ({style_desc} {STRATEGY_TYPE_DESC[strategy_type]}) | Window={window}, Threshold={threshold}\n{period_info}\n{time_metrics}',
            fontsize=TITLE_FONT_SIZE, loc='left'
        )
        ax1.set_ylabel('Cumulative Return', fontsize=AXIS_LABEL_SIZE)
        ax1.grid(True, alpha=0.3)
        ax1.legend(loc='upper left', fontsize=LEGEND_FONT_SIZE)
        ax1.tick_params(axis='both', labelsize=TICK_LABEL_SIZE)
        
        # ===== 只有 long-short 策略才繪製右上：長短倉組件圖表 =====
        if strategy_type == 'long_short' and ax2 is not None:
            # 計算長短倉交易數量
            long_trade = abs(temp_df['long_pos'].shift(SHIFT_PERIODS) - temp_df['long_pos']).sum()
            short_trade = abs(temp_df['short_pos'].shift(SHIFT_PERIODS) - temp_df['short_pos']).sum()
            long_trades_per_year = long_trade / (len(temp_df) / time_variable)
            short_trades_per_year = short_trade / (len(temp_df) / time_variable)
            
            # 計算長短倉嘅單期回報
            # 從累積回報中提取單期回報
            temp_df['long_pnl'] = temp_df['long_cumu'].diff().fillna(0)
            temp_df['short_pnl'] = temp_df['short_cumu'].diff().fillna(0)
            
            # 分別計算長倉同短倉嘅 alpha, beta, R² 同 p-value (更新)
            # 創建臨時 DataFrame 用於計算，因為 calculate_alpha_beta 預期特定的欄位名稱
            long_df = temp_df[['percentage_change']].copy()
            long_df['pnl'] = temp_df['long_pnl']
            long_alpha, long_beta, long_r_squared, long_alpha_p_value, long_beta_p_value = calculate_alpha_beta(long_df)
            
            short_df = temp_df[['percentage_change']].copy()
            short_df['pnl'] = temp_df['short_pnl']
            short_alpha, short_beta, short_r_squared, short_alpha_p_value, short_beta_p_value = calculate_alpha_beta(short_df)
            
            # 四捨五入結果用於顯示
            long_alpha = round(long_alpha, 3)
            long_beta = round(long_beta, 3)
            long_r_squared = round(long_r_squared, 3)
            long_alpha_p_value = round(long_alpha_p_value, 3)
            long_beta_p_value = round(long_beta_p_value, 3)
            
            short_alpha = round(short_alpha, 3)
            short_beta = round(short_beta, 3)
            short_r_squared = round(short_r_squared, 3)
            short_alpha_p_value = round(short_alpha_p_value, 3)
            short_beta_p_value = round(short_beta_p_value, 3)
            
            # Buy & Hold with darker color
            ax2.plot(temp_df['t'], temp_df['bnh_cumu'], label='Buy & Hold PnL', color='#555555', linewidth=1.5, linestyle='-')
            
            # Plot full combined strategy (更新)
            ax2.plot(temp_df['t'], temp_df['cumu'], 
                    label=f'Combined PnL (SR={sr}, CR={cr}, α={alpha} (p={alpha_p_value}), β={beta} (p={beta_p_value}), R²={r_squared})', 
                    color='#8e44ad', linewidth=2)  # Purple
            
            # Plot long component (更新)
            ax2.plot(temp_df['t'], temp_df['long_cumu'], 
                    label=f'Long PnL (SR={long_sr}, CR={long_cr}, α={long_alpha} (p={long_alpha_p_value}), β={long_beta} (p={long_beta_p_value}), R²={long_r_squared})', 
                    color='#2980b9', linewidth=2)  # Darker blue
            
            # Plot short component (更新)
            ax2.plot(temp_df['t'], temp_df['short_cumu'], 
                    label=f'Short PnL (SR={short_sr}, CR={short_cr}, α={short_alpha} (p={short_alpha_p_value}), β={short_beta} (p={short_beta_p_value}), R²={short_r_squared})', 
                    color='#c0392b', linewidth=2)  # Darker red
            
            # 設置標題、標籤和圖例
            # 修改標題，添加統計數據而不是時間範圍
            ax2.set_title(
                f'Long-Short Components\n'
                f'Combined: Trades={total_trades:.0f}, Trades/Yr={trades_per_year:.1f}\n'
                f'Long: SR={long_sr}, CR={long_cr}, MDD={long_mdd:.4f}, AR={long_ar}, Trades={long_trade:.0f}, Trades/Yr={long_trades_per_year:.1f}\n'
                f'Short: SR={short_sr}, CR={short_cr}, MDD={short_mdd:.4f}, AR={short_ar}, Trades={short_trade:.0f}, Trades/Yr={short_trades_per_year:.1f}',
                fontsize=TITLE_FONT_SIZE, loc='left'
            )
            ax2.set_ylabel('Cumulative Return', fontsize=AXIS_LABEL_SIZE)
            ax2.grid(True, alpha=0.3)
            ax2.legend(loc='upper left', fontsize=LEGEND_FONT_SIZE)
            ax2.tick_params(axis='both', labelsize=TICK_LABEL_SIZE)
        
        # ===== 左下/下方：Position 倉位圖表 =====
        ax3.plot(temp_df['t'], temp_df[pos_col], label='Position', color='purple', linewidth=1)
        ax3.axvline(x=split_date, color='black', linestyle='--')
        ax3.axvspan(temp_df['t'].iloc[0], split_date, alpha=0.1, color='green')
        ax3.axvspan(split_date, temp_df['t'].iloc[-1], alpha=0.1, color='red')
        ax3.set_ylabel('Position', fontsize=AXIS_LABEL_SIZE)
        ax3.set_xlabel('Date', fontsize=AXIS_LABEL_SIZE)
        ax3.grid(True, alpha=0.3)
        ax3.tick_params(axis='both', labelsize=TICK_LABEL_SIZE)

        # ===== 只有 long-short 策略才繪製右下：Long-Short Position 倉位圖表 =====
        if strategy_type == 'long_short' and ax4 is not None:
            ax4.plot(temp_df['t'], temp_df['long_pos'], label='Long Position', color='#2980b9', linewidth=1)
            ax4.plot(temp_df['t'], -temp_df['short_pos'], label='Short Position', color='#c0392b', linewidth=1)  # 使用負值顯示空倉
            ax4.axvspan(temp_df['t'].iloc[0], split_date, alpha=0.05, color='#7f8c8d')  # Lighter gray
            ax4.axvspan(split_date, temp_df['t'].iloc[-1], alpha=0.05, color='#bdc3c7')  # Very light gray
            ax4.set_ylabel('Long/Short Position', fontsize=AXIS_LABEL_SIZE)
            ax4.set_xlabel('Date', fontsize=AXIS_LABEL_SIZE)
            ax4.grid(True, alpha=0.3)
            ax4.tick_params(axis='both', labelsize=TICK_LABEL_SIZE)
            ax4.legend(loc='upper left', fontsize=LEGEND_FONT_SIZE)
        
        # 整體佈局調整
        plt.suptitle(factor_title, fontsize=16, fontweight='bold', y=0.98)  # 嘗試調整 y 值，越大越高
        plt.tight_layout()
        plt.subplots_adjust(top=0.87)  # 調整頂部空間，值越小，標題下空間越大
        plt.gcf().autofmt_xdate()  # 自動格式化x軸日期標籤，避免重疊
        
        # 檢查圖像是否為空白
        has_data = False
        if len(ax1.lines) > 0 or len(ax1.collections) > 0 or len(ax1.containers) > 0:
            has_data = True
        
        if not has_data:
            print(f"警告：策略 {strategy_name} 生成嘅圖表冇數據，跳過保存")
            return
        
        # 儲存圖片或顯示圖片
        if SAVE_FIGURES:
            try:
                # 創建目錄
                os.makedirs("backtest_result", exist_ok=True)
                os.makedirs(f"backtest_result/{factor_name.replace(' ', '_')}", exist_ok=True)
                
                # 為文件名添加資產、周期和指標
                metric_suffix = f"_{metric_key}" if metric_key and metric_key != "/" else ""
                
                # 添加時間戳，確保每次生成的文件名不同
                timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                
                filename = f"backtest_result/{factor_name.replace(' ', '_')}/{strategy_name}_{underlying}_{resolution}{metric_suffix}_{style[0]}_{strategy_type}_w{window}_t{threshold}_{timestamp}.png"
                plt.savefig(filename, dpi=300, bbox_inches='tight')
                print(f"已保存圖表到：{filename}")
            except Exception as e:
                print(f"保存圖表時出錯：{e}")
                plt.show()  # 如果保存失敗，嘗試顯示圖表
        else:
            plt.show()
            
        # 打印 alpha, beta 和 R² 在一個單獨嘅摘要中 (更新)
        print("\n=== Alpha and Beta Analysis ===")
        print(f"Full Period:    Alpha = {alpha:.4f} (p={alpha_p_value:.4f}), Beta = {beta:.4f} (p={beta_p_value:.4f}), R² = {r_squared:.4f}")
        print(f"Training Period: Alpha = {train_alpha:.4f} (p={train_alpha_p_value:.4f}), Beta = {train_beta:.4f} (p={train_beta_p_value:.4f}), R² = {train_r_squared:.4f}")
        print(f"Testing Period:  Alpha = {test_alpha:.4f} (p={test_alpha_p_value:.4f}), Beta = {test_beta:.4f} (p={test_beta_p_value:.4f}), R² = {test_r_squared:.4f}")
        
        # 打印交易統計
        print("\n=== Trade Statistics ===")
        print(f"Full Period:    Total Trades = {total_trades:.0f}, Trades per Year = {trades_per_year:.1f}")
        print(f"Training Period: Total Trades = {train_trades:.0f}, Trades per Year = {train_trades_per_year:.1f}")
        print(f"Testing Period:  Total Trades = {test_trades:.0f}, Trades per Year = {test_trades_per_year:.1f}")
        
        # 返回 Long/Short 表現指標，如果需要
        return {
            'sr': sr, 'cr': cr, 'mdd': mdd, 'ar': ar,
            'long_sr': long_sr, 'long_cr': long_cr, 'long_mdd': long_mdd, 'long_ar': long_ar,
            'short_sr': short_sr, 'short_cr': short_cr, 'short_mdd': short_mdd, 'short_ar': short_ar
        }
        
    except Exception as e:
        print(f"繪製回測結果時出錯：{str(e)}")
        import traceback
        traceback.print_exc()
        return None


def print_formatted_results(df):
    """統一格式化打印結果"""
    header_formats = {
        'Strategy': '{:<40}',
        'sr': '{:<8}',
        'cr': '{:<8}',
        'mdd': '{:<8}',
        'ar': '{:<8}',
        'Window': '{:<8}',
        'Threshold': '{:<8}'
    }

    data_formats = {
        'Strategy': '{:<40}',
        'sr': '{:<8.3f}',
        'cr': '{:<8.3f}',
        'mdd': '{:<8.3f}',
        'ar': '{:<8.3f}',
        'Window': '{:<8d}',
        'Threshold': '{:<8.3f}'
    }

    headers = ['Strategy', 'sr', 'cr', 'mdd', 'ar', 'Window', 'Threshold']
    print(''.join(header_formats[col].format(col) for col in headers))
    print('-' * 88)

    for _, row in df.iterrows():
        print(''.join(data_formats[col].format(row[col]) for col in headers))


def collect_good_factors(results, title_suffix):
    """只收集好factors，唔顯示熱力圖"""
    good_factors = []

    for name, result in results.items():
        if result.empty:
            continue

        # 檢查結果 DataFrame 中是否有 'sr_train' 欄位，如果沒有則使用 'sr'
        sr_col = 'sr_train' if 'sr_train' in result.columns else 'sr'
        cr_col = 'cr_train' if 'cr_train' in result.columns else 'cr'
        mdd_col = 'mdd_train' if 'mdd_train' in result.columns else 'mdd'
        ar_col = 'ar_train' if 'ar_train' in result.columns else 'ar'

        pivot_table = result.assign(threshold=result['threshold'].round(3)).pivot(
            index='window', columns='threshold', values=sr_col
        )

        for window in pivot_table.index:
            for threshold in pivot_table.columns:
                if pd.isna(pivot_table.loc[window, threshold]):
                    continue

                sr = pivot_table.loc[window, threshold]
                matching_rows = result[(result['window'] == window) &
                                       (result['threshold'] == threshold)]

                if matching_rows.empty:
                    continue

                cr = matching_rows[cr_col].iloc[0]
                mdd = matching_rows[mdd_col].iloc[0]
                ar = matching_rows[ar_col].iloc[0]

                if sr > 1.5 and cr > 1.8 and cr > sr:
                    strategy_base_name = name.split('_')[0]
                    strategy_type = ('long_only' if 'long_only' in name
                                     else 'short_only' if 'short_only' in name
                    else 'long_short')
                    strategy_style = 'momentum' if 'momentum' in name else 'reversion'

                    full_strategy_name = f"{strategy_base_name} ({STRATEGY_STYLE_DESC[strategy_style]} {STRATEGY_TYPE_DESC[strategy_type]})"

                    good_factors.append({
                        'Strategy': full_strategy_name,
                        'Window': window,
                        'Threshold': threshold,
                        'sr': sr,
                        'cr': cr,
                        'mdd': mdd,
                        'ar': ar
                    })

    return good_factors


def walk_forward_analysis(strategy_name, window, threshold, strategy_type='long_only', style='momentum',
                          n_segments=5, train_ratio=DEFAULT_TRAIN_RATIO):
    """
    執行 Walk Forward Analysis

    參數:
    - strategy_name: 策略名稱
    - window: 窗口大小
    - threshold: 閾值
    - strategy_type: 策略類型 ('long_only', 'short_only', 'long_short')
    - style: 策略風格 ('momentum', 'reversion')
    - n_segments: 分段數量
    - train_ratio: 訓練期佔每個分段嘅百分比
    """
    # 複製原始數據
    temp_df = df.copy()

    # 計算每個分段嘅大小
    segment_size = len(temp_df) // n_segments

    # 儲存每個測試期嘅結果
    test_results = []

    # 創建一個新嘅 DataFrame 嚟儲存所有測試期嘅結果
    wfa_df = pd.DataFrame()

    # 計算測試期佔比
    test_pct = 1 - train_ratio

    # 遍歷每個分段
    for i in range(n_segments):
        # 計算當前分段嘅起始同結束索引
        start_idx = i * segment_size
        end_idx = (i + 1) * segment_size if i < n_segments - 1 else len(temp_df)

        # 計算優化期同測試期嘅分界點
        split_idx = start_idx + int((end_idx - start_idx) * train_ratio)

        # 分割優化期同測試期
        train_df = temp_df.iloc[start_idx:split_idx].copy()
        test_df = temp_df.iloc[split_idx:end_idx].copy()

        # 喺優化期入面搵最佳參數 (呢度可以加入優化邏輯，或者直接用傳入嘅參數)
        # 簡化起見，我哋直接用傳入嘅參數

        # 喺測試期入面應用策略
        test_df['pos'] = strategy_params[strategy_name]['func'](
            test_df, window, threshold, strategy_type, style
        )

        # 計算測試期嘅表現
        test_df['pos_t-1'] = test_df['pos'].shift(SHIFT_PERIODS)
        test_df['trade'] = abs(test_df['pos_t-1'] - test_df['pos'])
        test_df['pnl'] = test_df['pos_t-1'] * test_df['percentage_change'] - test_df['trade'] * 0.05 / 100
        test_df['cumu'] = test_df['pnl'].cumsum()

        # 計算測試期嘅表現指標
        sr = round(test_df['pnl'].mean() / test_df['pnl'].std() * np.sqrt(time_variable), 3) if test_df[
                                                                                                    'pnl'].std() != 0 else np.nan
        ar = round(test_df['pnl'].mean() * time_variable, 3)
        dd = test_df['cumu'].cummax() - test_df['cumu']
        mdd = round(dd.max(), 6)
        cr = round(ar / mdd, 3) if mdd != 0 else np.nan

        # 儲存測試期嘅結果
        test_results.append({
            'Segment': i + 1,
            'Start Date': test_df['t'].iloc[0].strftime('%Y-%m-%d'),
            'End Date': test_df['t'].iloc[-1].strftime('%Y-%m-%d'),
            'SR': sr,
            'CR': cr,
            'MDD': mdd,
            'AR': ar
        })

        # 將測試期嘅結果加入到 wfa_df
        test_df['segment'] = i + 1
        wfa_df = pd.concat([wfa_df, test_df[['t', 'cumu', 'segment']]])

    # 顯示每個測試期嘅結果
    results_df = pd.DataFrame(test_results)
    print("\n=== Walk Forward Analysis 結果 ===")
    print(f"策略: {strategy_name} ({STRATEGY_STYLE_DESC[style]} {STRATEGY_TYPE_DESC[strategy_type]})")
    print(f"Window: {window}, Threshold: {threshold}")
    print(f"訓練期佔比: {train_ratio * 100:.0f}%, 測試期佔比: {test_pct * 100:.0f}%")
    print("\n")
    print(results_df.to_string(index=False))

    # 計算平均表現
    avg_sr = results_df['SR'].mean()
    avg_cr = results_df['CR'].mean()
    avg_mdd = results_df['MDD'].mean()
    avg_ar = results_df['AR'].mean()

    print("\n=== 平均表現 ===")
    print(f"平均 SR: {avg_sr:.3f}")
    print(f"平均 CR: {avg_cr:.3f}")
    print(f"平均 MDD: {avg_mdd:.6f}")
    print(f"平均 AR: {avg_ar:.3f}")

    # 畫出每個測試期嘅累積回報
    fig = px.line(wfa_df, x='t', y='cumu', color='segment',
                  title=f'Walk Forward Analysis - {strategy_name} ({STRATEGY_STYLE_DESC[style]} {STRATEGY_TYPE_DESC[strategy_type]})')
    fig.update_layout(
        xaxis_title='日期',
        yaxis_title='累積回報',
        legend_title='分段'
    )
    fig.show()

    return results_df, wfa_df


def walkforward_optimize_strategy(strategy_func, window_range, threshold_range, strategy_type, style, data_df,
                                  period_type="Training Period"):
    """為 walkforward 模式優化策略"""
    results = []
    total_iterations = len(window_range) * len(threshold_range)

    # 獲取當前正在處理嘅模型名稱
    model_name = None
    for name, params in base_params.items():
        if params['func'] == strategy_func:
            model_name = name
            break
    
    # 用 tqdm 包裝雙重循環，顯示模型名稱同係優化期定係測試期
    progress_desc = f"{period_type} {model_name} ({STRATEGY_TYPE_DESC[strategy_type]}, {STRATEGY_STYLE_DESC[style]})"
    with tqdm(total=total_iterations, desc=progress_desc, disable=not PROGRESS_BAR) as pbar:
        for window in window_range:
            for threshold in threshold_range:
                try:
                    # 使用傳入嘅 data_df 而唔係全局嘅 df
                    temp_df = data_df.copy()
                    temp_df['pos'] = strategy_func(temp_df, window, threshold, strategy_type, style)

                    # 檢查策略係咪產生有效嘅仓位
                    if temp_df['pos'].isna().all():
                        pbar.update(1)
                        continue

                    # 計算表現指標
                    temp_df['pos_t-1'] = temp_df['pos'].shift(SHIFT_PERIODS)
                    temp_df['trade'] = abs(temp_df['pos_t-1'] - temp_df['pos'])
                    temp_df['pnl'] = temp_df['pos_t-1'] * temp_df['percentage_change'] - temp_df['trade'] * 0.05 / 100
                    temp_df['cumu'] = temp_df['pnl'].cumsum()
                    temp_df['dd'] = temp_df['cumu'].cummax() - temp_df['cumu']

                    ar = round(temp_df['pnl'].mean() * time_variable, 3)
                    sr = round(temp_df['pnl'].mean() / temp_df['pnl'].std() * np.sqrt(time_variable), 3) if temp_df[
                                                                                                                'pnl'].std() != 0 else np.nan
                    mdd = temp_df['dd'].max()
                    cr = round(ar / mdd, 3) if mdd != 0 else np.nan

                    results.append([window, threshold, sr, cr, mdd, ar])
                except Exception as e:
                    pass
                pbar.update(1)

    # 創建結果 DataFrame
    result_df = pd.DataFrame(results, columns=['window', 'threshold', 'sr', 'cr', 'mdd', 'ar'])
    
    return result_df


def walkforward_combine_results(train_results, test_results):
    """合併優化期同測試期嘅結果"""
    if train_results.empty or test_results.empty:
        return pd.DataFrame()
    
    try:
        # 合併兩個 DataFrame，使用 window 同 threshold 作為 key
        merged = pd.merge(
            train_results,
            test_results,
            on=['window', 'threshold'],
            suffixes=('_train', '_test')
        )
        
        return merged
    except Exception as e:
        return pd.DataFrame()


def walkforward_collect_good_factors(results, title_suffix):
    """收集 walkforward 模式嘅好 factors"""
    good_factors = []

    for name, result in results.items():
        if result.empty:
            continue

        # 創建 pivot table 用於熱力圖
        pivot_table = result.assign(threshold=result['threshold'].round(3)).pivot(
            index='window', columns='threshold', values='sr_train'
        )

        for window in pivot_table.index:
            for threshold in pivot_table.columns:
                if pd.isna(pivot_table.loc[window, threshold]):
                    continue

                # 獲取優化期同測試期嘅指標
                matching_rows = result[(result['window'] == window) &
                               (result['threshold'] == threshold)]

                if matching_rows.empty:
                    continue

                train_sr = matching_rows['sr_train'].iloc[0]
                train_cr = matching_rows['cr_train'].iloc[0]
                test_sr = matching_rows['sr_test'].iloc[0]
                test_cr = matching_rows['cr_test'].iloc[0]

                # 判斷係咪好 factor (優化期同測試期都要 SR>1.5, CR>1.8, CR>SR)
                if (train_sr > 1.5 and train_cr > 1.8 and train_cr > train_sr and
                        test_sr > 1.5 and test_cr > 1.8 and test_cr > test_sr):
                    strategy_base_name = name.split('_')[0]
                    strategy_type = ('long_only' if 'long_only' in name
                                     else 'short_only' if 'short_only' in name
                    else 'long_short')
                    strategy_style = 'momentum' if 'momentum' in name else 'reversion'

                    full_strategy_name = f"{strategy_base_name} ({STRATEGY_STYLE_DESC[strategy_style]} {STRATEGY_TYPE_DESC[strategy_type]})"

                    good_factors.append({
                        'Strategy': full_strategy_name,
                        'Window': window,
                        'Threshold': threshold,
                        'train_sr': train_sr,
                        'train_cr': train_cr,
                        'train_mdd': matching_rows['mdd_train'].iloc[0],
                        'train_ar': matching_rows['ar_train'].iloc[0],
                        'test_sr': test_sr,
                        'test_cr': test_cr,
                        'test_mdd': matching_rows['mdd_test'].iloc[0],
                        'test_ar': matching_rows['ar_test'].iloc[0]
                    })

    return good_factors


def walkforward_print_formatted_results(df):
    """格式化打印 walkforward 結果"""
    header_formats = {
        'Strategy': '{:<40}',
        'train_sr': '{:<8}',
        'train_cr': '{:<8}',
        'test_sr': '{:<8}',
        'test_cr': '{:<8}',
        'Window': '{:<8}',
        'Threshold': '{:<8}'
    }

    data_formats = {
        'Strategy': '{:<40}',
        'train_sr': '{:<8.3f}',
        'train_cr': '{:<8.3f}',
        'test_sr': '{:<8.3f}',
        'test_cr': '{:<8.3f}',
        'Window': '{:<8d}',
        'Threshold': '{:<8.3f}'
    }

    headers = ['Strategy', 'train_sr', 'train_cr', 'test_sr', 'test_cr', 'Window', 'Threshold']
    print(''.join(header_formats[col].format(col) for col in headers))
    print('-' * 88)

    for _, row in df.iterrows():
        print(''.join(data_formats[col].format(row[col]) for col in headers))


def walkforward_plot_strategy_heatmaps(results, title_suffix, print_summary=True, train_df=None, test_df=None):
    """為 walkforward 模式畫熱力圖"""
    # 確保關閉所有現有圖形，避免之前嘅圖形影響
    plt.close('all')

    num_strategies = len(results)
    rows = (num_strategies + 2) // 3
    cols = min(3, num_strategies)

    fig = plt.figure(figsize=(17, 5 * rows))

    title_text = f'Factor: {factor_name} - Walk Forward Analysis'
    if metric_key and metric_key != "/":
        title_text += f' ({metric_key})'

    fig.text(0.5, 0.95, title_text, ha='center', va='bottom', fontsize=16, weight='bold')

    # 加入優化期同測試期嘅日期 (英文版)
    if train_df is not None and test_df is not None:
        train_period = f"Training Period: {train_df['t'].iloc[0].strftime('%Y-%m-%d')} to {train_df['t'].iloc[-1].strftime('%Y-%m-%d')}"
        test_period = f"Testing Period: {test_df['t'].iloc[0].strftime('%Y-%m-%d')} to {test_df['t'].iloc[-1].strftime('%Y-%m-%d')}"
        period_text = f"{train_period} | {test_period}"
        fig.text(0.5, 0.92, period_text, ha='center', va='bottom', fontsize=12)
        fig.text(0.5, 0.89, f'Strategy Type: {title_suffix}', ha='center', va='bottom', fontsize=14)
    else:
        fig.text(0.5, 0.92, f'Strategy Type: {title_suffix}', ha='center', va='bottom', fontsize=14)

    # 更新註腳，包括標準條件和短倉特殊條件
    footnote_text = "Red box: Both periods SR>1.5, CR>1.8, CR>SR | Blue box: Short-only with both periods SR>1.2, CR>1.5, CR>SR"
    fig.text(0.99, 0.99, footnote_text, ha='right', va='top', fontsize=10, style='italic')

    good_factors = []  # 用嚟收集好嘅factors
    
    # 檢查是否有非空結果
    has_valid_results = False

    for idx, (name, result) in enumerate(results.items()):
        if result.empty:
            continue
            
        ax = plt.subplot(rows, cols, idx + 1)
        
        # Extract the strategy base name and type from the full name
        name_parts = name.split('_')
        strategy_base_name = name_parts[0]
        is_short_only = 'short_only' in name
        strategy_style = 'momentum' if 'momentum' in name else 'reversion'
        
        if 'long_only' in name:
            strategy_type = 'long_only'
        elif 'short_only' in name:
            strategy_type = 'short_only'
        else:
            strategy_type = 'long_short'

        # 將 threshold 四捨五入到 3 位小數，確保一致性
        result['threshold_rounded'] = result['threshold'].round(3)

        # 使用四捨五入後嘅 threshold 創建 pivot table
        try:
            pivot_table = result.pivot(
                index='window', columns='threshold_rounded', values='sr_train'
            )
            
            # 檢查是否成功創建 pivot table
            if pivot_table.empty:
                print(f"警告: 策略 {name} 無法創建有效的熱力圖表格，跳過")
                continue
            
            has_valid_results = True
        except Exception as e:
            print(f"創建熱力圖表格時出錯 (策略: {name}): {str(e)}")
            continue

        # 創建兩個同樣大細嘅DataFrame嚟標記好嘅factors
        good_factors_mask = pd.DataFrame(False, index=pivot_table.index, columns=pivot_table.columns)
        short_only_good_factors_mask = pd.DataFrame(False, index=pivot_table.index, columns=pivot_table.columns)

        # 檢查每個位置係咪好factor - 使用更穩健嘅方法
        for _, row in result.iterrows():
            window = row['window']
            threshold_rounded = row['threshold_rounded']

            # 確保 window 同 threshold_rounded 喺 pivot_table 嘅索引同列中
            if window in pivot_table.index and threshold_rounded in pivot_table.columns:
                train_sr = row['sr_train']
                train_cr = row['cr_train']
                test_sr = row['sr_test']
                test_cr = row['cr_test']

                # 標準好 factor 條件（優化期和測試期都要符合）
                if (train_sr > 1.5 and train_cr > 1.8 and train_cr > train_sr and
                        test_sr > 1.5 and test_cr > 1.8 and test_cr > test_sr):

                    # 標記為好 factor
                    good_factors_mask.loc[window, threshold_rounded] = True

                    # 組合完整策略名稱
                    full_strategy_name = f"{strategy_base_name} ({STRATEGY_STYLE_DESC[strategy_style]} {STRATEGY_TYPE_DESC[strategy_type]})"

                    # 添加到好 factors 列表
                    good_factors.append({
                        'Strategy': full_strategy_name,
                        'Window': window,
                        'Threshold': row['threshold'],  # 使用原始 threshold 值
                        'train_sr': train_sr,
                        'train_cr': train_cr,
                        'train_mdd': row['mdd_train'],
                        'train_ar': row['ar_train'],
                        'test_sr': test_sr,
                        'test_cr': test_cr,
                        'test_mdd': row['mdd_test'],
                        'test_ar': row['ar_test']
                    })
                
                # 特殊短倉好 factor 條件（優化期和測試期都要符合）
                elif (is_short_only and 
                      train_sr > 1.2 and train_cr > 1.5 and train_cr > train_sr and
                      test_sr > 1.2 and test_cr > 1.5 and test_cr > test_sr):
                    
                    # 標記為短線好 factor
                    short_only_good_factors_mask.loc[window, threshold_rounded] = True
                    
                    # 組合完整策略名稱
                    full_strategy_name = f"{strategy_base_name} ({STRATEGY_STYLE_DESC[strategy_style]} {STRATEGY_TYPE_DESC['short_only']}) *"
                    
                    # 添加到好 factors 列表
                    good_factors.append({
                        'Strategy': full_strategy_name,
                        'Window': window,
                        'Threshold': row['threshold'],  # 使用原始 threshold 值
                        'train_sr': train_sr,
                        'train_cr': train_cr,
                        'train_mdd': row['mdd_train'],
                        'train_ar': row['ar_train'],
                        'test_sr': test_sr,
                        'test_cr': test_cr,
                        'test_mdd': row['mdd_test'],
                        'test_ar': row['ar_test'],
                        'short_only_special': True  # 特殊短倉標誌
                    })

        # 畫熱力圖 (顯示優化期嘅 SR)
        sns.heatmap(pivot_table, ax=ax, cmap='Greens', center=0,
                    annot=True, fmt='.2f', annot_kws={'size': 7}, cbar=False)

        # 為好嘅factors加紅框 (優化期同測試期都要符合條件)
        for window in pivot_table.index:
            for threshold in pivot_table.columns:
                if good_factors_mask.loc[window, threshold]:
                    # 使用 get_loc 獲取正確的索引位置
                    window_idx = pivot_table.index.get_loc(window)
                    threshold_idx = pivot_table.columns.get_loc(threshold)
                    
                    ax.add_patch(plt.Rectangle(
                        (threshold_idx, window_idx),
                        1, 1, fill=False, edgecolor='red', linewidth=2
                    ))
                
                # 為短倉好factors加藍框 (特殊短倉策略)
                if short_only_good_factors_mask.loc[window, threshold]:
                    # 使用 get_loc 獲取正確的索引位置
                    window_idx = pivot_table.index.get_loc(window)
                    threshold_idx = pivot_table.columns.get_loc(threshold)
                    
                    ax.add_patch(plt.Rectangle(
                        (threshold_idx, window_idx),
                        1, 1, fill=False, edgecolor='blue', linewidth=2
                    ))

        strategy_name = name.split('_')[0]
        ax.set_title(strategy_name, pad=10)  # 只顯示策略名
        ax.set_xlabel('Threshold')
        ax.set_ylabel('Window')
        ax.tick_params(labelsize=7)
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

    plt.tight_layout(pad=3.0, h_pad=2.0, w_pad=2.0)
    plt.subplots_adjust(top=0.86)
    
    # 如果沒有有效結果，顯示警告並返回
    if not has_valid_results:
        print(f"警告: 喺 {title_suffix} 策略類型中冇搵到有效結果，唔會生成熱力圖")
        plt.close(fig)
        return []
    
    # 根據 SAVE_FIGURES 設置決定是儲存圖片還是顯示圖片
    if SAVE_FIGURES:
        # 創建 backtest_result 目錄（如果不存在）
        os.makedirs("backtest_result", exist_ok=True)
        
        # 創建特定因子嘅目錄
        factor_dir = f"backtest_result/{factor_name.replace(' ', '_')}"
        os.makedirs(factor_dir, exist_ok=True)
        
        # 添加好 factors 數量指標
        good_factor_count = len(good_factors)
        good_factor_indicator = f"_{good_factor_count}_good_factors" if good_factor_count > 0 else ""
        
        # 添加 metric_key 到文件名（如果有）
        metric_suffix = f"_{metric_key}" if metric_key and metric_key != "/" else ""
        
        # 策略類型同風格嘅簡短命名
        short_title = title_suffix.replace('Momentum', 'M').replace('Reversion', 'R').replace('Long Only', 'L').replace('Short Only', 'S').replace('Long-Short', 'LS')
        
        # 生成唯一檔案名（添加時間戳）- 使用毫秒級時間戳確保唯一性
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S%f")[:17]
        
        # 儲存圖片到因子目錄
        filename = f"{factor_dir}/wf_heatmap_{underlying}_{resolution}{metric_suffix}_{short_title.replace(' ', '_').replace('-', '_')}{good_factor_indicator}_{timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close(fig)
        print(f"儲存 Walk Forward 熱力圖為 {filename}")
    else:
        # 顯示圖片
        plt.show()

    return good_factors


def calculate_alpha_beta(df):
    """
    Calculate alpha, beta, and related statistics using statsmodels OLS regression.
    
    Parameters:
    - df: DataFrame containing 'pnl' (strategy returns) and 'percentage_change' (benchmark returns)
    
    Returns:
    - alpha: Intercept of the regression (annualized)
    - beta: Slope coefficient of the regression
    - r_squared: R-squared value of the regression
    - alpha_p_value: p-value of the alpha coefficient
    - beta_p_value: p-value of the beta coefficient
    """
    # Create DataFrame for regression (drop any NaN or inf values)
    clean_df = df[['percentage_change', 'pnl']].replace([np.inf, -np.inf], np.nan).dropna()
    
    # If we don't have enough data after cleaning, return zeros
    if len(clean_df) < 2:
        return 0.0, 0.0, 0.0, 1.0, 1.0
    
    # Add constant for regression to calculate intercept (alpha)
    X = sm.add_constant(clean_df['percentage_change'])
    
    # Perform regression
    model = sm.OLS(clean_df['pnl'], X).fit()
    
    # Extract results
    alpha = model.params['const']
    beta = model.params['percentage_change']
    r_squared = model.rsquared
    alpha_p_value = model.pvalues['const']  # p-value for alpha
    beta_p_value = model.pvalues['percentage_change']  # p-value for beta
    
    # Annualize alpha based on resolution (if needed)
    alpha_annualized = alpha * time_variable
    
    return alpha_annualized, beta, r_squared, alpha_p_value, beta_p_value


# 在文件底部添加以下代碼，確保獨立運行時能正常顯示圖表
if __name__ == "__main__":
    # 設置測試參數
    SAVE_FIGURES = True  # 設置為 True 以儲存圖片
    print(f"SAVE_FIGURES 設置為 {SAVE_FIGURES}")
    
    # 顯示基本配置信息
    print("\n===== 回測基本配置 =====")
    print(f"資產: {underlying}")
    print(f"因子: {factor_name}")
    if metric_key and metric_key != "/":
        print(f"Metric Key: {metric_key}")
    print(f"解析度: {resolution}")
    
    # 計算並顯示更多配置
    shift_period = get_shift_periods(resolution)
    time_var = get_time_variable(resolution)
    print(f"Shift Period: {shift_period}")
    print(f"訓練期比例: {DEFAULT_TRAIN_RATIO * 100:.0f}%")
    
    # 顯示回測時間範圍
    start_date = pd.to_datetime(since, unit='s').strftime('%Y-%m-%d')
    end_date = pd.to_datetime(until, unit='s').strftime('%Y-%m-%d')
    print(f"回測時間範圍: {start_date} 至 {end_date}")
    
    # 顯示策略運行相關信息
    print("\n===== 運行策略 =====")
    
    # run_strategy(mode='optimize')
    # run_strategy(mode='walkforward')
    run_strategy(mode='backtest', strategy_name='MinMax', strategy_type='long_short', style='momentum', window=4560, threshold=0.1) 

    # strategy name: 'MA Diff', 'Z-Score', 'RSI', 'MinMax', 'Robust Scaling', 'MA Cross', 'Box-Cox', 'Rate of Change', 'Divergence'
    # strategy_Type: 'long_only', 'short_only', 'long_short'
    # style: 'momentum', 'reversion'
    # mode: 'optimize', 'backtest', 'walkforward'