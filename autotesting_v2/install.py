import os
import shutil
import sys
import glob

def clear_screen():
    """Clear the terminal screen based on OS"""
    # Use a more compatible approach to clear the screen
    if os.name == 'nt':
        os.system('cls')
    else:
        # Check if TERM is set before using 'clear'
        if 'TERM' in os.environ:
            os.system('clear')
        else:
            # Print fewer newlines for a cleaner appearance
            print('\n' * 5)

def select_version():
    """Prompt user to select a version to clone from"""
    clear_screen()
    
    print("==== Backtest Version Selection ====")
    print("Select which version to clone from:")
    print("1. bybit_backtest_v1")
    print("6. hoho_backtest_v6")
    print("7. hoho_backtest_v7")
    print("8. hoho_backtest_v8")
    print("9. hoho_backtest_v9")
    print("0. Exit")
    print("==================================")
    
    while True:
        try:
            choice = input("\nEnter your choice (0-9): ")
            
            if choice == '0':
                print("Exiting program.")
                sys.exit(0)
            
            if choice == '1':
                return "bybit_backtest_v1"
            
            if choice in ['6', '7', '8', '9']:
                return f"hoho_backtest_v{choice}"
            
            print("Invalid choice. Please select 1, 6, 7, 8, 9 or 0 to exit.")
        except KeyboardInterrupt:
            print("\nOperation cancelled by user. Exiting.")
            sys.exit(1)

def find_directory(version_dir):
    """Find the directory containing the version directory"""
    # Start at the current directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # First check if directories exist at the same level as current directory
    parent_dir = os.path.dirname(current_dir)
    
    # Search paths
    search_paths = [
        # Check in parent directory
        os.path.join(parent_dir, version_dir),
        # Check in current directory
        os.path.join(current_dir, version_dir),
        # Check other possible locations
        os.path.abspath(version_dir),
        os.path.join(os.path.dirname(parent_dir), version_dir)
    ]
    
    # Search for the directory in multiple possible locations
    for path in search_paths:
        if os.path.exists(path) and os.path.isdir(path):
            return path
    
    # If not found in standard locations, search recursively from the parent directory
    # but limit depth to avoid excessive searching
    for root, dirs, _ in os.walk(parent_dir, topdown=True, followlinks=False):
        # Limit subdirectory depth
        if root.count(os.sep) - parent_dir.count(os.sep) > 2:
            dirs.clear()  # Don't go deeper
            continue
            
        if version_dir in dirs:
            return os.path.join(root, version_dir)
    
    return None

def clone_files(version_dir):
    """Clone all .py files from the selected version directory"""
    # Find the source directory
    source_dir = find_directory(version_dir)
    
    if not source_dir:
        print(f"Error: Source directory '{version_dir}' not found.")
        # Print debugging information
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)
        print(f"\nDebugging information:")
        print(f"Script location: {os.path.abspath(__file__)}")
        print(f"Current directory: {current_dir}")
        print(f"Parent directory: {parent_dir}")
        print(f"\nSearching for directories matching '{version_dir}'...")
        
        # Try to find matching directories
        for search_dir in [parent_dir, current_dir, os.path.dirname(parent_dir)]:
            pattern = os.path.join(search_dir, version_dir.replace("v1", "v*") if "bybit" in version_dir else "hoho_backtest_v*")
            matches = glob.glob(pattern)
            if matches:
                print(f"Found matching directories in {search_dir}:")
                for match in matches:
                    print(f"  - {match}")
            else:
                print(f"No matching directories found in {search_dir}")
        
        return False
    
    print(f"Found source directory: {source_dir}")
    
    # Get the current directory where the script is running
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Get all .py files from source directory
    source_files = glob.glob(os.path.join(source_dir, "*.py"))
    
    if not source_files:
        print(f"Warning: No .py files found in {source_dir}.")
        return False
    
    success = True
    
    for source_file in source_files:
        # Extract just the filename
        file_name = os.path.basename(source_file)
        # Target is in the current directory
        target_file = os.path.join(current_dir, file_name)
        
        try:
            shutil.copy2(source_file, target_file)
        except Exception as e:
            print(f"Error copying {file_name}: {str(e)}")
            success = False
    
    return success

def main():
    """Main function to run the version selection and cloning"""
    try:
        version_dir = select_version()
        print(f"\nCloning files from {version_dir}...")
        
        if clone_files(version_dir):
            print("\nFiles successfully cloned!")
        else:
            print("\nNot all files could be cloned. Check the messages above.")
    
    except Exception as e:
        print(f"An unexpected error occurred: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 