import ccxt
import pandas as pd
import numpy as np
import datetime
import time
import requests
import yaml  # install package PyYAML
import glob
import os
import matplotlib.pyplot as plt
import seaborn as sns
import re
import sys
import traceback

# Bybit VIP level rate limits (requests per second)
BYBIT_RATE_LIMITS = {
    '0': 10,  # Default
    '1': 20,  # VIP 1
    '2': 40,  # VIP 2
    '3': 60,  # VIP 3
    '4': 60,  # VIP 4
    '5': 60,  # VIP 5
    'S': 60,  # VIP S
    'Default': 10  # Fallback
}

# ===== Print full data table =====
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)

# ===== Global Settings =====

# Load key file from key.yaml
# Get the directory of the current script for absolute path resolution
script_dir = os.path.dirname(os.path.abspath(__file__))
key_file = os.path.join(script_dir, 'config/key.yaml')

with open(key_file) as f:
    keys = yaml.safe_load(f)
print('Loading key file', key_file, ".....")

selected_exchange = keys.get('EXCHANGE').upper()
weight_strategy = keys.get('WEIGHT', 'EQUAL').upper()  # Get weight strategy from key.yaml
leverage = keys.get('LEVERAGE', 1)  # Get leverage from key.yaml
rebalance_freq = keys.get('REBALANCE_FREQ', '1d')  # Get rebalance frequency from key.yaml
portfolio_freq = keys.get('PORTFOLIO_FREQ', 20)  # Get portfolio frequency from key.yaml
PNL_DAYS_TO_FETCH = keys.get('PNL_DAYS_TO_FETCH', 30)  # Get days to fetch PNL data from key.yaml
print(f"Selected Exchange: {selected_exchange}")
print(f"Weight Strategy: {weight_strategy}")
print(f"Leverage: {leverage}")
print(f"Rebalance Frequency: {rebalance_freq}")
print(f"Portfolio Frequency: {portfolio_freq}")
print(f"PNL Days to Fetch: {PNL_DAYS_TO_FETCH}")

# Initialize global variables for VIP level and rate limit
BYBIT_VIP_LEVEL = None
BYBIT_RATE_LIMIT = None

# Handle different exchange logic
if selected_exchange == "BYBIT":
    # Get Bybit VIP level and set rate limit
    vip_level = str(keys.get('VIP', '0'))
    BYBIT_VIP_LEVEL = vip_level
    BYBIT_RATE_LIMIT = BYBIT_RATE_LIMITS.get(vip_level, 10)
    print(f"Bybit VIP Level: {BYBIT_VIP_LEVEL} - Rate Limit: {BYBIT_RATE_LIMIT} requests/second")

    EXCHANGE = ccxt.bybit({
        'apiKey': keys['APIKEY'],
        'secret': keys['SECRET'],
        'enableRateLimit': True,  # CCXT will automatically throttle requests to comply with Bybit's rate limits.
        'options': {
            'recvWindow': 10000,
            'adjustTime': True,  # Enables automatic adjustment of the local timestamp
        },
        'rateLimit': 1000 / BYBIT_RATE_LIMIT  # Convert requests/second to ms delay
    })
    if keys['LIVE_ACCOUNT'] == "N":
        EXCHANGE.enable_demo_trading(True)
    try:
        time_diff = EXCHANGE.load_time_difference()  # Adjust the local timestamp offset with server time
    except Exception as e:
        print("Failed to load time difference for Bybit:", e)
elif selected_exchange == "BINANCE":
    EXCHANGE = ccxt.binance({
        'apiKey': keys['APIKEY'],
        'secret': keys['SECRET'],
        'enableRateLimit': True,  # CCXT will automatically throttle requests to comply with Binance's rate limits.
        'options': {'defaultType': 'future'}})
    if keys['LIVE_ACCOUNT'] == "N":
        EXCHANGE.set_sandbox_mode(True)
elif selected_exchange == "OKX":
    EXCHANGE = ccxt.okx({
        'apiKey': keys['APIKEY'],
        'secret': keys['SECRET'],
        'password': keys.get('PASSPHRASE', ''),  # OKX requires a passphrase
        'enableRateLimit': True,
        'options': {'defaultType': 'swap'}})  # Use swap for perpetual futures
    if keys['LIVE_ACCOUNT'] == "N":
        EXCHANGE.set_sandbox_mode(True)
else:
    raise ValueError(f"Unsupported exchange: {selected_exchange}")


def get_acct_bal():
    account_balance = 0
    margin_details = {}
    collateral_value = {}

    if selected_exchange == "BYBIT":
        balance = EXCHANGE.fetch_balance()
        # First check if marginEquity is directly available in the API response
        if 'info' in balance and isinstance(balance['info'], dict):
            if 'marginEquity' in balance['info']:
                # Use margin equity directly if available
                account_balance = round(float(balance['info']['marginEquity']), 2)
                print(f"Using Bybit margin equity: {account_balance}")
                return account_balance
            elif 'totalEquity' in balance['info']:
                # Use total equity as a fallback
                account_balance = round(float(balance['info']['totalEquity']), 2)
                print(f"Using Bybit total equity: {account_balance}")
                return account_balance
            elif 'totalWalletBalance' in balance['info']:
                # Use total wallet balance as another fallback
                account_balance = round(float(balance['info']['totalWalletBalance']), 2)
                print(f"Using Bybit total wallet balance: {account_balance}")
                return account_balance

        # If margin equity is not directly available, try to get it from account overview
        try:
            account_info = EXCHANGE.private_get_v5_account_wallet_balance({'accountType': 'UNIFIED'})
            if 'result' in account_info and 'list' in account_info['result'] and account_info['result']['list']:
                account_data = account_info['result']['list'][0]
                if 'totalEquity' in account_data:
                    account_balance = round(float(account_data['totalEquity']), 2)
                    print(f"Using Bybit account overview total equity: {account_balance}")
                    return account_balance
                elif 'totalMarginBalance' in account_data:
                    account_balance = round(float(account_data['totalMarginBalance']), 2)
                    print(f"Using Bybit account overview margin balance: {account_balance}")
                    return account_balance
        except Exception as e:
            print(f"Error fetching Bybit account overview: {e}. Falling back to collateral calculation.")

        # Fall back to the original method of summing up collaterals
        # Get all collaterals available in the unified account
        for currency in balance:
            if currency != 'info' and currency != 'timestamp' and currency != 'datetime' and currency != 'free' and currency != 'used' and currency != 'total':
                if isinstance(balance[currency], dict) and 'total' in balance[currency] and balance[currency][
                    'total'] > 0:
                    # Check if the asset is opted in as collateral
                    is_collateral = True
                    if 'info' in balance and isinstance(balance['info'], dict):
                        # For Bybit, check if the asset is opted in as collateral
                        if 'coin' in balance['info'] and isinstance(balance['info']['coin'], list):
                            for coin_info in balance['info']['coin']:
                                if coin_info.get('coin') == currency:
                                    # If availableToWithdraw is false, it might be because it's used as collateral
                                    is_collateral = coin_info.get('availableToWithdraw', True) or coin_info.get(
                                        'transferable', True)
                                    # For newer API versions that explicitly state collateral status
                                    if 'isUsedAsCollateral' in coin_info:
                                        is_collateral = coin_info.get('isUsedAsCollateral', False)
                                    break

                    if is_collateral:
                        margin_details[currency] = {
                            'total': round(balance[currency]['total'], 4),
                            'free': round(balance[currency]['free'], 4),
                            'used': round(balance[currency]['used'], 4)
                        }

                        # Calculate USD value of each collateral
                        try:
                            if currency == 'USDT' or currency == 'USDC':
                                # Stablecoins at 1:1 ratio
                                collateral_value[currency] = balance[currency]['total']
                            else:
                                # For other crypto, get current market price
                                ticker_symbol = f"{currency}/USDT"
                                ticker = EXCHANGE.fetch_ticker(ticker_symbol)
                                price = ticker['last']
                                collateral_value[currency] = balance[currency]['total'] * price
                        except Exception as e:
                            print(f"Error getting price for {currency}: {e}. Using fallback method.")
                            # Fallback: use the exchange's valuation if available
                            collateral_value[currency] = balance[currency]['total']

        # Calculate total account balance from all collaterals
        account_balance = round(sum(collateral_value.values()), 2)

        # Ensure we have at least USDT value as fallback
        if account_balance == 0 and 'USDT' in balance and isinstance(balance['USDT'], dict) and 'total' in balance[
            'USDT']:
            account_balance = round(balance['USDT']['total'], 2)
            print("Warning: Using only USDT balance as fallback.")

    elif selected_exchange == "BINANCE":
        balance = EXCHANGE.fetch_balance()
        total_usd_value = 0

        # For Binance, the totalMarginBalance already includes all collaterals in USD value
        if 'info' in balance:
            if 'totalMarginBalance' in balance['info']:
                account_balance = round(float(balance['info']['totalMarginBalance']), 2)
            elif 'totalWalletBalance' in balance['info']:
                account_balance = round(float(balance['info']['totalWalletBalance']), 2)
            else:
                print("Warning: Total margin balance not found. Calculating manually.")
                account_balance = 0

        # Get all assets from the margins section and calculate their USD value
        if 'info' in balance and 'assets' in balance['info']:
            for asset in balance['info']['assets']:
                if 'asset' in asset and 'walletBalance' in asset and 'availableBalance' in asset:
                    currency = asset['asset']
                    wallet_balance = float(asset['walletBalance'])

                    if wallet_balance > 0:
                        margin_details[currency] = {
                            'total': round(wallet_balance, 4),
                            'free': round(float(asset['availableBalance']), 4),
                            'used': round(wallet_balance - float(asset['availableBalance']), 4)
                        }

                        # If we need to manually calculate total balance
                        if account_balance == 0:
                            try:
                                if currency == 'USDT' or currency == 'USDC' or currency == 'BUSD':
                                    # Stablecoins at 1:1 ratio
                                    usd_value = wallet_balance
                                elif 'usdValue' in asset:
                                    # Use exchange-provided USD value if available
                                    usd_value = float(asset['usdValue'])
                                else:
                                    # For other crypto, get current market price
                                    ticker_symbol = f"{currency}/USDT"
                                    ticker = EXCHANGE.fetch_ticker(ticker_symbol)
                                    price = ticker['last']
                                    usd_value = wallet_balance * price

                                collateral_value[currency] = usd_value
                                total_usd_value += usd_value
                            except Exception as e:
                                print(f"Error getting price for {currency}: {e}")

            # If we manually calculated, update the account balance
            if account_balance == 0:
                account_balance = round(total_usd_value, 2)

    elif selected_exchange == "OKX":
        try:
            # For OKX, fetch balance of the trading account
            balance = EXCHANGE.fetch_balance()
            if 'info' in balance and 'data' in balance['info']:
                for item in balance['info']['data']:
                    if 'totalEq' in item:
                        account_balance = float(item['totalEq'])
                        break

            if account_balance == 0:
                # Fallback calculation
                balance_items = balance.get('total', {})
                for currency, amount in balance_items.items():
                    if amount > 0:
                        if currency in ['USDT', 'USDC']:
                            account_balance += amount
                        else:
                            try:
                                ticker = EXCHANGE.fetch_ticker(f"{currency}/USDT")
                                account_balance += amount * ticker['last']
                            except Exception as e:
                                print(f"Error calculating value for {currency}: {e}")
        except Exception as e:
            print(f"Error fetching OKX account balance: {e}")
            account_balance = 0

    return account_balance


def send_tg(message):
    bot_token = keys['TG_BOT_TOKEN']
    chat_id = keys['TG_CHATID']
    url = f"https://api.telegram.org/bot{bot_token}/sendMessage"

    # Telegram has a message length limit of 4096 characters
    MAX_MESSAGE_LENGTH = 4000  # Using a slightly smaller limit for safety

    # Enhanced retry configuration for rate limiting
    max_retries = 10  # Increased from 3 to 10
    base_delay = 2  # Increased base delay from 1 to 2 seconds
    max_delay = 120  # Increased max delay from 30 to 120 seconds
    timeout = 30  # Increased timeout from 15 to 30 seconds
    rate_limit_delay = 60  # Special delay for rate limiting

    # If message is too long, split it into chunks
    if len(message) > MAX_MESSAGE_LENGTH:
        message_chunks = []
        for i in range(0, len(message), MAX_MESSAGE_LENGTH):
            message_chunks.append(message[i:i + MAX_MESSAGE_LENGTH])

        responses = []
        for chunk_idx, chunk in enumerate(message_chunks):
            print(f"Sending message chunk {chunk_idx + 1}/{len(message_chunks)}")

            for attempt in range(max_retries):
                try:
                    response = requests.post(
                        url,
                        json={
                            "chat_id": chat_id,
                            "text": chunk
                        },
                        timeout=timeout
                    )

                    if response.status_code == 200:
                        response_json = response.json()
                        if response_json.get('ok', False):
                            responses.append(response_json)
                            print(f"Chunk {chunk_idx + 1} sent successfully on attempt {attempt + 1}")
                            break
                        else:
                            error_description = response_json.get('description', 'Unknown error')
                            print(f"Telegram API error for chunk {chunk_idx + 1}: {error_description}")

                            # Handle rate limiting with special delays
                            if 'retry after' in error_description.lower():
                                import re
                                retry_match = re.search(r'retry after (\d+)', error_description)
                                if retry_match:
                                    retry_delay = int(retry_match.group(1))
                                    print(f"Rate limited. Waiting {retry_delay} seconds as requested by Telegram...")
                                    time.sleep(retry_delay)
                                    continue
                            elif 'too many requests' in error_description.lower():
                                delay = min(rate_limit_delay * (2 ** (attempt // 2)), max_delay)
                                print(f"Rate limited. Waiting {delay} seconds before retry...")
                                time.sleep(delay)
                                continue
                    elif response.status_code == 429:
                        # Handle HTTP 429 specifically
                        retry_after = response.headers.get('Retry-After')
                        if retry_after:
                            delay = int(retry_after)
                            print(f"HTTP 429 - Rate limited. Waiting {delay} seconds as specified in headers...")
                            time.sleep(delay)
                        else:
                            delay = min(rate_limit_delay * (2 ** (attempt // 2)), max_delay)
                            print(f"HTTP 429 - Rate limited. Waiting {delay} seconds...")
                            time.sleep(delay)
                        continue
                    else:
                        print(f"HTTP error {response.status_code} for chunk {chunk_idx + 1}")

                except requests.exceptions.Timeout:
                    print(f"Timeout error for chunk {chunk_idx + 1}, attempt {attempt + 1}")
                except requests.exceptions.RequestException as e:
                    print(f"Request error for chunk {chunk_idx + 1}, attempt {attempt + 1}: {e}")
                except Exception as e:
                    print(f"Unexpected error for chunk {chunk_idx + 1}, attempt {attempt + 1}: {e}")

                # Exponential backoff with jitter for non-rate-limit errors
                if attempt < max_retries - 1:
                    delay = min(base_delay * (2 ** attempt), max_delay)
                    # Add jitter to prevent thundering herd
                    jitter = delay * 0.1 * (0.5 - (time.time() % 1))
                    final_delay = max(1, delay + jitter)
                    print(f"Waiting {final_delay:.1f} seconds before retry...")
                    time.sleep(final_delay)
            else:
                print(f"Failed to send chunk {chunk_idx + 1} after {max_retries} attempts")
                responses.append(None)

            # Longer delay between chunks to avoid rate limiting
            if chunk_idx < len(message_chunks) - 1:  # Don't delay after the last chunk
                inter_chunk_delay = 3  # Increased from 0.5 to 3 seconds
                print(f"Waiting {inter_chunk_delay} seconds before sending next chunk...")
                time.sleep(inter_chunk_delay)

        return responses
    else:
        # Single message
        for attempt in range(max_retries):
            try:
                response = requests.post(
                    url,
                    json={
                        "chat_id": chat_id,
                        "text": message
                    },
                    timeout=timeout
                )

                if response.status_code == 200:
                    response_json = response.json()
                    if response_json.get('ok', False):
                        print(f"Message sent successfully on attempt {attempt + 1}")
                        return response_json
                    else:
                        error_description = response_json.get('description', 'Unknown error')
                        print(f"Telegram API error: {error_description}")

                        # Handle rate limiting with special delays
                        if 'retry after' in error_description.lower():
                            import re
                            retry_match = re.search(r'retry after (\d+)', error_description)
                            if retry_match:
                                retry_delay = int(retry_match.group(1))
                                print(f"Rate limited. Waiting {retry_delay} seconds as requested by Telegram...")
                                time.sleep(retry_delay)
                                continue
                        elif 'too many requests' in error_description.lower():
                            delay = min(rate_limit_delay * (2 ** (attempt // 2)), max_delay)
                            print(f"Rate limited. Waiting {delay} seconds before retry...")
                            time.sleep(delay)
                            continue
                elif response.status_code == 429:
                    # Handle HTTP 429 specifically
                    retry_after = response.headers.get('Retry-After')
                    if retry_after:
                        delay = int(retry_after)
                        print(f"HTTP 429 - Rate limited. Waiting {delay} seconds as specified in headers...")
                        time.sleep(delay)
                    else:
                        delay = min(rate_limit_delay * (2 ** (attempt // 2)), max_delay)
                        print(f"HTTP 429 - Rate limited. Waiting {delay} seconds...")
                        time.sleep(delay)
                    continue
                else:
                    print(f"HTTP error {response.status_code}: {response.text}")

            except requests.exceptions.Timeout:
                print(f"Timeout error on attempt {attempt + 1}")
            except requests.exceptions.RequestException as e:
                print(f"Request error on attempt {attempt + 1}: {e}")
            except Exception as e:
                print(f"Unexpected error on attempt {attempt + 1}: {e}")

            # Exponential backoff with jitter for non-rate-limit errors
            if attempt < max_retries - 1:
                delay = min(base_delay * (2 ** attempt), max_delay)
                # Add jitter to prevent thundering herd
                jitter = delay * 0.1 * (0.5 - (time.time() % 1))
                final_delay = max(1, delay + jitter)
                print(f"Waiting {final_delay:.1f} seconds before retry...")
                time.sleep(final_delay)

        print(f"Failed to send message after {max_retries} attempts")
        return None


def send_photo(photo_path):
    bot_token = keys['TG_BOT_TOKEN']
    chat_id = keys['TG_CHATID']
    url = f"https://api.telegram.org/bot{bot_token}/sendPhoto"

    # Enhanced retry configuration for rate limiting
    max_retries = 10  # Increased from 5 to 10
    base_delay = 3  # Increased base delay from 2 to 3 seconds
    max_delay = 180  # Increased max delay from 60 to 180 seconds
    timeout = 60  # Increased timeout from 30 to 60 seconds
    rate_limit_delay = 90  # Special delay for rate limiting

    for attempt in range(max_retries):
        try:
            # Check if file exists and is readable
            if not os.path.exists(photo_path):
                print(f"Error: Photo file does not exist: {photo_path}")
                return None

            if not os.access(photo_path, os.R_OK):
                print(f"Error: Cannot read photo file: {photo_path}")
                return None

            # Get file size for logging
            file_size = os.path.getsize(photo_path)
            print(
                f"Attempt {attempt + 1}/{max_retries}: Sending photo {os.path.basename(photo_path)} ({file_size} bytes)")

            with open(photo_path, 'rb') as photo:
                response = requests.post(
                    url,
                    data={"chat_id": chat_id},
                    files={"photo": photo},
                    timeout=timeout
                )

            # Check response status
            if response.status_code == 200:
                response_json = response.json()
                if response_json.get('ok', False):
                    print(f"Photo sent successfully on attempt {attempt + 1}")
                    return response_json
                else:
                    error_description = response_json.get('description', 'Unknown error')
                    print(f"Telegram API error: {error_description}")

                    # Handle specific Telegram errors with enhanced rate limiting
                    if 'retry after' in error_description.lower():
                        # Extract retry delay from error message
                        import re
                        retry_match = re.search(r'retry after (\d+)', error_description)
                        if retry_match:
                            retry_delay = int(retry_match.group(1))
                            print(f"Rate limited. Waiting {retry_delay} seconds as requested by Telegram...")
                            time.sleep(retry_delay)
                            continue
                    elif 'too many requests' in error_description.lower():
                        # Handle rate limiting with exponential backoff
                        delay = min(rate_limit_delay * (2 ** (attempt // 2)), max_delay)
                        print(f"Rate limited. Waiting {delay} seconds before retry...")
                        time.sleep(delay)
                        continue
                    elif 'file too large' in error_description.lower():
                        print(f"File too large error - cannot retry: {error_description}")
                        return None
                    elif 'bad request' in error_description.lower():
                        print(f"Bad request error - cannot retry: {error_description}")
                        return None
            elif response.status_code == 429:
                # Handle HTTP 429 specifically
                retry_after = response.headers.get('Retry-After')
                if retry_after:
                    delay = int(retry_after)
                    print(f"HTTP 429 - Rate limited. Waiting {delay} seconds as specified in headers...")
                    time.sleep(delay)
                else:
                    delay = min(rate_limit_delay * (2 ** (attempt // 2)), max_delay)
                    print(f"HTTP 429 - Rate limited. Waiting {delay} seconds...")
                    time.sleep(delay)
                continue
            else:
                print(f"HTTP error {response.status_code}: {response.text}")

        except requests.exceptions.Timeout:
            print(f"Timeout error on attempt {attempt + 1}")
        except requests.exceptions.ConnectionError:
            print(f"Connection error on attempt {attempt + 1}")
        except requests.exceptions.RequestException as e:
            print(f"Request error on attempt {attempt + 1}: {e}")
        except FileNotFoundError:
            print(f"Error: Photo file not found: {photo_path}")
            return None
        except PermissionError:
            print(f"Error: Permission denied accessing photo file: {photo_path}")
            return None
        except Exception as e:
            print(f"Unexpected error on attempt {attempt + 1}: {e}")

        # Enhanced exponential backoff with jitter for non-rate-limit errors
        if attempt < max_retries - 1:  # Don't delay after the last attempt
            delay = min(base_delay * (2 ** attempt), max_delay)
            # Add jitter to prevent thundering herd
            jitter = delay * 0.1 * (0.5 - (time.time() % 1))  # ±10% jitter
            final_delay = max(1, delay + jitter)
            print(f"Waiting {final_delay:.1f} seconds before retry...")
            time.sleep(final_delay)

    print(f"Failed to send photo after {max_retries} attempts: {photo_path}")
    return None


def calculate_portfolio_sharpe():
    # Set current_leverage equal to the global leverage variable
    current_leverage = leverage

    # Helper function to handle string or numeric values for formatting
    def format_sr(value):
        if isinstance(value, str):
            return value
        else:
            return f"{value:.2f}"

    data_dir = os.path.join(script_dir, 'data')
    if not os.path.exists(data_dir):
        print("No data directory found. Creating one...")
        os.makedirs(data_dir)
        return 'N/A', 'N/A'

    # Read all CSV files from the data directory and process based on timeframe
    csv_files = glob.glob(os.path.join(data_dir, "*.csv"))
    daily_pnl_list = []  # list to store daily pnl series
    daily_bnh_pnl_list = []  # list to store daily buy-and-hold pnl series
    daily_pos_list = []  # list to store daily position series

    # Map strategy names to their data (for weight-based calculation later)
    strategy_data_map = {}

    # Files to exclude from timeframe detection
    excluded_files = ["bybit_pnl.csv", "binance_pnl.csv", "max_positions.csv", "strategy_performance.csv"]
    excluded_patterns = ["trade_log_", "position_over_time_debug"]

    # Supported timeframes ordered by descending length to avoid substring conflicts
    supported_timeframes = ["15m", "10m", "30m", "12h", "24h", "1d", "1m", "3m", "5m", "1h", "2h", "4h", "6h"]

    for file in csv_files:
        # Skip excluded files
        basename = os.path.basename(file)
        if basename in excluded_files:
            continue

        # Skip files matching excluded patterns
        if any(pattern in basename for pattern in excluded_patterns):
            continue

        found_tf = None
        for tf in supported_timeframes:
            if tf in basename:
                found_tf = tf
                break
        if found_tf is None:
            print(f"Warning: Could not determine timeframe for {file}. Skipping.")
            continue

        try:
            df = pd.read_csv(file)
        except pd.errors.EmptyDataError:
            print(f"Warning: {file} is empty. Skipping.")
            continue

        df['t'] = pd.to_datetime(df['t'])
        # Use daily data as is if the file is already in daily resolution
        if found_tf in ["24h", "1d"]:
            df.set_index('t', inplace=True)
            daily_df = df['pnl']
            daily_bnh_df = df['chg']
            daily_pos_df = df['pos']
        else:
            # For intraday timeframes, resample to daily
            df.set_index('t', inplace=True)
            daily_df = df['pnl'].resample('D').sum()
            daily_bnh_df = df['chg'].resample('D').sum()
            # For positions, use the last value of the day (end-of-day position)
            # This better represents the actual position held rather than daily average
            daily_pos_df = df['pos'].resample('D').last()

        if not daily_df.empty:
            daily_pnl_list.append(daily_df)

            # Extract strategy name for weighted calculation
            filename = os.path.basename(file).split('.')[0]
            if found_tf and found_tf in filename:
                # Handle both underscore and dash separators
                if f"{found_tf}_" in filename:
                    strategy_name = filename.split(f"{found_tf}_", 1)[1]
                elif f"{found_tf}-" in filename:
                    strategy_name = filename.split(f"{found_tf}-", 1)[1]
                else:
                    strategy_name = filename
            else:
                strategy_name = filename

            # Store the strategy's daily PnL series for weighted calculation later
            strategy_data_map[strategy_name] = {
                'pnl': daily_df,
                'bnh': daily_bnh_df,
                'pos': daily_pos_df
            }

        if not daily_bnh_df.empty:
            daily_bnh_pnl_list.append(daily_bnh_df)
        if not daily_pos_df.empty:
            daily_pos_list.append(daily_pos_df)

    # Load strategy weights from strategy_performance.csv if available
    strategy_weights = {}
    strategy_performance_file = os.path.join(data_dir, 'strategy_performance.csv')
    has_weights = False

    if os.path.exists(strategy_performance_file):
        try:
            strategy_df = pd.read_csv(strategy_performance_file)
            if not strategy_df.empty and 'strategy_name' in strategy_df.columns and 'weight_ratio' in strategy_df.columns:
                for _, row in strategy_df.iterrows():
                    strategy_weights[row['strategy_name']] = row['weight_ratio']
                has_weights = True
                print(f"Loaded {len(strategy_weights)} strategy weights from strategy_performance.csv")
            else:
                print("Strategy performance file does not contain required columns. Using equal weights.")
        except Exception as e:
            print(f"Error loading strategy weights: {e}. Using equal weights.")

    # Calculate EQUAL-weighted portfolio (keep unchanged for comparison)
    if daily_pnl_list:
        # Get the unified date range across all strategies
        all_dates = set()
        for pnl_series in daily_pnl_list:
            all_dates.update(pnl_series.index)

        # Sort dates chronologically
        all_dates = sorted(all_dates)

        # Create a DataFrame with all dates and initialize with zeros
        full_range_df = pd.DataFrame(index=all_dates)

        # Add each strategy's PnL, filling missing dates with zeros
        strategy_count = len(daily_pnl_list)
        for pnl_series in daily_pnl_list:
            # Reindex each series to the full date range
            reindexed_series = pnl_series.reindex(all_dates, fill_value=0)
            # Add to the DataFrame, dividing by the strategy count for equal weighting
            full_range_df = pd.concat([full_range_df, reindexed_series.to_frame()], axis=1)

        # Calculate the mean across all strategies for each date
        portfolio_daily = full_range_df.mean(axis=1).to_frame(name='pnl')

        # Add cumulative and drawdown fields
        portfolio_daily['cumu'] = portfolio_daily['pnl'].cumsum()
        portfolio_daily['dd'] = portfolio_daily['cumu'].cummax() - portfolio_daily['cumu']
    else:
        return 'N/A', 'N/A'

    if daily_bnh_pnl_list:
        # Apply the same unified date range approach for buy-and-hold
        all_bnh_dates = set()
        for bnh_series in daily_bnh_pnl_list:
            all_bnh_dates.update(bnh_series.index)

        all_bnh_dates = sorted(all_bnh_dates)
        full_bnh_range_df = pd.DataFrame(index=all_bnh_dates)

        for bnh_series in daily_bnh_pnl_list:
            reindexed_bnh = bnh_series.reindex(all_bnh_dates, fill_value=0)
            full_bnh_range_df = pd.concat([full_bnh_range_df, reindexed_bnh.to_frame()], axis=1)

        portfolio_bnh_daily = full_bnh_range_df.mean(axis=1).to_frame(name='pnl')
        portfolio_bnh_daily['cumu'] = portfolio_bnh_daily['pnl'].cumsum()
        portfolio_bnh_daily['dd'] = portfolio_bnh_daily['cumu'].cummax() - portfolio_bnh_daily['cumu']
    else:
        return 'N/A', 'N/A'

    if daily_pos_list:
        # Apply the same unified date range approach for positions
        all_pos_dates = set()
        for pos_series in daily_pos_list:
            all_pos_dates.update(pos_series.index)

        all_pos_dates = sorted(all_pos_dates)
        full_pos_range_df = pd.DataFrame(index=all_pos_dates)

        for pos_series in daily_pos_list:
            reindexed_pos = pos_series.reindex(all_pos_dates, fill_value=0)
            full_pos_range_df = pd.concat([full_pos_range_df, reindexed_pos.to_frame()], axis=1)

        # Reindex portfolio_daily to match the full position date range
        portfolio_daily = portfolio_daily.reindex(full_pos_range_df.index, fill_value=0)

        # Use the same weighting logic as PnL calculation
        if weight_strategy == 'EQUAL':
            # For equal weighting, use mean (same as PnL calculation)
            portfolio_daily['pos'] = full_pos_range_df.mean(axis=1)
        else:
            # For other weighting strategies, sum the positions (will be handled by weighted portfolio later)
            portfolio_daily['pos'] = full_pos_range_df.sum(axis=1)
    else:
        portfolio_daily['pos'] = 0  # or handle as needed

    # Calculate WEIGHTED portfolio only if we have weights and multiple strategies
    weighted_portfolio_daily = None
    if has_weights and len(strategy_data_map) > 1:
        try:
            # Filter to only strategies we have data for
            valid_strategies = {k: v for k, v in strategy_weights.items() if k in strategy_data_map}

            if valid_strategies:
                # Normalize weights if needed
                total_weight = sum(valid_strategies.values())
                if total_weight != 0:
                    normalized_weights = {k: w / total_weight for k, w in valid_strategies.items()}

                    # Create a combined DataFrame with all strategy PnLs
                    all_pnls = pd.DataFrame()

                    # Get unified date range for all strategies
                    all_weighted_dates = set()
                    for strategy, weight in normalized_weights.items():
                        if strategy in strategy_data_map:
                            all_weighted_dates.update(strategy_data_map[strategy]['pnl'].index)

                    # Sort dates chronologically
                    all_weighted_dates = sorted(all_weighted_dates)

                    # Create a DataFrame with all dates
                    all_pnls = pd.DataFrame(index=all_weighted_dates)

                    # Add each strategy's weighted PnL, filling missing dates with zeros
                    for strategy, weight in normalized_weights.items():
                        if strategy in strategy_data_map:
                            # Reindex to include all dates
                            reindexed_pnl = strategy_data_map[strategy]['pnl'].reindex(all_weighted_dates, fill_value=0)
                            # Multiply by weight
                            all_pnls[strategy] = reindexed_pnl * weight

                    if not all_pnls.empty:
                        # Create weighted daily PnL series
                        weighted_pnl = all_pnls.sum(axis=1)
                        weighted_portfolio_daily = weighted_pnl.to_frame(name='pnl')
                        weighted_portfolio_daily['cumu'] = weighted_portfolio_daily['pnl'].cumsum()
                        weighted_portfolio_daily['dd'] = weighted_portfolio_daily['cumu'].cummax() - \
                                                         weighted_portfolio_daily['cumu']

                        # Add weighted position data if available
                        weighted_pos = pd.Series(0, index=weighted_portfolio_daily.index)
                        for strategy, weight in normalized_weights.items():
                            if strategy in strategy_data_map and not strategy_data_map[strategy]['pos'].empty:
                                # Reindex to handle potential missing dates
                                pos_series = strategy_data_map[strategy]['pos'].reindex(weighted_portfolio_daily.index,
                                                                                        fill_value=0)
                                weighted_pos += pos_series * weight

                        weighted_portfolio_daily['pos'] = weighted_pos

                        print(f"Successfully created weighted portfolio with {len(valid_strategies)} strategies")
                    else:
                        print("No PnL data available for weighted strategies")
                else:
                    print("Total weight is zero. Cannot create weighted portfolio.")
            else:
                print("No valid strategies with weights found in data")
        except Exception as e:
            print(f"Error creating weighted portfolio: {e}")
            weighted_portfolio_daily = None

    # ===== Plot Equity Curve =====
    if not portfolio_daily.empty:
        # Use weighted portfolio if available, otherwise fall back to equal-weighted
        if weighted_portfolio_daily is not None:
            portfolio_daily = weighted_portfolio_daily

        # Use the cumulative returns from the selected portfolio approach
        # This will be our 1x (no leverage) version
        portfolio_daily_1x = portfolio_daily.copy()  # No leverage (1x) is the original
        cumulative_returns_1x = portfolio_daily_1x['cumu']
        cumulative_bnh_returns = portfolio_bnh_daily['cumu']

        # Create leveraged versions of the portfolio for comparison

        # 2x leveraged version
        portfolio_daily_2x = portfolio_daily.copy()
        portfolio_daily_2x['pnl'] = portfolio_daily['pnl'] * 2  # Double the daily returns
        portfolio_daily_2x['cumu'] = portfolio_daily_2x['pnl'].cumsum()  # Recalculate cumulative returns
        portfolio_daily_2x['dd'] = portfolio_daily_2x['cumu'].cummax() - portfolio_daily_2x[
            'cumu']  # Recalculate drawdowns
        if 'pos' in portfolio_daily.columns:
            portfolio_daily_2x['pos'] = portfolio_daily['pos'] * 2  # Double the position size

        # 3x leveraged version
        portfolio_daily_3x = portfolio_daily.copy()
        portfolio_daily_3x['pnl'] = portfolio_daily['pnl'] * 3  # Triple the daily returns
        portfolio_daily_3x['cumu'] = portfolio_daily_3x['pnl'].cumsum()  # Recalculate cumulative returns
        portfolio_daily_3x['dd'] = portfolio_daily_3x['cumu'].cummax() - portfolio_daily_3x[
            'cumu']  # Recalculate drawdowns
        if 'pos' in portfolio_daily.columns:
            portfolio_daily_3x['pos'] = portfolio_daily['pos'] * 3  # Triple the position size

        # Custom leverage version based on leverage setting from key.yaml
        portfolio_daily_custom = portfolio_daily.copy()
        custom_leverage = leverage  # Get leverage from key.yaml
        if custom_leverage != 1:  # Only apply if it's not 1x (to avoid duplicate calculations)
            portfolio_daily_custom['pnl'] = portfolio_daily['pnl'] * custom_leverage
            portfolio_daily_custom['cumu'] = portfolio_daily_custom['pnl'].cumsum()
            portfolio_daily_custom['dd'] = portfolio_daily_custom['cumu'].cummax() - portfolio_daily_custom['cumu']
            if 'pos' in portfolio_daily.columns:
                portfolio_daily_custom['pos'] = portfolio_daily['pos'] * custom_leverage

        # Create a "realistic" version that accounts for real-world trading costs
        portfolio_daily_realistic = portfolio_daily_custom.copy()
        if custom_leverage > 1:
            # Apply realistic adjustments for leveraged trading
            funding_cost_daily = 0.0001 * custom_leverage  # Approximate daily funding cost
            slippage_cost = 0.0002 * abs(portfolio_daily_realistic['pnl'])  # Slippage proportional to PnL

            # Reduce PnL by realistic costs
            portfolio_daily_realistic['pnl'] = portfolio_daily_realistic['pnl'] - funding_cost_daily - slippage_cost
            portfolio_daily_realistic['cumu'] = portfolio_daily_realistic['pnl'].cumsum()
            portfolio_daily_realistic['dd'] = portfolio_daily_realistic['cumu'].cummax() - portfolio_daily_realistic[
                'cumu']

        # Calculate metrics for 1x portfolio (no leverage)
        daily_return_mean_1x = portfolio_daily_1x['pnl'].mean()
        daily_return_std_1x = portfolio_daily_1x['pnl'].std()

        if daily_return_std_1x == 0:
            sharpe_ratio_1x = 'N/A'
        else:
            sharpe_ratio_1x = round((daily_return_mean_1x / daily_return_std_1x) * np.sqrt(365), 3)

        # Calculate metrics for 2x leveraged portfolio
        daily_return_mean_2x = portfolio_daily_2x['pnl'].mean()
        daily_return_std_2x = portfolio_daily_2x['pnl'].std()

        if daily_return_std_2x == 0:
            sharpe_ratio_2x = 'N/A'
        else:
            sharpe_ratio_2x = round((daily_return_mean_2x / daily_return_std_2x) * np.sqrt(365), 3)

        # Calculate metrics for 3x leveraged portfolio
        daily_return_mean_3x = portfolio_daily_3x['pnl'].mean()
        daily_return_std_3x = portfolio_daily_3x['pnl'].std()

        if daily_return_std_3x == 0:
            sharpe_ratio_3x = 'N/A'
        else:
            sharpe_ratio_3x = round((daily_return_mean_3x / daily_return_std_3x) * np.sqrt(365), 3)

        # Calculate metrics for custom leveraged portfolio
        daily_return_mean_custom = portfolio_daily_custom['pnl'].mean()
        daily_return_std_custom = portfolio_daily_custom['pnl'].std()

        if daily_return_std_custom == 0:
            sharpe_ratio_custom = 'N/A'
        else:
            sharpe_ratio_custom = round((daily_return_mean_custom / daily_return_std_custom) * np.sqrt(365), 3)

        # Calculate Sharpe Ratio for Buy and Hold
        daily_bnh_return_mean = portfolio_bnh_daily['pnl'].mean()
        daily_bnh_return_std = portfolio_bnh_daily['pnl'].std()

        if daily_bnh_return_std == 0:
            sharpe_ratio_bnh = 'N/A'
        else:
            sharpe_ratio_bnh = round((daily_bnh_return_mean / daily_bnh_return_std) * np.sqrt(365), 3)

        # Calculate Annual Return for all versions
        ar_1x = round(daily_return_mean_1x * 365, 3)
        ar_2x = round(daily_return_mean_2x * 365, 3)
        ar_3x = round(daily_return_mean_3x * 365, 3)
        ar_custom = round(daily_return_mean_custom * 365, 3)
        ar_bnh = round(daily_bnh_return_mean * 365, 3)

        # Calculate Maximum Drawdown and Calmar Ratio for all versions
        mdd_1x = round(portfolio_daily_1x['dd'].max(), 3)
        mdd_2x = round(portfolio_daily_2x['dd'].max(), 3)
        mdd_3x = round(portfolio_daily_3x['dd'].max(), 3)
        mdd_custom = round(portfolio_daily_custom['dd'].max(), 3)
        mdd_bnh = round(portfolio_bnh_daily['dd'].max(), 3)

        cr_1x = round(ar_1x / mdd_1x, 3) if mdd_1x != 0 else 0
        cr_2x = round(ar_2x / mdd_2x, 3) if mdd_2x != 0 else 0
        cr_3x = round(ar_3x / mdd_3x, 3) if mdd_3x != 0 else 0
        cr_custom = round(ar_custom / mdd_custom, 3) if mdd_custom != 0 else 0
        cr_bnh = round(ar_bnh / mdd_bnh, 3) if mdd_bnh != 0 else 0

        # Calculate the number of days in the data
        num_days = (portfolio_daily.index.max() - portfolio_daily.index.min()).days + 1

        # Calculate realistic liquidation risk assessment for leveraged trading
        # This provides guidance for margin trading risk management

        def calculate_comprehensive_risk_score(leverage, max_drawdown, annual_return, sharpe_ratio):
            """
            Calculate comprehensive risk score considering multiple risk factors
            Risk Score: 0-100 where higher = more risky

            For leverage > 1: Focuses on liquidation risk
            For leverage = 1: Focuses on drawdown risk, volatility risk, and strategy risk
            """

            if leverage > 1:
                # LEVERAGED RISK: Focus on liquidation risk
                # Conservative maintenance margin estimates by exchange
                maintenance_margins = {
                    'BYBIT': 0.005,  # 0.5% for most crypto pairs
                    'BINANCE': 0.01,  # 1% for most futures
                    'OKX': 0.005  # 0.5% for most perpetual swaps
                }

                maintenance_margin = maintenance_margins.get(selected_exchange, 0.01)

                # Calculate maximum allowable portfolio drawdown before margin call
                initial_margin_ratio = 1.0 / leverage
                allowable_drawdown = (initial_margin_ratio - maintenance_margin) / initial_margin_ratio

                # Calculate liquidation risk score
                if allowable_drawdown <= 0:
                    return 100  # Extremely high risk - even small moves can liquidate

                risk_ratio = max_drawdown / allowable_drawdown
                liquidation_risk = min(100, risk_ratio * 100)
                return round(liquidation_risk, 1)

            else:
                # UNLEVERAGED RISK: Comprehensive risk assessment for 1x leverage
                # Consider multiple risk factors for true risk picture

                # 1. Drawdown Risk (0-40 points): Based on maximum drawdown severity
                if max_drawdown <= 0.05:  # <= 5%
                    drawdown_risk = 5
                elif max_drawdown <= 0.10:  # 5-10%
                    drawdown_risk = 10
                elif max_drawdown <= 0.20:  # 10-20%
                    drawdown_risk = 20
                elif max_drawdown <= 0.35:  # 20-35%
                    drawdown_risk = 30
                else:  # > 35%
                    drawdown_risk = 40

                # 2. Volatility Risk (0-30 points): Based on Sharpe ratio (lower SR = higher volatility risk)
                if isinstance(sharpe_ratio, str) or sharpe_ratio == 'N/A':
                    volatility_risk = 25  # Unknown volatility = moderate risk
                elif sharpe_ratio >= 2.5:  # Excellent risk-adjusted returns
                    volatility_risk = 5
                elif sharpe_ratio >= 1.5:  # Good risk-adjusted returns
                    volatility_risk = 10
                elif sharpe_ratio >= 1.0:  # Acceptable risk-adjusted returns
                    volatility_risk = 15
                elif sharpe_ratio >= 0.5:  # Poor risk-adjusted returns
                    volatility_risk = 25
                else:  # Very poor risk-adjusted returns
                    volatility_risk = 30

                # 3. Strategy Risk (0-30 points): Based on annual return consistency
                if annual_return < 0:  # Negative returns
                    strategy_risk = 30
                elif annual_return < 0.05:  # 0-5% annual return
                    strategy_risk = 25
                elif annual_return < 0.15:  # 5-15% annual return
                    strategy_risk = 15
                elif annual_return < 0.30:  # 15-30% annual return
                    strategy_risk = 10
                else:  # > 30% annual return
                    strategy_risk = 5

                # Total unleveraged risk score (max 100)
                total_risk = drawdown_risk + volatility_risk + strategy_risk
                return round(min(100, total_risk), 1)

        # Function to convert numerical risk score to descriptive text
        def get_risk_level_text(risk_score):
            """Convert numerical risk score to descriptive text"""
            if risk_score <= 25:
                return "Low"
            elif risk_score <= 50:
                return "Moderate"
            elif risk_score <= 75:
                return "High"
            else:
                return "Very High"

        # Function to convert numerical risk score to colored circle emoji
        def get_risk_level_emoji(risk_score):
            """Convert numerical risk score to colored circle emoji"""
            if risk_score <= 25:
                return "🟢"  # Green circle - Low risk
            elif risk_score <= 50:
                return "🟡"  # Yellow circle - Moderate risk
            elif risk_score <= 75:
                return "🟠"  # Orange circle - High risk
            else:
                return "🔴"  # Red circle - Very High risk

        # Calculate comprehensive risk scores for different leverage levels
        risk_score_1x = calculate_comprehensive_risk_score(1, mdd_1x, ar_1x, sharpe_ratio_1x)
        risk_score_2x = calculate_comprehensive_risk_score(2, mdd_2x, ar_2x, sharpe_ratio_2x)
        risk_score_3x = calculate_comprehensive_risk_score(3, mdd_3x, ar_3x, sharpe_ratio_3x)
        risk_score_custom = calculate_comprehensive_risk_score(custom_leverage, mdd_custom, ar_custom,
                                                               sharpe_ratio_custom)
        risk_score_bnh = calculate_comprehensive_risk_score(1, mdd_bnh, ar_bnh,
                                                            sharpe_ratio_bnh)  # Buy & Hold comprehensive risk

        # Convert risk scores to descriptive text and emojis
        risk_text_1x = get_risk_level_text(risk_score_1x)
        risk_text_2x = get_risk_level_text(risk_score_2x)
        risk_text_3x = get_risk_level_text(risk_score_3x)
        risk_text_custom = get_risk_level_text(risk_score_custom)
        risk_text_bnh = get_risk_level_text(risk_score_bnh)

        # Convert risk scores to emojis for compact dashboard display
        risk_emoji_1x = get_risk_level_emoji(risk_score_1x)
        risk_emoji_2x = get_risk_level_emoji(risk_score_2x)
        risk_emoji_3x = get_risk_level_emoji(risk_score_3x)
        risk_emoji_custom = get_risk_level_emoji(risk_score_custom)
        risk_emoji_bnh = get_risk_level_emoji(risk_score_bnh)

        # Calculate maximum safe leverage dynamically based on historical drawdown
        def find_maximum_safe_leverage(base_max_drawdown, selected_exchange, safety_margin=0.8):
            """
            Find the maximum leverage that would not have caused liquidation
            based on historical maximum drawdown with a safety margin
            """
            # Exchange-specific maintenance margins
            maintenance_margins = {
                'BYBIT': 0.005,  # 0.5% for most crypto pairs
                'BINANCE': 0.01,  # 1% for most futures
                'OKX': 0.005  # 0.5% for most perpetual swaps
            }

            maintenance_margin = maintenance_margins.get(selected_exchange, 0.01)

            # Test different leverage levels to find maximum safe leverage
            max_safe_leverage = 1.0  # Start with no leverage

            # Test leverage levels from 1.1x to 10x in 0.1 increments
            for test_leverage in [x / 10.0 for x in range(11, 101)]:  # 1.1, 1.2, ... 10.0
                # Calculate what the max drawdown would be at this leverage
                leveraged_drawdown = base_max_drawdown * test_leverage

                # Calculate liquidation threshold for this leverage
                initial_margin_ratio = 1.0 / test_leverage
                allowable_drawdown = (initial_margin_ratio - maintenance_margin) / initial_margin_ratio

                # Check if this leverage would have caused liquidation
                # Use safety margin to be conservative (e.g., only use 80% of allowable drawdown)
                safe_allowable_drawdown = allowable_drawdown * safety_margin

                if leveraged_drawdown <= safe_allowable_drawdown and allowable_drawdown > 0:
                    max_safe_leverage = test_leverage
                else:
                    # This leverage would have caused liquidation, so previous was the max
                    break

            return round(max_safe_leverage, 1)

        # Calculate the base maximum drawdown (1x leverage)
        base_mdd = portfolio_daily_1x['dd'].max()

        # Find maximum safe leverage based on historical drawdown
        max_leverage = find_maximum_safe_leverage(base_mdd, selected_exchange)

        print(f"📊 Dynamic Max Leverage Calculation:")
        print(f"   Base MDD (1x): {base_mdd:.3f}")
        print(f"   Exchange: {selected_exchange}")
        print(f"   Calculated Max Safe Leverage: {max_leverage}x")

        # Create maximum leverage portfolio for analysis
        portfolio_daily_max = portfolio_daily.copy()
        portfolio_daily_max['pnl'] = portfolio_daily['pnl'] * max_leverage
        portfolio_daily_max['cumu'] = portfolio_daily_max['pnl'].cumsum()
        portfolio_daily_max['dd'] = portfolio_daily_max['cumu'].cummax() - portfolio_daily_max['cumu']
        if 'pos' in portfolio_daily.columns:
            portfolio_daily_max['pos'] = portfolio_daily['pos'] * max_leverage

        # Calculate metrics for maximum leverage
        daily_return_mean_max = portfolio_daily_max['pnl'].mean()
        daily_return_std_max = portfolio_daily_max['pnl'].std()

        if daily_return_std_max == 0:
            sharpe_ratio_max = 'N/A'
        else:
            sharpe_ratio_max = round((daily_return_mean_max / daily_return_std_max) * np.sqrt(365), 3)

        ar_max = round(daily_return_mean_max * 365, 3)
        mdd_max = round(portfolio_daily_max['dd'].max(), 3)
        cr_max = round(ar_max / mdd_max, 3) if mdd_max != 0 else 0
        risk_score_max = calculate_comprehensive_risk_score(max_leverage, mdd_max, ar_max, sharpe_ratio_max)
        risk_text_max = get_risk_level_text(risk_score_max)
        risk_emoji_max = get_risk_level_emoji(risk_score_max)

        # Determine optimal leverage based on risk-adjusted returns
        def get_risk_adjusted_score(sharpe_ratio, annual_return, risk_score):
            """Calculate risk-adjusted score for leverage selection"""
            if isinstance(sharpe_ratio, str) or sharpe_ratio == 'N/A':
                return 0

            # Penalize high risk scores
            risk_penalty = max(0, 1 - (risk_score / 100))

            # Combined score: Sharpe ratio * Annual return * Risk penalty
            return round(sharpe_ratio * annual_return * risk_penalty, 2)

        # Calculate risk-adjusted scores for leverage optimization
        score_1x = get_risk_adjusted_score(sharpe_ratio_1x, ar_1x, risk_score_1x)
        score_2x = get_risk_adjusted_score(sharpe_ratio_2x, ar_2x, risk_score_2x)
        score_3x = get_risk_adjusted_score(sharpe_ratio_3x, ar_3x, risk_score_3x)
        score_custom = get_risk_adjusted_score(sharpe_ratio_custom, ar_custom, risk_score_custom)
        score_max = get_risk_adjusted_score(sharpe_ratio_max, ar_max, risk_score_max)

        # Find optimal leverage including the dynamically calculated maximum
        leverage_scores = {
            1: score_1x,
            2: score_2x,
            3: score_3x,
            max_leverage: score_max,
            custom_leverage: score_custom if custom_leverage not in [1, 2, 3, max_leverage] else 0
        }

        # Remove zero scores and find max
        valid_scores = {k: v for k, v in leverage_scores.items() if v > 0}
        optimal_leverage = max(valid_scores.keys(), key=valid_scores.get) if valid_scores else 1

        # Check for strategy_performance.csv and add summary of individual strategies
        strategy_metrics_message = ""
        if os.path.exists(strategy_performance_file):
            try:
                strategy_df = pd.read_csv(strategy_performance_file)
                if not strategy_df.empty:
                    # Convert numeric columns to proper numeric types
                    numeric_columns = ['SR', 'AR', 'MDD', 'CR', 'POS', 'weight_ratio']
                    for col in numeric_columns:
                        if col in strategy_df.columns:
                            strategy_df[col] = pd.to_numeric(strategy_df[col], errors='coerce')

                    # Sort strategies by Sharpe Ratio (SR) in descending order
                    strategy_df = strategy_df.sort_values('SR', ascending=False)
                    strategy_metrics_message = "🎯All Strategies:\n"
                    strategy_metrics_message += "--------------------------------\n"
                    strategy_metrics_message += f"{'SR':<7}|{'AR':<7}|{'MDD':<7}|{'CR':<7}|{'Weight':<9}|{'Strategy':<22}\n"
                    for _, row in strategy_df.iterrows():
                        # Handle potential NaN values in display
                        sr_val = row['SR'] if not pd.isna(row['SR']) else 0.0
                        ar_val = row['AR'] if not pd.isna(row['AR']) else 0.0
                        mdd_val = row['MDD'] if not pd.isna(row['MDD']) else 0.0
                        cr_val = row['CR'] if not pd.isna(row['CR']) else 0.0

                        if 'weight_ratio' in row and not pd.isna(row['weight_ratio']):
                            weight_info = f"{row['weight_ratio'] * 100:.1f}%"
                        else:
                            weight_info = "N/A"

                        strategy_metrics_message += (f"{sr_val:7.2f}|"
                                                     f"{ar_val:7.2f}|"
                                                     f"{mdd_val:7.2f}|"
                                                     f"{cr_val:7.2f}|"
                                                     f"{weight_info:>9}|"
                                                     f"{row['strategy_name']:<22}\n")
                    strategy_metrics_message += "--------------------------------\n"
            except Exception as e:
                print(f"Error loading strategy performance data: {e}")

        # Send detailed metrics to Telegram FIRST, but collect trade activity info to include in the message
        # Get trade activity summary data
        trade_summary_data = get_trade_activity_summary()

        # Add trade activity summary to metrics message
        if trade_summary_data:
            total_trades, avg_daily_trades, top_strategies_text = trade_summary_data
            trade_metrics = f"\n\n📈 Trade Count Summary 📈\n"
            trade_metrics += f"Total Trades: {int(total_trades)}\n"
            trade_metrics += f"Avg Daily Trades: {avg_daily_trades:.1f}\n"
            trade_metrics += f"All Trading Strategies:\n--------------------------------\n{top_strategies_text}"
        else:
            trade_metrics = ""

        # Split message sending to avoid exceeding Telegram message size limits
        metrics_message = (f"📊Backtesting Dashboard (Last {num_days} Days)\n"
                           f"Weight➡️{weight_strategy}\n")  # Add weight strategy information

        # Create table header for strategy pool metrics - compact format with emoji risk
        metrics_message += "\n📊 Strategy Pool Performance:\n"
        metrics_message += "------------------------------\n"
        metrics_message += f"{'SR':<6}|{'AR':<6}|{'MDD':<6}|{'CR':<6}|{'Risk':<2}|{'Leverage':<12}\n"

        # Show only 3 essential strategies with compact format
        essential_strategies = []

        # 1. Max Safe Leverage
        essential_strategies.append(
            (max_leverage, sharpe_ratio_max, ar_max, mdd_max, cr_max, risk_emoji_max, f"{max_leverage}x Max"))

        # 2. Current Strategy
        if custom_leverage == 1:
            essential_strategies.append((1, sharpe_ratio_1x, ar_1x, mdd_1x, cr_1x, risk_emoji_1x, f"1x Curr"))
        elif custom_leverage == 2:
            essential_strategies.append((2, sharpe_ratio_2x, ar_2x, mdd_2x, cr_2x, risk_emoji_2x, f"2x Curr"))
        elif custom_leverage == 3:
            essential_strategies.append((3, sharpe_ratio_3x, ar_3x, mdd_3x, cr_3x, risk_emoji_3x, f"3x Curr"))
        else:
            essential_strategies.append(
                (custom_leverage, sharpe_ratio_custom, ar_custom, mdd_custom, cr_custom, risk_emoji_custom,
                 f"{custom_leverage}x Curr"))

        # 3. Buy & Hold
        essential_strategies.append((0, sharpe_ratio_bnh, ar_bnh, mdd_bnh, cr_bnh, risk_emoji_bnh, "BuyHold"))

        # Display only the 3 essential strategies with compact format
        for lev, sr, ar, mdd, cr, risk_emoji, label in essential_strategies:
            metrics_message += (f"{format_sr(sr):>6}|"
                                f"{ar:6.2f}|"
                                f"{mdd:6.2f}|"
                                f"{cr:6.2f}|"
                                f"{risk_emoji:<2}|"
                                f"{label:<12}\n")

        # Add optimal leverage recommendation
        metrics_message += "------------------------------\n"
        metrics_message += f"💡 Optimal: {optimal_leverage:.1f}x (Score: {valid_scores.get(optimal_leverage, 0):.2f})\n"
        metrics_message += f"Risk: 🟢Low 🟡Moderate 🟠High 🔴Very High\n"
        metrics_message += "------------------------------\n"

        # Add strategy and trade metrics
        metrics_message += f"{strategy_metrics_message}{trade_metrics}"
        send_tg(metrics_message)

        # Get trade count data
        trade_count_data = get_daily_trade_counts()

        # Create a combined figure with three subplots now
        if trade_count_data is not None:
            fig, (ax_equity, ax_position, ax_trades) = plt.subplots(3, 1, figsize=(12, 15),
                                                                    gridspec_kw={'height_ratios': [2, 1, 2]})
        else:
            fig, (ax_equity, ax_position) = plt.subplots(2, 1, figsize=(12, 10),
                                                         gridspec_kw={'height_ratios': [2, 1]})

        # Top subplot - Simplified Equity Curve showing only 3 lines
        current_leverage = leverage  # current leverage from key.yaml

        # Define colors for the 3 curves
        colors = {
            'max': 'red',  # Maximum leverage (highest risk/reward)
            'current': 'blue',  # Current leverage setting
            'bnh': 'gray'  # Buy & Hold benchmark
        }

        # Use the previously calculated maximum leverage and portfolio data

        # Plot 1: Maximum Safe Leverage Curve - Shows maximum potential without liquidation risk
        ax_equity.plot(portfolio_daily_max['cumu'].index, portfolio_daily_max['cumu'].values,
                       label=f'Max Safe Leverage ({max_leverage}x)',
                       color=colors['max'], linewidth=2, linestyle='-', marker=None)

        # Plot 2: Current Leverage Curve - Shows current strategy performance
        if custom_leverage == 1:
            current_data = portfolio_daily_1x['cumu']
            current_risk_text = risk_text_1x
        elif custom_leverage == 2:
            current_data = portfolio_daily_2x['cumu']
            current_risk_text = risk_text_2x
        elif custom_leverage == 3:
            current_data = portfolio_daily_3x['cumu']
            current_risk_text = risk_text_3x
        else:
            current_data = portfolio_daily_custom['cumu']
            current_risk_text = risk_text_custom

        ax_equity.plot(current_data.index, current_data.values,
                       label=f'Current Strategy ({custom_leverage}x)',
                       color=colors['current'], linewidth=3, linestyle='-', marker=None)

        # Plot 3: Buy and Hold Benchmark - Shows unlevered performance
        ax_equity.plot(cumulative_bnh_returns.index, cumulative_bnh_returns.values,
                       label='Buy & Hold (Unlevered)',
                       color=colors['bnh'], linewidth=2, linestyle='--', marker=None)

        # Add risk indicators only for high-risk leveraged strategies
        # Only show warning markers for Very High Risk (>80) or Extreme Risk (>100)
        def add_risk_warning(data, risk_score, leverage, label_suffix):
            if risk_score >= 80:  # High risk threshold
                last_date = data.index[-1]
                last_value = data.values[-1]

                if risk_score >= 100:
                    # Extreme risk - red X
                    ax_equity.plot(last_date, last_value, 'rX', markersize=12, markeredgewidth=3,
                                   label=f'Extreme Risk ({leverage}x)')
                else:
                    # High risk - orange triangle
                    ax_equity.plot(last_date, last_value, '^', color='orange', markersize=10,
                                   label=f'High Risk ({leverage}x)')

        # Maximum leverage metrics were already calculated earlier

        # Add risk warnings for displayed curves
        add_risk_warning(portfolio_daily_max['cumu'], risk_score_max, max_leverage, 'Max')

        # Get current risk score for warning function
        if custom_leverage == 1:
            current_risk_score = risk_score_1x
        elif custom_leverage == 2:
            current_risk_score = risk_score_2x
        elif custom_leverage == 3:
            current_risk_score = risk_score_3x
        else:
            current_risk_score = risk_score_custom

        add_risk_warning(current_data, current_risk_score, custom_leverage, 'Current')

        ax_equity.set_xlabel('')  # Remove x-label from top subplot
        ax_equity.set_ylabel('Cumulative PnL')

        # Create simplified, focused plot title
        plot_title = f'Portfolio Strategy Comparison (Last {num_days} Days) - Weight: {weight_strategy}\n'

        # Show metrics for the three displayed curves only (including CR) with 2 decimal places
        # Current Strategy
        if custom_leverage == 1:
            plot_title += f'Current ({custom_leverage:.1f}x): SR={format_sr(sharpe_ratio_1x)}, AR={ar_1x:.2f}, CR={cr_1x:.2f}, MDD={mdd_1x:.2f}, Risk={risk_text_1x}\n'
        elif custom_leverage == 2:
            plot_title += f'Current ({custom_leverage:.1f}x): SR={format_sr(sharpe_ratio_2x)}, AR={ar_2x:.2f}, CR={cr_2x:.2f}, MDD={mdd_2x:.2f}, Risk={risk_text_2x}\n'
        elif custom_leverage == 3:
            plot_title += f'Current ({custom_leverage:.1f}x): SR={format_sr(sharpe_ratio_3x)}, AR={ar_3x:.2f}, CR={cr_3x:.2f}, MDD={mdd_3x:.2f}, Risk={risk_text_3x}\n'
        else:
            plot_title += f'Current ({custom_leverage:.1f}x): SR={format_sr(sharpe_ratio_custom)}, AR={ar_custom:.2f}, CR={cr_custom:.2f}, MDD={mdd_custom:.2f}, Risk={risk_text_custom}\n'

        # Maximum Safe Leverage (dynamically calculated based on historical drawdown)
        plot_title += f'Max Safe ({max_leverage:.1f}x): SR={format_sr(sharpe_ratio_max)}, AR={ar_max:.2f}, CR={cr_max:.2f}, MDD={mdd_max:.2f}, Risk={risk_text_max}\n'

        # Buy & Hold
        plot_title += f'Buy&Hold: SR={format_sr(sharpe_ratio_bnh)}, AR={ar_bnh:.2f}, CR={cr_bnh:.2f}, MDD={mdd_bnh:.2f}, Risk={risk_text_bnh}\n'

        # Set title with smaller font size and adjust layout
        ax_equity.set_title(plot_title, fontsize=9)
        # Adjust figure size to accommodate the title
        fig.subplots_adjust(top=0.85)

        ax_equity.legend(loc='upper left')
        ax_equity.grid(True)

        # Middle subplot - Position Over Time (only if position data exists)
        if 'pos' in portfolio_daily.columns and not portfolio_daily['pos'].empty:
            # Get current live position using weighted calculation from strategy_performance.csv
            try:
                # Read strategy performance file to get current positions and weights
                strategy_performance_file = os.path.join(data_dir, 'strategy_performance.csv')
                current_live_position = 0

                if os.path.exists(strategy_performance_file):
                    strategy_df = pd.read_csv(strategy_performance_file)
                    if not strategy_df.empty and 'POS' in strategy_df.columns and 'weight_ratio' in strategy_df.columns:
                        # Calculate weighted position based on current strategy positions and weights
                        for _, row in strategy_df.iterrows():
                            pos = row['POS'] if not pd.isna(row['POS']) else 0
                            weight = row['weight_ratio'] if not pd.isna(row['weight_ratio']) else 0
                            current_live_position += pos * weight

                # Get the position data for plotting
                if custom_leverage == 1:
                    plot_data = portfolio_daily_1x['pos']
                    color = 'blue'
                elif custom_leverage == 2:
                    plot_data = portfolio_daily_2x['pos']
                    color = 'green'
                elif custom_leverage == 3:
                    plot_data = portfolio_daily_3x['pos']
                    color = 'red'
                else:
                    plot_data = portfolio_daily_custom['pos']
                    color = 'purple'

                # Plot the historical position data
                ax_position.plot(plot_data.index, plot_data.values, label=f'Historical Position ({custom_leverage}x)',
                                 color=color, linewidth=2)

                # Add current live position as a point if it's different from the last historical point
                if len(plot_data) > 0:
                    last_historical_pos = plot_data.iloc[-1]
                    last_date = plot_data.index[-1]

                    # Calculate current live position with leverage
                    current_live_pos_leveraged = current_live_position * custom_leverage

                    # If current position is different from last historical, add it as a point
                    if abs(current_live_pos_leveraged - last_historical_pos) > 0.01:  # Small tolerance for floating point
                        # Add today's date for the current position
                        today = pd.Timestamp.now().normalize()
                        ax_position.scatter([today], [current_live_pos_leveraged],
                                            color='red', s=100, marker='o',
                                            label=f'Current Live Position: {current_live_pos_leveraged:.2f}',
                                            zorder=5, edgecolors='black', linewidth=2)

            except Exception as e:
                # Fallback to original plotting
                if custom_leverage == 1:
                    ax_position.plot(portfolio_daily_1x.index, portfolio_daily_1x['pos'],
                                     label=f'Position ({custom_leverage}x)', color='blue')
                elif custom_leverage == 2:
                    ax_position.plot(portfolio_daily_2x.index, portfolio_daily_2x['pos'],
                                     label=f'Position ({custom_leverage}x)', color='green')
                elif custom_leverage == 3:
                    ax_position.plot(portfolio_daily_3x.index, portfolio_daily_3x['pos'],
                                     label=f'Position ({custom_leverage}x)', color='red')
                else:
                    ax_position.plot(portfolio_daily_custom.index, portfolio_daily_custom['pos'],
                                     label=f'Position ({custom_leverage}x)', color='purple')

        ax_position.set_xlabel('Date')
        ax_position.set_ylabel('Position')

        # Add current date and position info to title
        if 'pos' in portfolio_daily.columns and not portfolio_daily['pos'].empty:
            # Get current live position using the same logic as above
            try:
                strategy_performance_file = os.path.join(data_dir, 'strategy_performance.csv')
                current_title_position = 0

                if os.path.exists(strategy_performance_file):
                    strategy_df = pd.read_csv(strategy_performance_file)
                    if not strategy_df.empty and 'POS' in strategy_df.columns and 'weight_ratio' in strategy_df.columns:
                        # Calculate weighted position based on current strategy positions and weights
                        for _, row in strategy_df.iterrows():
                            pos = row['POS'] if not pd.isna(row['POS']) else 0
                            weight = row['weight_ratio'] if not pd.isna(row['weight_ratio']) else 0
                            current_title_position += pos * weight

                        # Apply leverage to the title position
                        current_title_position_leveraged = current_title_position * custom_leverage

                        current_date = datetime.datetime.now().strftime('%Y-%m-%d')
                        ax_position.set_title(
                            f'Position Over Time ({custom_leverage}x Leverage)\nCurrent: {current_date} = {current_title_position_leveraged:.2f}')
                    else:
                        ax_position.set_title(f'Position Over Time ({custom_leverage}x Leverage)')
                else:
                    ax_position.set_title(f'Position Over Time ({custom_leverage}x Leverage)')
            except Exception as e:
                ax_position.set_title(f'Position Over Time ({custom_leverage}x Leverage)')
        else:
            ax_position.set_title(f'Position Over Time ({custom_leverage}x Leverage)')

        ax_position.legend(loc='upper left')
        ax_position.grid(True)

        # Bottom subplot - Trade Counts (if data is available)
        if trade_count_data is not None:
            all_trades, strategies = trade_count_data
            # Plot stacked bars for each strategy
            ax_trades.set_title('Daily Trade Counts by all Strategy', fontsize=12, fontweight='bold')

            # Align trade count data with the same date range as the equity curve
            if not portfolio_daily.empty:
                # Reindex trade data to match the portfolio date range
                portfolio_dates = portfolio_daily.index
                all_trades_aligned = all_trades.reindex(portfolio_dates, fill_value=0)
            else:
                all_trades_aligned = all_trades

            # Exclude the 'Total' column from the stacked bars
            if 'Total' in all_trades_aligned.columns:
                strat_cols = all_trades_aligned.columns.drop('Total')
            else:
                strat_cols = all_trades_aligned.columns

            # Use the same date formatting approach as the equity curve
            if not all_trades_aligned.empty:
                # Create x-axis positions that match the other charts
                x_positions = range(len(all_trades_aligned))

                # Plot stacked bars manually to control x-axis positioning
                bottom = np.zeros(len(all_trades_aligned))
                colors = plt.cm.tab10(np.linspace(0, 1, len(strat_cols)))

                for i, col in enumerate(strat_cols):
                    ax_trades.bar(x_positions, all_trades_aligned[col], bottom=bottom,
                                  label=col, alpha=0.7, color=colors[i])
                    bottom += all_trades_aligned[col]

                # Plot the total trades as a histogram on a secondary axis
                if 'Total' in all_trades_aligned.columns:
                    ax_trades2 = ax_trades.twinx()
                    ax_trades2.bar(x_positions, all_trades_aligned['Total'],
                                   color='black', alpha=0.3, width=0.8,
                                   label='Total Trades')
                    ax_trades2.set_ylabel('')  # Remove the "Total Trades" label
                    ax_trades2.legend(loc='upper right')

                # Set x-axis labels to match the portfolio chart date format
                date_labels = [date.strftime('%Y-%m-%d') for date in all_trades_aligned.index]
                ax_trades.set_xticks(x_positions)
                ax_trades.set_xticklabels(date_labels)

                # Rotate x-axis labels for better readability
                plt.setp(ax_trades.get_xticklabels(), rotation=45, ha='right')

                # Limit the number of x-axis labels if there are too many dates
                if len(x_positions) > 10:
                    # Show approximately 10 dates evenly spaced
                    step = max(1, len(x_positions) // 10)
                    ax_trades.set_xticks(x_positions[::step])
                    ax_trades.set_xticklabels(date_labels[::step])

            ax_trades.set_xlabel('')  # Remove the "Date" label
            ax_trades.set_ylabel('')  # Remove the "Number of Trades" label
            ax_trades.legend().remove()  # Remove the strategy legend to avoid clutter

            # Add grid for better readability
            ax_trades.grid(True, axis='y', alpha=0.3)

        # Adjust layout and save
        plt.tight_layout()
        combined_path = os.path.join(data_dir, 'combined_portfolio_chart.png')
        plt.savefig(combined_path)

        # Send the combined chart image to Telegram
        send_photo(combined_path)

        # Add delay to avoid rate limiting
        time.sleep(5)

        # Create a separate Daily PnL chart showing only the last 30 days of data
        plt.figure(figsize=(12, 8))

        # Select the appropriate daily returns based on current leverage
        if current_leverage == 1:
            daily_returns = portfolio_daily_1x['pnl']
            color = 'blue'
        elif current_leverage == 2:
            daily_returns = portfolio_daily_2x['pnl']
            color = 'green'
        elif current_leverage == 3:
            daily_returns = portfolio_daily_3x['pnl']
            color = 'red'
        else:
            daily_returns = portfolio_daily_custom['pnl']
            color = 'green'  # Changed from 'purple' to 'green'

        # Get buy and hold daily returns for comparison
        bnh_daily = portfolio_bnh_daily['pnl']

        # Limit data to just the last PNL_DAYS_TO_FETCH days (from key.yaml)
        # Calculate the cutoff date
        days_to_show = PNL_DAYS_TO_FETCH
        if len(daily_returns) > days_to_show:
            daily_returns = daily_returns.iloc[-days_to_show:]
        if len(bnh_daily) > days_to_show:
            bnh_daily = bnh_daily.iloc[-days_to_show:]

        # Get the date range in a readable format for the title
        if not daily_returns.empty:
            start_date = daily_returns.index[0].strftime('%Y-%m-%d')
            end_date = daily_returns.index[-1].strftime('%Y-%m-%d')
            date_range = f"({start_date} to {end_date})"
        else:
            date_range = f"(Last {days_to_show} days)"

        # Plot daily PnL as bars - always using green for profit and red for loss
        bars = plt.bar(daily_returns.index, daily_returns.values,
                       width=0.8, color=['green' if x >= 0 else 'red' for x in daily_returns.values],
                       alpha=0.7, label=f'Daily PnL ({custom_leverage}x)')

        # Add a zero line for reference
        plt.axhline(y=0, color='black', linestyle='-', linewidth=0.5)

        # Calculate average daily return and highlight with a horizontal line
        avg_daily_return = daily_returns.mean()
        plt.axhline(y=avg_daily_return, color='grey', linestyle='--', linewidth=1,
                    label=f'Avg Daily: {avg_daily_return * 100:.1f}%')

        # Add labels and grid
        plt.xlabel('Date')
        plt.ylabel('Daily PnL (%)')
        plt.title(f'Backtesting Daily PnL ({custom_leverage}x Leverage) - Last {days_to_show} Days {date_range}\n'
                  f'Weight: {weight_strategy}, AR: {ar_custom if custom_leverage not in [1, 2, 3] else ar_1x if custom_leverage == 1 else ar_2x if custom_leverage == 2 else ar_3x}\n'
                  f'⚠️ Note: Backtesting uses theoretical returns, Live trading uses actual USD PnL')
        plt.legend(loc='best')
        plt.grid(True, alpha=0.3)

        # Format x-axis dates
        plt.xticks(rotation=45, ha='right')

        # If there are many dates, limit the number of ticks
        if len(daily_returns) > 10:
            # Show approximately 10 dates evenly spaced
            plt.gca().xaxis.set_major_locator(plt.MaxNLocator(10))

        # Add value labels to significant bars
        for i, bar in enumerate(bars):
            height = bar.get_height()
            # Show percentage values for all bars, not just significant ones
            va = 'bottom' if height >= 0 else 'top'
            y_offset = max(0.0001, abs(height * 0.05)) if height >= 0 else -max(0.0001, abs(height * 0.05))
            plt.text(bar.get_x() + bar.get_width() / 2, height + y_offset,
                     f'{height * 100:.1f}%', ha='center', va=va,
                     fontsize=8, rotation=90 if abs(height) < 0.0001 else 0,
                     bbox=dict(boxstyle='round,pad=0.2', fc='white', ec='none', alpha=0.7))

        # Check if there's cumulative returns information available
        if not daily_returns.empty:
            # Add annotations for stats
            stats_text = (f"Total PnL: {daily_returns.sum() * 100:.1f}%\n"
                          f"Avg Daily: {avg_daily_return * 100:.1f}%\n"
                          f"Win Rate: {(daily_returns > 0).mean():.1%}\n"
                          f"Largest Gain: {daily_returns.max() * 100:.1f}%\n"
                          f"Largest Loss: {daily_returns.min() * 100:.1f}%")

            # Position stats text in top left corner with light background
            plt.annotate(stats_text, xy=(0.02, 0.98), xycoords='axes fraction',
                         fontsize=9, va='top',
                         bbox=dict(boxstyle='round,pad=0.4', fc='white', ec='none', alpha=0.8))

        plt.tight_layout()

        # Save the Daily PnL chart
        daily_pnl_path = os.path.join(data_dir, 'daily_pnl_chart.png')
        plt.savefig(daily_pnl_path)
        plt.close()

        # Send the Daily PnL chart image to Telegram
        send_photo(daily_pnl_path)

        # Add delay to avoid rate limiting
        time.sleep(5)

        # Create a pie chart showing allocation of funds across symbols
        # Get symbols from all YAML configuration files
        config_files = glob.glob(os.path.join(script_dir, 'config/', 'config_*.yaml'))
        symbols = []
        for config_file in config_files:
            with open(config_file, 'r') as f:
                config = yaml.safe_load(f)
                # Get appropriate symbol based on selected exchange
                if selected_exchange == "BYBIT":
                    symbol = config.get('ASSET', {}).get('symbol', config.get('ASSET', {}).get('bybit_symbol'))
                elif selected_exchange == "BINANCE":
                    symbol = config.get('ASSET', {}).get('symbol', config.get('ASSET', {}).get('binance_symbol'))
                elif selected_exchange == "OKX":
                    symbol = config.get('ASSET', {}).get('symbol', config.get('ASSET', {}).get('okx_symbol'))

                if symbol:
                    symbols.append(symbol)

        # Create allocation weights based on max_positions.csv if it exists
        if symbols:
            # Check if max_positions.csv exists
            max_positions_file = os.path.join(data_dir, 'max_positions.csv')
            if os.path.exists(max_positions_file):
                try:
                    # Load fund allocation data from max_positions.csv
                    positions_df = pd.read_csv(max_positions_file)

                    # Check if the dataframe contains the necessary columns
                    if 'symbol' in positions_df.columns and 'fund_allocation' in positions_df.columns:
                        # Create a dictionary mapping symbols to their fund allocations
                        allocation_dict = dict(zip(positions_df['symbol'], positions_df['fund_allocation']))

                        # Get unique symbols and their allocations
                        unique_symbols = []
                        weights = []

                        for symbol in set(symbols):
                            if symbol in allocation_dict:
                                unique_symbols.append(symbol)
                                weights.append(allocation_dict[symbol])

                        # Normalize weights to ensure they sum to 1
                        if sum(weights) > 0:
                            weights = [w / sum(weights) for w in weights]
                        else:
                            # Fall back to equal weights if something went wrong
                            weights = [1 / len(unique_symbols)] * len(unique_symbols)

                        print(f"Using fund allocations from max_positions.csv for {len(unique_symbols)} symbols")
                    else:
                        # Fall back to equal weights if necessary columns are missing
                        unique_symbols = list(set(symbols))
                        weights = [1 / len(unique_symbols)] * len(unique_symbols)
                        print("max_positions.csv doesn't contain required columns, using equal weights")
                except Exception as e:
                    # Fall back to equal weights if there's an error reading the file
                    unique_symbols = list(set(symbols))
                    weights = [1 / len(unique_symbols)] * len(unique_symbols)
                    print(f"Error reading max_positions.csv: {e}, using equal weights instead")
            else:
                # Fall back to equal weights if max_positions.csv doesn't exist
                unique_symbols = list(set(symbols))
                weights = [1 / len(unique_symbols)] * len(unique_symbols)
                print("max_positions.csv not found, using equal weights")

            # Create a pie chart
            plt.figure(figsize=(10, 8))

            # Create pie chart with percentages and labels
            patches, texts, autotexts = plt.pie(
                weights,
                labels=unique_symbols,
                autopct='%1.1f%%',
                startangle=90,
                pctdistance=0.85,  # Move percentage labels closer to center
                wedgeprops={'edgecolor': 'w', 'linewidth': 1}
            )

            # Improve visibility of labels
            for text in texts:
                text.set_fontsize(10)
                text.set_fontweight('bold')

            for autotext in autotexts:
                autotext.set_fontsize(9)
                autotext.set_fontweight('bold')
                autotext.set_color('white')

            # Draw a circle at the center to make a donut chart and avoid label overlap
            centre_circle = plt.Circle((0, 0), 0.60, fc='white')
            plt.gca().add_artist(centre_circle)

            # Equal aspect ratio ensures that pie is drawn as a circle
            plt.axis('equal')
            plt.title(f'Asset Allocation', fontsize=14, pad=20)

            # Adjust layout and save
            plt.tight_layout()
            pie_chart_path = os.path.join(data_dir, 'allocation_pie_chart.png')
            plt.savefig(pie_chart_path)

            # Send the pie chart image to Telegram
            send_photo(pie_chart_path)
            plt.close()

            # Add delay to avoid rate limiting
            time.sleep(5)

            # Generate correlation heatmap of asset prices
            try:
                print("Generating correlation heatmap of asset prices...")
                # Get symbols from all YAML configuration files
                config_files = glob.glob(os.path.join(script_dir, 'config/', 'config_*.yaml'))
                symbols = []
                for config_file in config_files:
                    with open(config_file, 'r') as f:
                        config = yaml.safe_load(f)
                        # Get appropriate symbol based on selected exchange
                        if selected_exchange == "BYBIT":
                            symbol = config.get('ASSET', {}).get('symbol', config.get('ASSET', {}).get('bybit_symbol'))
                        elif selected_exchange == "BINANCE":
                            symbol = config.get('ASSET', {}).get('symbol',
                                                                 config.get('ASSET', {}).get('binance_symbol'))
                        elif selected_exchange == "OKX":
                            symbol = config.get('ASSET', {}).get('symbol', config.get('ASSET', {}).get('okx_symbol'))

                        if symbol:
                            symbols.append(symbol)

                if symbols:
                    # Calculate end time (current time)
                    end_time = int(time.time() * 1000)  # Current time in milliseconds

                    # Instead of a fixed 365 days, use the same time period as the portfolio equity curve
                    # Get the earliest date from the portfolio data to determine the period length
                    if not portfolio_daily.empty:
                        start_date = portfolio_daily.index.min()
                        end_date = datetime.datetime.now()

                        # Convert pandas Timestamp to datetime.date for consistent comparison
                        if hasattr(start_date, 'date'):
                            start_date = start_date.date()
                        else:
                            # If it's already a date object, use it as is
                            start_date = start_date

                        days_in_period = (end_date.date() - start_date).days

                        # Ensure we have at least 30 days of data
                        days_in_period = max(days_in_period, 30)

                        print(f"Using {days_in_period} days for price correlation analysis (matching portfolio period)")
                    else:
                        # Fallback to 90 days if no portfolio data is available
                        days_in_period = 90
                        print(f"No portfolio data found, using {days_in_period} days for price correlation analysis")

                    # Calculate start time based on the number of days in period
                    start_time = end_time - (days_in_period * 24 * 60 * 60 * 1000)

                    # Fetch historical price data for all symbols
                    price_data = {}
                    for symbol in symbols:
                        try:
                            # Fetch OHLCV data (Open, High, Low, Close, Volume)
                            # Using 1d timeframe to get daily prices
                            ohlcv = EXCHANGE.fetch_ohlcv(
                                symbol=symbol,
                                timeframe='1d',
                                since=start_time,
                                limit=days_in_period  # Maximum number of candles
                            )

                            if ohlcv:
                                # Convert to DataFrame
                                df = pd.DataFrame(ohlcv,
                                                  columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                                df.set_index('timestamp', inplace=True)
                                # Store only the close prices
                                price_data[symbol] = df['close']
                                print(f"Successfully fetched price data for {symbol}")
                        except Exception as e:
                            print(f"Error fetching price data for {symbol}: {e}")

                    if price_data:
                        # Combine all price data into a single DataFrame
                        price_df = pd.DataFrame(price_data)

                        # Calculate correlation matrix
                        corr_matrix = price_df.pct_change(fill_method=None).corr()

                        # Create heatmap
                        plt.figure(figsize=(12, 10))

                        # Remove the mask to show the full correlation matrix (both sides of comparisons)
                        # No mask means we'll show the complete matrix including both A→B and B→A correlations

                        # Generate a custom diverging colormap
                        cmap = plt.cm.RdBu_r

                        # Draw the heatmap without a mask to show the full matrix
                        sns_plot = sns.heatmap(
                            corr_matrix,
                            cmap=cmap,
                            vmax=1.0,
                            vmin=-1.0,
                            center=0,
                            square=True,
                            linewidths=.5,
                            cbar_kws={"shrink": .8, "label": "Correlation Coefficient"},
                            annot=True,  # Add correlation values inside cells
                            fmt=".1f",  # Format for correlation values
                            annot_kws={"size": 8}  # Text size for annotations
                        )

                        # Set title
                        plt.title(f'Asset Close Price Correlation ({days_in_period}-day)', fontsize=16, pad=20)
                        plt.tight_layout()

                        # Save the heatmap
                        heatmap_path = os.path.join(data_dir, 'correlation_heatmap.png')
                        plt.savefig(heatmap_path)
                        plt.close()

                        # Send the heatmap image to Telegram
                        send_photo(heatmap_path)
                        print(f"Correlation heatmap saved as {heatmap_path}")

                        # Add delay to avoid rate limiting
                        time.sleep(5)

                        try:
                            print("Generating PNL correlation heatmap between Strategies...")
                            data_dir = os.path.join(script_dir, 'data')
                            csv_files = glob.glob(os.path.join(data_dir, "*.csv"))
                            excluded_files = {"bybit_pnl.csv", "binance_pnl.csv", "max_positions.csv",
                                              "strategy_performance.csv"}
                            excluded_patterns = ["trade_log_", "position_over_time_debug"]
                            strategy_pnl_dict = {}
                            supported_timeframes = ["15m", "10m", "30m", "12h", "24h", "1d", "1m", "3m", "5m", "1h",
                                                    "2h", "4h", "6h"]
                            for file in csv_files:
                                base = os.path.basename(file)
                                if base in excluded_files:
                                    continue

                                # Skip files matching excluded patterns
                                if any(pattern in base for pattern in excluded_patterns):
                                    continue

                                found_tf = None
                                for tf in supported_timeframes:
                                    if tf in base:
                                        found_tf = tf
                                        break
                                if found_tf is None:
                                    print(f"Warning: Could not determine timeframe for {file}. Skipping.")
                                    continue
                                try:
                                    df = pd.read_csv(file)
                                except pd.errors.EmptyDataError:
                                    print(f"Warning: {file} is empty. Skipping.")
                                    continue
                                if 't' not in df.columns or 'pnl' not in df.columns:
                                    print(f"Warning: {file} missing required columns. Skipping.")
                                    continue
                                df['t'] = pd.to_datetime(df['t'])
                                df.set_index('t', inplace=True)
                                if found_tf in ["24h", "1d"]:
                                    daily_pnl = df['pnl']
                                else:
                                    daily_pnl = df['pnl'].resample('D').sum()
                                strategy_name = base.split('.')[0]
                                strategy_pnl_dict[strategy_name] = daily_pnl
                            if len(strategy_pnl_dict) > 1:
                                strategies_df = pd.concat(strategy_pnl_dict, axis=1)

                                # Clean up strategy names by removing timeframe prefixes
                                clean_column_names = {}
                                for col in strategies_df.columns:
                                    # Extract strategy name without timeframe prefix
                                    for tf in supported_timeframes:
                                        if f"{tf}_" in col:
                                            clean_name = col.split(f"{tf}_", 1)[1]
                                            clean_column_names[col] = clean_name
                                            break
                                        elif f"{tf}-" in col:
                                            clean_name = col.split(f"{tf}-", 1)[1]
                                            clean_column_names[col] = clean_name
                                            break
                                    else:
                                        # If no timeframe prefix found, keep original name
                                        clean_column_names[col] = col

                                # Rename columns with clean names
                                strategies_df = strategies_df.rename(columns=clean_column_names)

                                pnl_corr = strategies_df.corr()
                                plt.figure(figsize=(12, 10))
                                cmap = plt.cm.RdBu_r
                                sns_heatmap = sns.heatmap(
                                    pnl_corr,
                                    cmap=cmap,
                                    vmax=1.0,
                                    vmin=-1.0,
                                    center=0,
                                    square=True,
                                    linewidths=.5,
                                    cbar_kws={"shrink": .8, "label": "Correlation Coefficient"},
                                    annot=False,
                                    fmt=".1f",
                                    annot_kws={"size": 8}
                                )
                                plt.title('Daily PNL Correlation between Strategies', fontsize=16, pad=20)
                                plt.tight_layout()
                                pnl_corr_path = os.path.join(data_dir, 'pnl_correlation_heatmap.png')
                                plt.savefig(pnl_corr_path)
                                plt.close()
                                send_photo(pnl_corr_path)
                                print(f"PNL correlation heatmap saved as {pnl_corr_path}")

                                # Add delay to avoid rate limiting
                                time.sleep(5)
                            else:
                                print("Not enough strategy pnl data to compute correlation heatmap.")
                        except Exception as e:
                            print(f"Error generating pnl correlation heatmap: {e}")
                    else:
                        print("No price data available to create correlation heatmap")
                else:
                    print("No symbols found to create correlation heatmap")
            except Exception as e:
                print(f"Error generating correlation heatmap: {e}")

        plt.close(fig)
        print(f"Combined portfolio chart saved as combined_portfolio_chart.png with metrics:\n"
              f"Strategy - SR: {format_sr(sharpe_ratio_1x)}, AR: {ar_1x}, MDD: {mdd_1x}, CR: {cr_1x}\n"
              f"BnH - SR: {format_sr(sharpe_ratio_bnh)}, AR: {ar_bnh}, MDD: {mdd_bnh}, CR: {cr_bnh}")

        # Print individual strategy metrics if available
        if os.path.exists(strategy_performance_file):
            try:
                strategy_df = pd.read_csv(strategy_performance_file)
                if not strategy_df.empty:
                    # Convert numeric columns to proper numeric types for display
                    numeric_columns = ['SR', 'AR', 'MDD', 'CR', 'POS']
                    for col in numeric_columns:
                        if col in strategy_df.columns:
                            strategy_df[col] = pd.to_numeric(strategy_df[col], errors='coerce')

                    print("\nIndividual Strategy Performance:")
                    print(strategy_df[['strategy_name', 'SR', 'AR', 'MDD', 'CR']].to_string(index=False))
            except Exception as e:
                print(f"Error displaying strategy performance data: {e}")

    # Calculate Sharpe Ratio
    if len(portfolio_daily) == 0:
        return 'N/A', 'N/A'

    # Sharpe Ratio has already been calculated and included in the plot

    daily_return_mean = portfolio_daily['pnl'].mean()
    daily_return_std = portfolio_daily['pnl'].std()

    sharpe_ratio = (daily_return_mean / daily_return_std) * np.sqrt(365)

    # Return both Sharpe Ratios
    return round(sharpe_ratio, 3), round(sharpe_ratio_bnh, 3)


def get_pnl_data():
    if selected_exchange != "BYBIT" and selected_exchange != "BINANCE" and selected_exchange != "OKX":
        print(f"Getting PnL data not supported for exchange: {selected_exchange}")
        return

    try:
        # Get current account balance and position information
        account_balance = None
        current_positions = {}
        total_unrealized_pnl = 0
        positions_message = ""

        # Calculate API call delay based on exchange
        api_call_delay = 0.1  # Default delay
        if selected_exchange == "BYBIT":
            api_call_delay = 1.0 / BYBIT_RATE_LIMIT if BYBIT_RATE_LIMIT else 0.1
        elif selected_exchange == "BINANCE":
            # Binance has a default rate limit of 1200 requests per minute = 20 per second
            api_call_delay = 0.05  # 20 requests per second
        elif selected_exchange == "OKX":
            # OKX has a default rate limit of 1200 requests per minute = 20 per second
            api_call_delay = 0.05  # 20 requests per second

        try:
            # Use the get_acct_bal function to get total account balance including all collaterals
            account_balance = get_acct_bal()
            time.sleep(api_call_delay)  # Respect rate limit
            print(f"Current account balance: {account_balance} USD")

            # Dynamically retrieve symbols from all YAML configuration files
            config_files = glob.glob(os.path.join(script_dir, 'config/', 'config_*.yaml'))
            symbols = []
            for config_file in config_files:
                with open(config_file, 'r') as f:
                    config = yaml.safe_load(f)
                    if selected_exchange == "BYBIT":
                        symbol = config.get('ASSET', {}).get('symbol', config.get('ASSET', {}).get('bybit_symbol'))
                    elif selected_exchange == "BINANCE":
                        symbol = config.get('ASSET', {}).get('symbol', config.get('ASSET', {}).get('binance_symbol'))
                    elif selected_exchange == "OKX":
                        symbol = config.get('ASSET', {}).get('symbol', config.get('ASSET', {}).get('okx_symbol'))
                    if symbol:
                        symbols.append(symbol)

            # Add weight strategy, leverage, and rebalance frequency to the positions message
            positions_message = f"Weight➡️{weight_strategy}\n"  # Add weight strategy information
            positions_message += f"Leverage➡️{leverage}\n"  # Add leverage information
            positions_message += f"Rebalance Frequency➡️{rebalance_freq}\n\n"  # Add rebalance frequency information

            # Create balance message - now includes all collaterals in the account balance
            positions_message += f"🏛{selected_exchange} Balance: {account_balance:,.2f} USD\n"

            # Get all actual open positions from exchange to ensure we don't miss any
            all_open_positions = {}
            try:
                if selected_exchange == "BINANCE":
                    # Get all positions (both open and closed) and filter for open ones
                    all_positions = EXCHANGE.fetch_account_positions()
                    for pos in all_positions:
                        pos_symbol = pos['symbol']
                        pos_size = float(pos['info'].get('positionAmt', 0))
                        if abs(pos_size) > 0:  # Only positions with non-zero size
                            all_open_positions[pos_symbol] = pos
                elif selected_exchange == "BYBIT":
                    # For Bybit, we'll stick to config symbols but could extend this later
                    pass
                elif selected_exchange == "OKX":
                    # For OKX, we'll stick to config symbols but could extend this later
                    pass
            except Exception as e:
                print(f"Error fetching all positions: {e}")

            print(f"Found {len(symbols)} symbols from config files: {symbols}")
            print(f"Found {len(all_open_positions)} open positions from exchange: {list(all_open_positions.keys())}")

            # Create a set to track which symbols we've processed
            processed_symbols = set()

            # Fetch current position data for all symbols from config files
            for symbol in symbols:
                try:
                    if selected_exchange == "BYBIT":
                        formatted_symbol = symbol  # For Bybit, use the symbol as-is
                        position_info = EXCHANGE.fetch_position(symbol)['info']
                        unrealized_pnl = float(position_info['unrealisedPnl'] or 0)
                        size = float(position_info['size'] or 0)
                        side = position_info.get('side', '').upper()
                    elif selected_exchange == "BINANCE":
                        # Binance uses a different API structure for positions
                        try:
                            # Convert symbol format for matching (BTCUSDT -> BTC/USDT:USDT)
                            # This is how CCXT formats Binance futures symbols
                            if len(symbol) > 4 and symbol.endswith('USDT'):
                                base = symbol[:-4]  # Remove 'USDT'
                                formatted_symbol = f"{base}/USDT:USDT"
                            else:
                                formatted_symbol = symbol

                            # First check if we already have this position from our earlier fetch
                            if formatted_symbol in all_open_positions:
                                position = all_open_positions[formatted_symbol]
                                position_info = position['info']
                                unrealized_pnl = float(position_info.get('unrealizedProfit', 0))
                                size = float(position_info.get('positionAmt', 0))  # Already signed correctly
                                side = 'BUY' if float(size) > 0 else 'SELL' if float(size) < 0 else ''
                            else:
                                # Use fetch_account_positions which is known to work for Binance
                                all_positions = EXCHANGE.fetch_account_positions()
                                # Find the position for the current symbol using formatted symbol
                                position = next((p for p in all_positions if p['symbol'] == formatted_symbol), None)

                                if position:
                                    position_info = position['info']
                                    unrealized_pnl = float(position_info.get('unrealizedProfit', 0))
                                    size = float(position_info.get('positionAmt', 0))  # Already signed correctly
                                    side = 'BUY' if float(size) > 0 else 'SELL' if float(size) < 0 else ''
                                else:
                                    # No position for this symbol
                                    unrealized_pnl = 0
                                    size = 0
                                    side = ''
                        except Exception as binance_error:
                            print(f"Error fetching position with fetch_account_positions for {symbol}: {binance_error}")
                            # Fallback to the old method as a backup
                            try:
                                positions = EXCHANGE.fetch_positions([symbol])
                                if positions and len(positions) > 0:
                                    position_info = positions[0]['info']
                                    unrealized_pnl = float(position_info.get('unrealizedProfit', 0))
                                    size = float(position_info.get('positionAmt', 0))  # Already signed correctly
                                    side = 'BUY' if float(size) > 0 else 'SELL' if float(size) < 0 else ''
                                else:
                                    # No position for this symbol
                                    unrealized_pnl = 0
                                    size = 0
                                    side = ''
                            except Exception as fallback_error:
                                print(f"Fallback method also failed for {symbol}: {fallback_error}")
                                unrealized_pnl = 0
                                size = 0
                                side = ''
                    elif selected_exchange == "OKX":
                        formatted_symbol = symbol  # For OKX, use the symbol as-is
                        # OKX uses a different API structure for positions
                        positions = EXCHANGE.fetch_positions([symbol])
                        if positions and len(positions) > 0:
                            symbol_unrealized_pnl = 0
                            total_size = 0
                            side = ''

                            # OKX may have separate entries for long and short positions for the same symbol
                            for pos in positions:
                                unrealized_pnl = float(pos.get('unrealizedPnl', 0) or 0)
                                size = float(pos.get('contracts', 0) or 0)
                                pos_side = pos.get('side', '')

                                symbol_unrealized_pnl += unrealized_pnl
                                if pos_side == 'long':
                                    total_size += size
                                    side = 'BUY' if total_size > 0 else ''
                                elif pos_side == 'short':
                                    total_size -= size
                                    side = 'SELL' if total_size < 0 else ''

                            unrealized_pnl = symbol_unrealized_pnl
                            size = total_size
                        else:
                            # No position for this symbol
                            unrealized_pnl = 0
                            size = 0
                            side = ''

                    time.sleep(api_call_delay)  # Respect rate limit

                    # Add position info to message only if position size is not 0
                    if selected_exchange == "BINANCE":
                        # For Binance, positionAmt already contains the correct sign
                        # Use more decimal places for very small positions
                        if abs(float(size)) < 0.01:
                            Pos = round(float(size), 4)
                        else:
                            Pos = round(float(size), 2)
                    else:
                        # For other exchanges, apply the side logic
                        Pos = round(float(size), 2)
                        if side == 'SELL' or side == 'SHORT':
                            Pos = -Pos

                    # Only include symbols with non-zero positions in the message
                    if Pos != 0:
                        positions_message += f"{symbol} ➡️ UPnl: {unrealized_pnl:,.2f}, Pos: {Pos}\n"
                        processed_symbols.add(formatted_symbol)  # Mark the formatted symbol as processed

                    # Store positions with size > 0 for PNL calculations
                    if abs(float(size)) > 0:
                        current_positions[symbol] = {
                            'unrealizedPnl': unrealized_pnl,
                            'size': abs(float(size)),
                            'side': side
                        }
                        total_unrealized_pnl += unrealized_pnl
                except Exception as e:
                    print(f"Error fetching position for {symbol}: {e}")

            # Process any additional open positions that weren't covered by config files
            if selected_exchange == "BINANCE" and all_open_positions:
                for pos_symbol, position in all_open_positions.items():
                    if pos_symbol not in processed_symbols:
                        try:
                            position_info = position['info']
                            unrealized_pnl = float(position_info.get('unrealizedProfit', 0))
                            size = float(position_info.get('positionAmt', 0))  # Already signed correctly

                            # For Binance, positionAmt already contains the correct sign
                            # Use more decimal places for very small positions
                            if abs(float(size)) < 0.01:
                                Pos = round(float(size), 4)
                            else:
                                Pos = round(float(size), 2)

                            # Only include symbols with non-zero positions in the message
                            if Pos != 0:
                                positions_message += f"{pos_symbol} ➡️ UPnl: {unrealized_pnl:,.2f}, Pos: {Pos}\n"
                                print(f"Added missing position: {pos_symbol} with position {Pos}")

                            # Store positions with size > 0 for PNL calculations
                            if abs(float(size)) > 0:
                                side = 'BUY' if float(size) > 0 else 'SELL' if float(size) < 0 else ''
                                current_positions[pos_symbol] = {
                                    'unrealizedPnl': unrealized_pnl,
                                    'size': abs(float(size)),
                                    'side': side
                                }
                                total_unrealized_pnl += unrealized_pnl
                        except Exception as e:
                            print(f"Error processing additional position for {pos_symbol}: {e}")

            print(f"Current unrealized PNL across all positions: {total_unrealized_pnl:.2f} USD")

        except Exception as balance_error:
            print(f"Error fetching account balance and positions: {balance_error}")
            account_balance = None
            positions_message = "Error fetching current positions\n"

        # Calculate end time (current time)
        end_time = int(time.time() * 1000)  # Current time in milliseconds

        # For BINANCE, include all open positions in symbols list for historical data processing
        if selected_exchange == "BINANCE" and all_open_positions:
            all_symbols = set(symbols)  # Start with config symbols
            # Convert exchange position symbols back to Binance API format
            for exchange_symbol in all_open_positions.keys():
                # Convert BTC/USDT:USDT back to BTCUSDT for Binance income API
                if '/' in exchange_symbol and ':' in exchange_symbol:
                    base_quote = exchange_symbol.split(':')[0]  # Gets BTC/USDT
                    binance_symbol = base_quote.replace('/', '')  # Gets BTCUSDT
                    all_symbols.add(binance_symbol)
                else:
                    all_symbols.add(exchange_symbol)
            symbols = list(all_symbols)
            print(f"Updated symbols list for historical data processing: {symbols}")

        # Break the period into 7-day chunks to comply with API limits
        pnl_records = []
        days_to_fetch = PNL_DAYS_TO_FETCH  # Use global variable
        chunk_size = 7 * 24 * 60 * 60 * 1000  # 7 days in milliseconds

        for i in range(0, days_to_fetch, 7):
            # Calculate time range for this chunk
            chunk_end = end_time - (i * 24 * 60 * 60 * 1000)
            # Use either 7 days or remaining days for the last chunk
            days_in_chunk = min(7, days_to_fetch - i)
            chunk_start = chunk_end - (days_in_chunk * 24 * 60 * 60 * 1000)

            try:
                if selected_exchange == "BYBIT":
                    # Fetch closed PNL records for this time chunk using Bybit's API
                    params = {
                        'category': 'linear',
                        'startTime': chunk_start,
                        'endTime': chunk_end,
                        'limit': 100  # Maximum per request
                    }

                    # Paginate through all results for this time chunk
                    has_more = True
                    cursor = None

                    while has_more:
                        if cursor:
                            params['cursor'] = cursor

                        response = EXCHANGE.private_get_v5_position_closed_pnl(params)
                        time.sleep(api_call_delay)  # Respect rate limit

                        # Extract data
                        result = response.get('result', {})
                        data = result.get('list', [])

                        if data:
                            pnl_records.extend(data)
                            cursor = result.get('nextPageCursor')
                            has_more = bool(cursor)
                        else:
                            has_more = False

                elif selected_exchange == "BINANCE":
                    # For Binance, we'll fetch income history with REALIZED_PNL type
                    for symbol in symbols:
                        try:
                            # Fetch income history for each symbol using the futures API endpoint
                            params = {
                                'symbol': symbol,
                                'incomeType': 'REALIZED_PNL',
                                'startTime': chunk_start,
                                'endTime': chunk_end,
                                'limit': 1000
                            }

                            # Use the fapiPrivateGetIncome method for Binance Futures income history
                            income_history = EXCHANGE.fapiPrivateGetIncome(params)

                            time.sleep(api_call_delay)  # Respect rate limit

                            if income_history:
                                # Transform Binance format to match our processing requirements
                                for record in income_history:
                                    # Determine side from income info if available
                                    side = 'BUY'
                                    if 'positionSide' in record:
                                        side = 'BUY' if record['positionSide'] == 'LONG' else 'SELL'

                                    # Create a structure compatible with our processing logic
                                    transformed_record = {
                                        'symbol': record['symbol'],
                                        'side': side,
                                        'closedPnl': float(record['income']),
                                        'createdTime': int(record['time']),
                                    }
                                    pnl_records.append(transformed_record)
                        except Exception as symbol_error:
                            print(f"Error fetching income history for {symbol}: {symbol_error}")
                elif selected_exchange == "OKX":
                    # For OKX, we need to fetch account bills and trading history for accurate PNL calculation
                    try:
                        print("Fetching OKX account bills and trading history...")

                        # Convert milliseconds to seconds if needed for OKX API
                        start_time = int(chunk_start / 1000) if chunk_start else None  # Convert to seconds
                        end_time = int(chunk_end / 1000) if chunk_end else None  # Convert to seconds

                        # Get account bills for balance history
                        bills_params = {
                            'instType': 'SWAP',
                            'limit': 100
                        }

                        if start_time:
                            bills_params['begin'] = start_time
                        if end_time:
                            bills_params['end'] = end_time

                        print(f"Fetching OKX account bills with params: {bills_params}")

                        # Use privateGetAccountBills to get balance history and PNL information
                        try:
                            bills_response = EXCHANGE.privateGetAccountBills(bills_params)

                            if bills_response and bills_response.get('code') == '0' and 'data' in bills_response:
                                bills_data = bills_response['data']
                                print(f"Retrieved {len(bills_data)} account bill records")

                                # Store balance history by date
                                balance_history = {}

                                # Process bill records which contain realized PNL and balance info
                                for bill in bills_data:
                                    # Extract PNL data if it exists (type 2 = trade)
                                    if bill.get('type') == '2':  # trade related
                                        bill_timestamp = int(bill.get('ts', 0))
                                        symbol_id = bill.get('instId', '')
                                        realized_pnl = float(bill.get('pnl', 0) or 0)
                                        current_balance = float(bill.get('bal', 0) or 0)

                                        # Create a pnl record using the accurate balance
                                        if bill_timestamp > 0 and realized_pnl != 0:
                                            # Format the date for daily grouping
                                            bill_date = datetime.datetime.fromtimestamp(bill_timestamp / 1000).date()

                                            # Store balance by date
                                            balance_history[bill_date] = current_balance

                                            # Create transformed record with the actual balance at the time
                                            transformed_record = {
                                                'symbol': symbol_id,
                                                'side': bill.get('side', 'BUY').upper(),
                                                'closedPnl': realized_pnl,
                                                'createdTime': bill_timestamp,
                                                'balance': current_balance,  # Track actual balance at the time
                                            }

                                            pnl_records.append(transformed_record)

                                print(f"Processed {len(pnl_records)} PNL records from account bills")

                                # If we don't have enough PNL records from bills, try trade fills history as backup
                                if len(pnl_records) < 5:
                                    print("Not enough PNL data from bills, fetching trade history...")

                                    # Fetch trade fills history
                                    trades_params = {
                                        'instType': 'SWAP',
                                        'limit': 100
                                    }

                                    if start_time:
                                        trades_params['begin'] = start_time
                                    if end_time:
                                        trades_params['end'] = end_time

                                    trades_response = EXCHANGE.privateGetTradeFillsHistory(trades_params)

                                    if trades_response and trades_response.get(
                                            'code') == '0' and 'data' in trades_response:
                                        trades_data = trades_response['data']
                                        print(f"Retrieved {len(trades_data)} trade records")

                                        # Process trades to estimate PNL
                                        for trade in trades_data:
                                            timestamp = int(trade.get('ts', 0))
                                            symbol_id = trade.get('instId', '')
                                            price = float(trade.get('fillPx', 0) or 0)
                                            size = float(trade.get('fillSz', 0) or 0)
                                            side = trade.get('side', '').upper()
                                            fee = float(trade.get('fee', 0) or 0)

                                            # Use fee as proxy for PNL if no other PNL data
                                            # (this is not perfect but gives some indication)
                                            trade_date = datetime.datetime.fromtimestamp(timestamp / 1000).date()
                                            balance = balance_history.get(trade_date, account_balance)

                                            transformed_record = {
                                                'symbol': symbol_id,
                                                'side': side,
                                                'closedPnl': -fee,  # Use fee as an estimate
                                                'createdTime': timestamp,
                                                'balance': balance,
                                            }

                                            pnl_records.append(transformed_record)
                            else:
                                print(f"No valid bill data in OKX API response: {bills_response}")

                                # Last resort - fallback to position history
                                print("Falling back to position history...")
                                fallback_params = {
                                    'instType': 'SWAP',
                                    'limit': 100,
                                }

                                positions_history = EXCHANGE.privateGetAccountPositionsHistory(fallback_params)

                                if positions_history and positions_history.get(
                                        'code') == '0' and 'data' in positions_history:
                                    position_data = positions_history['data']
                                    print(f"Retrieved {len(position_data)} position history records from OKX")

                                    for record in position_data:
                                        symbol_id = record.get('instId', '')
                                        realized_pnl = float(record.get('realizedPnl', 0) or 0)
                                        direction = record.get('direction', '')
                                        side = 'BUY' if direction == 'long' else 'SELL' if direction == 'short' else ''

                                        u_time = record.get('uTime', '')
                                        timestamp = int(u_time) if u_time else int(time.time() * 1000)

                                        transformed_record = {
                                            'symbol': symbol_id,
                                            'side': side,
                                            'closedPnl': realized_pnl,
                                            'createdTime': timestamp,
                                            'balance': account_balance,  # Use current balance as fallback
                                        }

                                        pnl_records.append(transformed_record)
                        except Exception as bills_error:
                            print(f"Error fetching account bills: {bills_error}")
                            print("Trying alternative methods...")

                            # Try position history as fallback
                            try:
                                positions_history = EXCHANGE.privateGetAccountPositionsHistory(
                                    {'instType': 'SWAP', 'limit': 100})

                                if positions_history and positions_history.get(
                                        'code') == '0' and 'data' in positions_history:
                                    position_data = positions_history['data']
                                    print(f"Retrieved {len(position_data)} position history records")

                                    for record in position_data:
                                        symbol_id = record.get('instId', '')
                                        realized_pnl = float(record.get('realizedPnl', 0) or 0)
                                        direction = record.get('direction', '')
                                        side = 'BUY' if direction == 'long' else 'SELL' if direction == 'short' else ''

                                        timestamp = int(
                                            record.get('uTime', 0) or record.get('cTime', int(time.time() * 1000)))

                                        transformed_record = {
                                            'symbol': symbol_id,
                                            'side': side,
                                            'closedPnl': realized_pnl,
                                            'createdTime': timestamp,
                                            'balance': account_balance,  # Use current balance as fallback
                                        }

                                        pnl_records.append(transformed_record)
                            except Exception as pos_error:
                                print(f"Error fetching position history: {pos_error}")

                        time.sleep(api_call_delay)  # Respect rate limit
                    except Exception as okx_error:
                        print(f"Error processing OKX PNL data: {okx_error}")
                        traceback.print_exc()
            except Exception as chunk_error:
                print(f"Error fetching PNL data for chunk {i}: {chunk_error}")

        # Create an empty DataFrame if no PNL records were found
        if not pnl_records:
            print("No PNL records found. Creating dashboard with available data.")

            # Create a basic DataFrame with current date to show at least current data
            current_date = datetime.datetime.now().date()
            formatted_date = datetime.datetime.now().strftime('%b%d')

            # Create a default record for today with zero values
            default_record = {
                'date': current_date,
                'formatted_date': formatted_date,
                'closedPnl': 0.0,
                'symbol': 'DEFAULT'  # Just a placeholder
            }

            # Create DataFrame with the default record
            df = pd.DataFrame([default_record])
            df['createdTime'] = pd.to_datetime(current_date)

            # Create dummy total_daily_pnl
            total_daily_pnl = df[['date', 'formatted_date', 'closedPnl']].copy()
            total_daily_pnl['totalPnl'] = total_unrealized_pnl

            # Calculate percentage values if account balance is available
            if account_balance is not None and account_balance > 0:
                total_daily_pnl['pnl_percent'] = 0  # No realized PNL
                unrealized_percent = (total_unrealized_pnl / account_balance) * 100
                total_daily_pnl['total_pnl_percent'] = unrealized_percent
                total_daily_pnl['cum_realized_pnl'] = 0
                total_daily_pnl['cum_realized_pnl_percent'] = 0
                total_daily_pnl['cum_total_pnl'] = total_unrealized_pnl
                total_daily_pnl['cum_total_pnl_percent'] = unrealized_percent

            # Initialize variables for message generation
            total_realized_pnl = 0
            total_pnl = total_unrealized_pnl

            # Prepare symbol data from current positions
            if current_positions:
                # Create Series with symbols and their unrealized PNL
                symbol_pnl = pd.Series(
                    {symbol: pos_data['unrealizedPnl'] for symbol, pos_data in current_positions.items()})

                if account_balance is not None and account_balance > 0:
                    # Calculate percentage for each symbol
                    symbol_pnl_percent = pd.Series({symbol: (pos_data['unrealizedPnl'] / account_balance) * 100
                                                    for symbol, pos_data in current_positions.items()})

                    symbol_combined = pd.DataFrame({'pnl': symbol_pnl, 'pnl_percent': symbol_pnl_percent}).sort_values(
                        'pnl', ascending=False)
                else:
                    symbol_combined = pd.DataFrame({'pnl': symbol_pnl}).sort_values('pnl', ascending=False)
            else:
                # If no current positions, create empty DataFrame
                symbol_combined = pd.DataFrame({'pnl': []})

            # Set total_realized_pnl_percent to None if no account balance
            total_realized_pnl_percent = None
            total_pnl_percent = None
            if account_balance is not None and account_balance > 0:
                total_pnl_percent = (total_pnl / account_balance) * 100

            # Flag to indicate if we should generate charts
            has_historical_data = False
        # Process records if they exist
        else:
            df = pd.DataFrame(pnl_records)

            # Convert necessary columns to numeric
            numeric_columns = ['closedPnl', 'cumRealisedPnl', 'qty', 'avgEntryPrice', 'avgExitPrice', 'balance']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            # Convert timestamp to datetime - fix the FutureWarning by ensuring createdTime is numeric
            if 'createdTime' in df.columns:
                # First convert the 'createdTime' column to numeric explicitly to avoid the FutureWarning
                df['createdTime'] = pd.to_numeric(df['createdTime'], errors='coerce')
                # Then convert to datetime with unit='ms'
                df['createdTime'] = pd.to_datetime(df['createdTime'], unit='ms')
                df['date'] = df['createdTime'].dt.date
                # Create a formatted date column for display
                df['formatted_date'] = df['createdTime'].dt.strftime('%b%d')

            # Get historical balance data if available
            try:
                # Use the balance information stored in the records if available
                if 'balance' in df.columns and not df['balance'].isna().all():
                    print("Using actual balance values from account history")
                    # Create a daily balance series from the records
                    daily_balance = df.groupby('date')['balance'].last()
                    # Fill forward any missing days (use the last known balance)
                    date_range = pd.date_range(daily_balance.index.min(), daily_balance.index.max())
                    daily_balance = daily_balance.reindex(date_range).ffill()
                    # Map these balances back to the original dataframe
                    historical_balance = df['date'].map(lambda x: daily_balance.get(x, account_balance))
                else:
                    # Fallback to current balance if no historical data
                    print("No historical balance data available, using current balance")
                historical_balance = account_balance
            except Exception as hist_error:
                print(f"Error processing historical balance data: {hist_error}")
                historical_balance = account_balance

            # Group by date and symbol
            daily_pnl = df.groupby(['date', 'symbol'])['closedPnl'].sum().reset_index()

            # Group by date for total daily PNL
            total_daily_pnl = df.groupby('date')['closedPnl'].sum().reset_index()

            # Get formatted dates from the original dataframe and merge
            date_format_mapping = df[['date', 'formatted_date']].drop_duplicates()
            total_daily_pnl = pd.merge(total_daily_pnl, date_format_mapping, on='date', how='left')

            # Sort by date for calculating cumulative values
            total_daily_pnl = total_daily_pnl.sort_values('date')

            # Calculate total realized PNL for the period
            total_realized_pnl = total_daily_pnl['closedPnl'].sum()

            # Include unrealized PNL in the calculations
            total_pnl = total_realized_pnl + total_unrealized_pnl

            # Add unrealized PNL to the last day's data point
            if not total_daily_pnl.empty:
                # Add a new column for total PNL (realized + unrealized)
                total_daily_pnl['totalPnl'] = total_daily_pnl['closedPnl'].copy()
                # Update the last day with unrealized PNL
                if 'date' in total_daily_pnl.columns:
                    max_date = total_daily_pnl['date'].max()
                    total_daily_pnl.loc[total_daily_pnl['date'] == max_date, 'totalPnl'] += total_unrealized_pnl

            # Add percentage column if we have balance information
            if account_balance is not None and account_balance > 0:
                # Check if we have historical balance data
                if isinstance(historical_balance, pd.Series):
                    print("Using daily historical balances for percentage calculations")
                    # Create a mapping from date to balance
                    date_to_balance = historical_balance.to_dict()

                    # For each date in total_daily_pnl, get the appropriate balance
                    daily_balances = total_daily_pnl['date'].map(lambda x: date_to_balance.get(x, account_balance))

                    # Calculate percentage based on the balance for each day
                    total_daily_pnl['pnl_percent'] = (total_daily_pnl['closedPnl'] / daily_balances) * 100
                    total_daily_pnl['total_pnl_percent'] = (total_daily_pnl['totalPnl'] / daily_balances) * 100
                else:
                    # Fallback to using current account balance for all dates
                    print("Using current account balance for all PNL percentage calculations")
                total_daily_pnl['pnl_percent'] = (total_daily_pnl['closedPnl'] / account_balance) * 100
                total_daily_pnl['total_pnl_percent'] = (total_daily_pnl['totalPnl'] / account_balance) * 100

                total_realized_pnl_percent = (total_realized_pnl / account_balance) * 100
                total_pnl_percent = (total_pnl / account_balance) * 100

                # Calculate cumulative PNL and cumulative percentage
                total_daily_pnl['cum_realized_pnl'] = total_daily_pnl['closedPnl'].cumsum()

                # For cumulative percentages, we use the latest account balance
                # since this represents the current percentage impact of historical PNL
                total_daily_pnl['cum_realized_pnl_percent'] = (total_daily_pnl[
                                                                   'cum_realized_pnl'] / account_balance) * 100

                # Calculate total PNL cumulative (including unrealized)
                total_daily_pnl['cum_total_pnl'] = total_daily_pnl['totalPnl'].copy()
                for i in range(1, len(total_daily_pnl)):
                    total_daily_pnl.iloc[i, total_daily_pnl.columns.get_loc('cum_total_pnl')] += total_daily_pnl.iloc[
                        i - 1, total_daily_pnl.columns.get_loc('cum_total_pnl')]
                total_daily_pnl['cum_total_pnl_percent'] = (total_daily_pnl['cum_total_pnl'] / account_balance) * 100

                # Add percentage to symbol PNL
                symbol_pnl = df.groupby('symbol')['closedPnl'].sum()
                symbol_pnl_percent = (symbol_pnl / account_balance) * 100

                # Add current unrealized PNL to symbol data
                symbol_data = {'pnl': symbol_pnl, 'pnl_percent': symbol_pnl_percent}
                for symbol, pos_data in current_positions.items():
                    # If symbol exists in realized PNL, add unrealized to it, otherwise add new entry
                    if symbol in symbol_pnl:
                        symbol_data['pnl'][symbol] += pos_data['unrealizedPnl']
                        symbol_data['pnl_percent'][symbol] = (symbol_data['pnl'][symbol] / account_balance) * 100
                    else:
                        symbol_data['pnl'][symbol] = pos_data['unrealizedPnl']
                        symbol_data['pnl_percent'][symbol] = (pos_data['unrealizedPnl'] / account_balance) * 100

                symbol_combined = pd.DataFrame(symbol_data).sort_values('pnl', ascending=False)
            else:
                total_realized_pnl_percent = None
                total_pnl_percent = None
                total_daily_pnl['cum_realized_pnl'] = total_daily_pnl['closedPnl'].cumsum()

                # Calculate total PNL cumulative (including unrealized)
                total_daily_pnl['cum_total_pnl'] = total_daily_pnl['totalPnl'].copy()
                for i in range(1, len(total_daily_pnl)):
                    total_daily_pnl.iloc[i, total_daily_pnl.columns.get_loc('cum_total_pnl')] += total_daily_pnl.iloc[
                        i - 1, total_daily_pnl.columns.get_loc('cum_total_pnl')]

                # Add current unrealized PNL to symbol data
                symbol_pnl = df.groupby('symbol')['closedPnl'].sum()
                for symbol, pos_data in current_positions.items():
                    # If symbol exists in realized PNL, add unrealized to it, otherwise add new entry
                    if symbol in symbol_pnl:
                        symbol_pnl[symbol] += pos_data['unrealizedPnl']
                    else:
                        symbol_pnl[symbol] = pos_data['unrealizedPnl']

                symbol_combined = pd.DataFrame({'pnl': symbol_pnl}).sort_values('pnl', ascending=False)

            # Flag to indicate if we should generate charts
            has_historical_data = True

        # Create detailed message with combined account info and PNL data
        message = f"📊{selected_exchange} Live Dashboard (Last {days_to_fetch} Days)\n\n"

        # Add current account balance and positions instead of sending them separately
        message += positions_message + "\n"

        # Add total PNL (realized + unrealized) first
        message += f"💰Total PNL: {total_pnl:,.2f} USD"
        if total_pnl_percent is not None:
            message += f" ({total_pnl_percent:.2f}% of balance)"
        message += "\n"

        # Total PNL for the period with percentage if available
        message += f"Realized PNL: {total_realized_pnl:,.2f} USD"
        if total_realized_pnl_percent is not None:
            message += f" ({total_realized_pnl_percent:.2f}% of balance)"
        message += "\n"

        # Add unrealized PNL information last
        message += f"Unrealized PNL: {total_unrealized_pnl:,.2f} USD"
        if account_balance is not None and account_balance > 0:
            unrealized_percent = (total_unrealized_pnl / account_balance) * 100
            message += f" ({unrealized_percent:.2f}% of balance)"
        message += "\n\n"

        # Check if the dataframe has at least one row before calculating max()
        max_date = total_daily_pnl['date'].max() if not total_daily_pnl.empty else None

        # Show daily breakdown only if we have historical data
        if len(total_daily_pnl) > 1 or total_realized_pnl > 0:
            message += "🗓️Daily PNL Summary:\n"
            for _, row in total_daily_pnl.sort_values('date', ascending=False).iterrows():
                # Use formatted date in the message
                date_display = row['formatted_date'] if 'formatted_date' in row and not pd.isna(
                    row['formatted_date']) else row['date']
                # Show realized PNL for all days
                message += f"{date_display}: {row['closedPnl']:,.2f} USD"
                if account_balance is not None and account_balance > 0 and 'pnl_percent' in row:
                    message += f" ({row['pnl_percent']:.2f}%)"

                # For the most recent day, also show the total (with unrealized)
                if max_date is not None and row['date'] == max_date and total_unrealized_pnl != 0:
                    message += f" + {total_unrealized_pnl:,.2f} unrealized"
                    if account_balance is not None and account_balance > 0:
                        unrealized_percent = (total_unrealized_pnl / account_balance) * 100
                        message += f" ({unrealized_percent:.2f}%)"
                message += "\n"

            message += "\n"

        # Always show PNL by Asset section
        message += "💎PNL by Asset:\n"
        if not symbol_combined.empty:
            for symbol, row in symbol_combined.iterrows():
                message += f"{symbol}: {row['pnl']:,.2f} USD"
                if 'pnl_percent' in row:
                    message += f" ({row['pnl_percent']:.2f}%)"

                # Show unrealized portion if the symbol has an active position
                if symbol in current_positions and current_positions[symbol]['unrealizedPnl'] != 0:
                    unrealized_pnl = current_positions[symbol]['unrealizedPnl']
                    # If the PNL is entirely unrealized, no need to show it again
                    if unrealized_pnl != row['pnl']:
                        message += f" (incl. {unrealized_pnl:,.2f} unrealized"
                        if account_balance is not None and account_balance > 0:
                            pos_percent = (unrealized_pnl / account_balance) * 100
                            message += f" / {pos_percent:.2f}%"
                        message += ")"
                message += "\n"
        else:
            message += "No position data available.\n"

        # If we have no historical PNL data, add a note
        if not has_historical_data:
            message += "\nNote: No historical PNL data available for this exchange."

        # Add important disclaimer about backtesting vs live trading differences
        message += "\n\n⚠️ IMPORTANT DISCLAIMER:\n"
        message += "Backtesting results may differ significantly from live trading due to:\n"
        message += "• Real-world slippage and spread costs\n"
        message += "• Funding fees for leveraged positions\n"
        message += "• Market impact and execution timing\n"
        message += "• Account balance fluctuations affecting percentage calculations\n"
        message += "Live trading PnL reflects actual USD gains/losses vs account balance."

        # Send the message FIRST before generating charts
        send_tg(message)

        # Create data directory if it doesn't exist
        data_dir = os.path.join(script_dir, 'data')
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        # Store the dataframe in memory instead of saving to CSV
        # (Removing the CSV operations)
        # Keeping the print statement to indicate data is ready
        print("PNL data ready in memory")

        # Only generate and send charts if we have historical data
        if has_historical_data:
            # Create a plot of daily PNL with percentages
            plt.figure(figsize=(12, 8))

            # Create primary y-axis for absolute PNL
            ax1 = plt.gca()

            # Check if we have data to plot
            if len(total_daily_pnl) > 0:
                # Use formatted dates for x-axis
                x_values = total_daily_pnl['formatted_date'].values

                # Create two different color bars for realized and total PNL
                width = 0.35  # width of the bars
                x_positions = np.arange(len(x_values))
                bars1 = ax1.bar(x_positions - width / 2, total_daily_pnl['closedPnl'], width, color='green', alpha=0.7,
                                label='')

                # Create separate bars for the total PNL (last day only gets a different bar for total that includes unrealized)
                total_pnl_bars = total_daily_pnl['closedPnl'].copy().values
                if len(total_pnl_bars) > 0:
                    # If we have totalPnl column, use it for the last bar
                    if 'totalPnl' in total_daily_pnl.columns:
                        total_pnl_bars[-1] = total_daily_pnl['totalPnl'].iloc[-1]  # Update last value with total PNL
                    bars2 = ax1.bar(x_positions + width / 2, total_pnl_bars, width, color='blue', alpha=0.7, label='')

                ax1.set_xlabel('Date')
                ax1.set_ylabel('PNL (USD)', color='darkgreen')
                ax1.tick_params(axis='y', labelcolor='darkgreen')

                # Set x-tick positions and labels
                ax1.set_xticks(x_positions)
                ax1.set_xticklabels(x_values)

                # Rotate x-axis labels at 45 degrees and adjust their alignment
                plt.xticks(rotation=45, ha='right')

                # Add percentage labels if we have balance data
                if account_balance is not None and account_balance > 0:
                    # Create secondary y-axis for percentage
                    ax2 = ax1.twinx()
                    ax2.set_ylabel('Daily PNL (% of Balance)', color='darkgreen')
                    ax2.tick_params(axis='y', labelcolor='darkgreen')

                    # Add percentage labels to bars with improved positioning
                    for i, bar in enumerate(bars1):
                        height = bar.get_height()
                        # Skip if pnl_percent is not in the dataframe
                        if 'pnl_percent' not in total_daily_pnl.columns:
                            continue
                        percent = total_daily_pnl['pnl_percent'].iloc[i]
                        # For positive values, place text above bar; for negative values, place below bar
                        vert_align = 'bottom' if height >= 0 else 'top'
                        y_offset = 0.1 if height >= 0 else -0.1
                        ax1.text(
                            bar.get_x() + bar.get_width() / 2.,
                            height + y_offset,
                            f'{percent:.1f}%',
                            ha='center',
                            va=vert_align,
                            color='darkgreen',
                            fontsize=8,
                            rotation=90 if abs(percent) < 1 else 0,
                            bbox=dict(boxstyle='round,pad=0.3', fc='white', ec='none', alpha=0.7)
                        )

                    # Add annotation for the Total % value on the last day with improved positioning
                    if 'total_pnl_percent' in total_daily_pnl.columns and len(total_pnl_bars) > 0:
                        last_bar = bars2[-1]
                        last_total_percent = total_daily_pnl['total_pnl_percent'].iloc[-1]
                        height = last_bar.get_height()
                        # For positive values, place text above bar; for negative values, place below bar
                        vert_align = 'bottom' if height >= 0 else 'top'
                        y_offset = 0.1 if height >= 0 else -0.1
                        ax1.text(
                            last_bar.get_x() + last_bar.get_width() / 2.,
                            height + y_offset,
                            f'{last_total_percent:.1f}%',
                            ha='center',
                            va=vert_align,
                            color='blue',
                            fontsize=8,
                            rotation=90 if abs(last_total_percent) < 1 else 0,
                            bbox=dict(boxstyle='round,pad=0.3', fc='white', ec='none', alpha=0.7)
                        )

                    # Add a simplified legend for the bars
                    green_patch = plt.Rectangle((0, 0), 1, 1, color='green', alpha=0.7)
                    blue_patch = plt.Rectangle((0, 0), 1, 1, color='blue', alpha=0.7)
                    ax1.legend([green_patch, blue_patch], ['Realized', 'Total (incl. Unrealized)'],
                               loc='best', frameon=True, fancybox=True, shadow=True)
            else:
                # If no data, add a text message to the plot
                ax1.text(0.5, 0.5, 'No historical PNL data available',
                         ha='center', va='center', fontsize=14,
                         transform=ax1.transAxes)

            # Current date and time for the subtitle
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            plt.title(
                f'{selected_exchange} Live Trade Daily PNL (Last {days_to_fetch} Days) - Weight: {weight_strategy}\n'
                f'Leverage: {leverage}, Rebalance: {rebalance_freq}\n'
                f'Realized: {total_realized_pnl:.2f} USD, Unrealized: {total_unrealized_pnl:.2f} USD\n'
                f'⚠️ Note: Live trading shows actual USD PnL vs account balance\n'
                f'Generated: {current_time}')
            plt.tight_layout()  # Adjust padding to ensure labels fit

            # Use exchange name in filename for daily PNL plot
            plot_path = os.path.join(data_dir, f"{selected_exchange.lower()}_pnl_plot.png")
            plt.savefig(plot_path)
            plt.close()

            # Send the plot
            send_photo(plot_path)

            # Add delay to avoid rate limiting
            time.sleep(5)

            # Create cumulative PNL equity curve plot only if we have data
            if len(total_daily_pnl) > 0:
                plt.figure(figsize=(12, 8))

                # Sort dataframe by date to ensure chronological order
                sorted_daily_pnl = total_daily_pnl.sort_values('date')

                # Create primary y-axis for cumulative absolute PNL
                ax1 = plt.gca()
                # Use formatted dates for x-axis
                x_values = sorted_daily_pnl['formatted_date'].values
                x_positions = np.arange(len(x_values))

                # Plot realized cumulative PNL if column exists
                if 'cum_realized_pnl' in sorted_daily_pnl.columns:
                    ax1.plot(x_positions, sorted_daily_pnl['cum_realized_pnl'], 'g-', marker='o', linewidth=2, label='')

                # Plot total cumulative PNL (including unrealized)
                if 'cum_total_pnl' in sorted_daily_pnl.columns:
                    ax1.plot(x_positions, sorted_daily_pnl['cum_total_pnl'], 'b-', marker='s', linewidth=2, label='')

                ax1.set_xlabel('Date')
                ax1.set_ylabel('Cumulative PNL (USD)', color='darkgreen')
                ax1.tick_params(axis='y', labelcolor='darkgreen')
                ax1.grid(True, alpha=0.3)

                # Set x-tick positions and labels
                ax1.set_xticks(x_positions)
                ax1.set_xticklabels(x_values)

                # Rotate x-axis labels at 45 degrees and adjust their alignment
                plt.xticks(rotation=45, ha='right')

                # Add cumulative percentage curve if we have balance data
                if account_balance is not None and account_balance > 0:
                    # Create secondary y-axis for cumulative percentage
                    ax2 = ax1.twinx()

                    # Plot realized cumulative percentage if column exists
                    line1 = []
                    if 'cum_realized_pnl_percent' in sorted_daily_pnl.columns:
                        line1 = ax2.plot(x_positions, sorted_daily_pnl['cum_realized_pnl_percent'], 'g-', marker='o',
                                         linewidth=2, label='Realized %')

                    # Add a line for total PNL percentage if column exists
                    line2 = []
                    if 'cum_total_pnl_percent' in sorted_daily_pnl.columns:
                        line2 = ax2.plot(x_positions, sorted_daily_pnl['cum_total_pnl_percent'], 'b--', marker='s',
                                         linewidth=2, label='Total % (incl. Unrealized)')

                    ax2.set_ylabel('Cumulative PNL (% of Balance)', color='darkgreen')
                    ax2.tick_params(axis='y', labelcolor='darkgreen')

                    # Create a combined legend for better display if we have any lines
                    if line1 or line2:
                        lines = line1 + line2
                        labels = [l.get_label() for l in lines]
                        # Place legend in the best location that doesn't overlap with data
                        ax2.legend(lines, labels, loc='best', frameon=True, fancybox=True, shadow=True, fontsize=10)

                        # Add annotations to the cumulative percentage lines with improved positioning
                        if line1 and 'cum_realized_pnl_percent' in sorted_daily_pnl.columns:
                            for i, (x_pos, pct) in enumerate(
                                    zip(x_positions, sorted_daily_pnl['cum_realized_pnl_percent'])):
                                # Only annotate every few points to avoid crowding
                                if i % 4 == 0 or i == len(sorted_daily_pnl) - 1:  # Reduce frequency to every 4th point
                                    ax2.annotate(f'{pct:.1f}%',
                                                 xy=(x_pos, pct),
                                                 xytext=(0, 10),
                                                 textcoords='offset points',
                                                 ha='center',
                                                 fontsize=8,
                                                 color='darkgreen',
                                                 bbox=dict(boxstyle='round,pad=0.2', fc='white', ec='none', alpha=0.7))

                    # Add annotations for the Total % line with improved positioning
                    if line2 and 'cum_total_pnl_percent' in sorted_daily_pnl.columns:
                        for i, (x_pos, pct) in enumerate(zip(x_positions, sorted_daily_pnl['cum_total_pnl_percent'])):
                            # Only annotate every few points to avoid crowding
                            if i % 4 == 0 or i == len(sorted_daily_pnl) - 1:  # Reduce frequency to every 4th point
                                ax2.annotate(f'{pct:.1f}%',
                                             xy=(x_pos, pct),
                                             xytext=(0, 10),
                                             textcoords='offset points',
                                             ha='center',
                                             fontsize=8,
                                             color='blue',
                                             bbox=dict(boxstyle='round,pad=0.2', fc='white', ec='none', alpha=0.7))

                # Set title
                plt.title(
                    f'{selected_exchange} Cumulative PNL (Last {days_to_fetch} Days) - Weight: {weight_strategy}\n'
                    f'Leverage: {leverage}, Rebalance: {rebalance_freq}\n'
                    f'Total Realized: {total_realized_pnl:.2f} USD, Current Unrealized: {total_unrealized_pnl:.2f} USD\n'
                    f'Generated: {current_time}')
                plt.tight_layout()

                # Save the cumulative plot
                cum_plot_path = os.path.join(data_dir, f"{selected_exchange.lower()}_cum_pnl_plot.png")
                plt.savefig(cum_plot_path)
                plt.close()

                # Send the cumulative plot
                send_photo(cum_plot_path)

                # Add delay to avoid rate limiting
                time.sleep(5)

                try:
                    print("Generating PNL correlation heatmap between Strategies...")
                    data_dir = os.path.join(script_dir, 'data')
                    csv_files = glob.glob(os.path.join(data_dir, "*.csv"))
                    excluded_files = {"bybit_pnl.csv", "binance_pnl.csv", "max_positions.csv",
                                      "strategy_performance.csv"}
                    excluded_patterns = ["trade_log_", "position_over_time_debug"]
                    strategy_pnl_dict = {}
                    supported_timeframes = ["15m", "10m", "30m", "12h", "24h", "1d", "1m", "3m", "5m", "1h", "2h", "4h",
                                            "6h"]
                    for file in csv_files:
                        base = os.path.basename(file)
                        if base in excluded_files:
                            continue

                        # Skip files matching excluded patterns
                        if any(pattern in base for pattern in excluded_patterns):
                            continue

                        found_tf = None
                        for tf in supported_timeframes:
                            if tf in base:
                                found_tf = tf
                                break
                        if found_tf is None:
                            print(f"Warning: Could not determine timeframe for {file}. Skipping.")
                            continue
                        try:
                            df = pd.read_csv(file)
                        except pd.errors.EmptyDataError:
                            print(f"Warning: {file} is empty. Skipping.")
                            continue
                        if 't' not in df.columns or 'pnl' not in df.columns:
                            print(f"Warning: {file} missing required columns. Skipping.")
                            continue
                        df['t'] = pd.to_datetime(df['t'])
                        df.set_index('t', inplace=True)
                        if found_tf in ["24h", "1d"]:
                            daily_pnl = df['pnl']
                        else:
                            daily_pnl = df['pnl'].resample('D').sum()
                        strategy_name = base.split('.')[0]
                        strategy_pnl_dict[strategy_name] = daily_pnl
                    if len(strategy_pnl_dict) > 1:
                        strategies_df = pd.concat(strategy_pnl_dict, axis=1)

                        # Clean up strategy names by removing timeframe prefixes
                        clean_column_names = {}
                        for col in strategies_df.columns:
                            # Extract strategy name without timeframe prefix
                            for tf in supported_timeframes:
                                if f"{tf}_" in col:
                                    clean_name = col.split(f"{tf}_", 1)[1]
                                    clean_column_names[col] = clean_name
                                    break
                                elif f"{tf}-" in col:
                                    clean_name = col.split(f"{tf}-", 1)[1]
                                    clean_column_names[col] = clean_name
                                    break
                            else:
                                # If no timeframe prefix found, keep original name
                                clean_column_names[col] = col

                        # Rename columns with clean names
                        strategies_df = strategies_df.rename(columns=clean_column_names)

                        pnl_corr = strategies_df.corr()
                        plt.figure(figsize=(12, 10))
                        cmap = plt.cm.RdBu_r
                        sns_heatmap = sns.heatmap(
                            pnl_corr,
                            cmap=cmap,
                            vmax=1.0,
                            vmin=-1.0,
                            center=0,
                            square=True,
                            linewidths=.5,
                            cbar_kws={"shrink": .8, "label": "Correlation Coefficient"},
                            annot=False,
                            fmt=".1f",
                            annot_kws={"size": 8}
                        )
                        plt.title('Daily PNL Correlation between Strategies', fontsize=16, pad=20)
                        plt.tight_layout()
                        pnl_corr_path = os.path.join(data_dir, 'pnl_correlation_heatmap.png')
                        plt.savefig(pnl_corr_path)
                        plt.close()
                        send_photo(pnl_corr_path)
                        print(f"PNL correlation heatmap saved as {pnl_corr_path}")

                        # Add delay to avoid rate limiting
                        time.sleep(5)
                    else:
                        print("Not enough strategy pnl data to compute correlation heatmap.")
                except Exception as e:
                    print(f"Error generating pnl correlation heatmap: {e}")

            # Only generate asset-specific chart if we have symbol data
            if not symbol_combined.empty:
                # Create a bar chart showing PNL by asset
                plt.figure(figsize=(12, 8))
                ax = plt.gca()

                # Get symbol and PNL values
                symbols = symbol_combined.index
                pnl_values = symbol_combined['pnl'].values

                # Sort by absolute PNL value for better visualization
                sorted_idx = np.argsort(np.abs(pnl_values))[::-1]
                symbols = [symbols[i] for i in sorted_idx]
                pnl_values = [pnl_values[i] for i in sorted_idx]

                # Determine colors based on PNL value
                colors = ['green' if pnl >= 0 else 'red' for pnl in pnl_values]

                # Create bar chart
                bars = ax.bar(symbols, pnl_values, color=colors, alpha=0.7)

                ax.set_xlabel('Asset')
                ax.set_ylabel('PNL (USD)', color='darkgreen')
                ax.grid(True, axis='y', alpha=0.3)

                # Add value labels to bars with improved positioning
                for i, bar in enumerate(bars):
                    height = bar.get_height()
                    if height == 0:
                        continue
                    
                    # Calculate dynamic offset based on bar height
                    dynamic_offset = max(abs(height) * 0.05, 1)
                    
                    # For positive values, place text above bar; for negative values, place below bar
                    vert_align = 'bottom' if height >= 0 else 'top'
                    y_offset = dynamic_offset if height >= 0 else -dynamic_offset
                    
                    # Position PNL amount text slightly to the left of center to make room for percentage
                    x_position = bar.get_x() + bar.get_width() * 0.35  # 35% from left edge
                    
                    # Smart rotation logic: avoid rotation when possible
                    needs_rotation = len(str(f'{height:.1f}')) > 6 or abs(height) < 5
                    rotation_angle = 45 if needs_rotation else 0
                    
                    ax.text(
                        x_position,
                        height + y_offset,
                        f'{height:.1f}',
                        ha='center',
                        va=vert_align,
                        fontsize=8,
                        rotation=rotation_angle,
                        bbox=dict(boxstyle='round,pad=0.2', fc='white', ec='gray', alpha=0.9)
                    )

                # Add percentage labels if we have balance data
                if account_balance is not None and account_balance > 0 and 'pnl_percent' in symbol_combined.columns:
                    # Add a secondary y-axis for percentage
                    ax2 = ax.twinx()
                    ax2.set_ylabel('PNL (% of Balance)', color='darkgreen')
                    ax2.grid(False)

                    # Get percentage values in same order as bars
                    pct_values = [symbol_combined.loc[symbol, 'pnl_percent'] for symbol in symbols]

                    # Add percentage annotations to bars with side-by-side positioning
                    for i, (bar, pct) in enumerate(zip(bars, pct_values)):
                        height = bar.get_height()
                        if height == 0:
                            continue
                        
                        # Position percentage text to the right of center for side-by-side display
                        x_position = bar.get_x() + bar.get_width() * 0.65  # 65% from left edge
                        
                        # Calculate offset - use same level as PNL amount for side-by-side display
                        dynamic_offset = max(abs(height) * 0.05, 1)
                        
                        # For positive values, place text above bar; for negative values, place below bar
                        vert_align = 'bottom' if height >= 0 else 'top'
                        y_offset = dynamic_offset if height >= 0 else -dynamic_offset
                        
                        # Smart rotation for percentage: prefer horizontal, use 45° only when needed
                        pct_text = f'{pct:.1f}%'
                        needs_rotation = len(pct_text) > 5 or abs(pct) < 0.5
                        rotation_angle = -45 if needs_rotation else 0  # Negative angle for contrast with PNL amount
                        
                        ax.text(
                            x_position,
                            height + y_offset,
                            pct_text,
                            ha='center',
                            va=vert_align,
                            color='blue',
                            fontsize=8,
                            fontweight='bold',
                            rotation=rotation_angle,
                            bbox=dict(boxstyle='round,pad=0.2', fc='lightblue', ec='blue', alpha=0.9)
                        )

                # Rotate x-axis labels for better visibility with multiple assets
                ax.tick_params(axis='x', rotation=45)
                plt.setp(ax.get_xticklabels(), rotation=45, ha='right', rotation_mode='anchor')

                # Set title
                plt.title(f'{selected_exchange} PNL by Asset - Weight: {weight_strategy}\n'
                          f'Leverage: {leverage}, Rebalance: {rebalance_freq}\n'
                          f'Generated: {current_time}')
                plt.tight_layout()

                # Save and send the asset PNL chart
                asset_pnl_path = os.path.join(data_dir, f"{selected_exchange.lower()}_asset_pnl.png")
                plt.savefig(asset_pnl_path)
                plt.close()

                # Send the asset PNL chart
                send_photo(asset_pnl_path)
        else:
            print("No symbols found to create correlation heatmap")

    except Exception as e:
        error_message = f"Error retrieving {selected_exchange} PNL data: {str(e)}"
        print(error_message)

        # Send a simple message to Telegram with the error
        simple_message = f"📊{selected_exchange} Live Trade Dashboard 📊\n\n"
        if account_balance is not None:
            simple_message += f"Account Balance: {account_balance:,.2f} USD\n\n"
        simple_message += f"Error retrieving full PNL data: {str(e)}\n"
        simple_message += f"Unrealized PNL: {total_unrealized_pnl:.2f} USD\n\n"

        # Add position information if available
        if positions_message:
            simple_message += "Current Positions:\n" + positions_message

        send_tg(simple_message)


def get_daily_trade_counts():
    """Get daily trade counts data without sending messages or creating charts"""
    data_dir = os.path.join(script_dir, 'data')
    if not os.path.exists(data_dir):
        print("No data directory found. Creating one...")
        os.makedirs(data_dir)
        return None

    # Read all CSV files from the data directory
    csv_files = glob.glob(os.path.join(data_dir, "*.csv"))

    # Files to exclude from processing
    excluded_files = ["bybit_pnl.csv", "binance_pnl.csv", "max_positions.csv", "strategy_performance.csv"]
    excluded_patterns = ["trade_log_", "position_over_time_debug"]

    # Supported timeframes ordered by descending length to avoid substring conflicts
    supported_timeframes = ["15m", "10m", "30m", "12h", "24h", "1d", "1m", "3m", "5m", "1h", "2h", "4h", "6h"]

    # Dictionary to store daily trade counts for each strategy
    strategy_daily_trades = {}

    for file in csv_files:
        # Skip excluded files
        basename = os.path.basename(file)
        if basename in excluded_files:
            continue

        # Skip files matching excluded patterns
        if any(pattern in basename for pattern in excluded_patterns):
            continue

        found_tf = None
        for tf in supported_timeframes:
            if tf in basename:
                found_tf = tf
                break

        if found_tf is None:
            print(f"Warning: Could not determine timeframe for {file}. Skipping.")
            continue

        try:
            df = pd.read_csv(file)
        except pd.errors.EmptyDataError:
            print(f"Warning: {file} is empty. Skipping.")
            continue

        # Check if 'trade' column exists
        if 'trade' not in df.columns:
            print(f"Warning: {file} does not have a 'trade' column. Skipping.")
            continue

        # Convert timestamp to datetime
        if 't' in df.columns:
            df['t'] = pd.to_datetime(df['t'])

            # Extract strategy name from filename
            filename = os.path.basename(file).split('.')[0]

            # Remove timeframe prefix from strategy name
            if found_tf and found_tf in filename:
                # Handle both underscore and dash separators
                if f"{found_tf}_" in filename:
                    strategy_name = filename.split(f"{found_tf}_", 1)[1]
                elif f"{found_tf}-" in filename:
                    strategy_name = filename.split(f"{found_tf}-", 1)[1]
                else:
                    strategy_name = filename
            else:
                strategy_name = filename

            # Set timestamp as index
            df.set_index('t', inplace=True)

            # Count trades per day - fixed to properly sum all trade values
            if found_tf in ["24h", "1d"]:
                # For daily data, sum the trade values directly
                daily_trades = df['trade'].resample('D').sum()
            else:
                # For intraday data, also sum the trade values
                daily_trades = df['trade'].resample('D').sum()

            strategy_daily_trades[strategy_name] = daily_trades

    if not strategy_daily_trades:
        print("No trade data found in any strategy file.")
        return None

    # Combine all strategy data into a single DataFrame
    all_trades = pd.DataFrame(strategy_daily_trades)

    # Fill NaN values with 0 (days without trades)
    all_trades = all_trades.fillna(0)

    # Calculate total daily trades across all strategies
    all_trades['Total'] = all_trades.sum(axis=1)

    strategies = all_trades.columns.drop('Total') if 'Total' in all_trades.columns else all_trades.columns
    return all_trades, strategies


def get_trade_activity_summary():
    """Get trade activity summary data without sending messages"""
    trade_count_data = get_daily_trade_counts()
    if trade_count_data is None:
        return None

    all_trades, _ = trade_count_data

    # Calculate summary statistics
    total_trades = all_trades['Total'].sum() if 'Total' in all_trades.columns else all_trades.sum().sum()
    avg_daily_trades = all_trades['Total'].mean() if 'Total' in all_trades.columns else all_trades.mean().sum()

    # Get all strategies by trade count
    strategy_totals = all_trades.drop('Total', axis=1).sum() if 'Total' in all_trades.columns else all_trades.sum()
    all_strategies = strategy_totals.sort_values(ascending=False)

    # Format all strategies text
    top_strategies_text = ""
    for strategy, count in all_strategies.items():
        top_strategies_text += f"{strategy}➡️{int(count)} trades\n"

    return total_trades, avg_daily_trades, top_strategies_text


def find_oldest_since_date():
    """
    Find the oldest 'since' date, prioritizing the main config file before checking others.
    Returns the timestamp in seconds.
    """
    config_dir = os.path.join(script_dir, 'config')

    # Define the main config file path consistently
    btc_config_file = os.path.join(config_dir, 'config_BTC.yaml')
    main_config_basename = os.path.basename(btc_config_file)

    # First, try to get the 'since' value from the main config file
    if os.path.exists(btc_config_file):
        try:
            with open(btc_config_file) as f:
                main_config = yaml.safe_load(f)

            if 'ASSET' in main_config and 'since' in main_config['ASSET']:
                main_since = int(main_config['ASSET']['since'])
                print(f"Using 'since' value from {main_config_basename}: {datetime.datetime.fromtimestamp(main_since)}")
                return main_since
        except Exception as e:
            print(f"Error reading {main_config_basename}: {e}")

    # If main config doesn't have a 'since' value, check all other config files
    oldest_since = None
    config_files = [f for f in os.listdir(config_dir) if f.startswith('config_') and f.endswith('.yaml')]

    for config_file_name in config_files:
        # Skip main config as we already checked it
        if config_file_name == main_config_basename:
            continue

        config_path = os.path.join(config_dir, config_file_name)
        try:
            with open(config_path) as f:
                config_data = yaml.safe_load(f)

            if 'ASSET' in config_data and 'since' in config_data['ASSET']:
                since_value = int(config_data['ASSET']['since'])
                if oldest_since is None or since_value < oldest_since:
                    oldest_since = since_value
        except Exception as e:
            print(f"Error reading 'since' date from {config_file_name}: {e}")

    if oldest_since is None:
        # Use a default of 2 years as fallback if no since date is found
        default_years = 2
        end_time = int(time.time())
        oldest_since = end_time - (default_years * 365 * 24 * 60 * 60)
        print(f"No 'since' dates found in config files, using {default_years} years as fallback")
    else:
        print(f"Using oldest 'since' date from config files: {datetime.datetime.fromtimestamp(oldest_since)}")

    return oldest_since


def main():
    while True:
        try:
            now = datetime.datetime.now()

            # Existing code for periodic tasks
            if now.second == 0:
                if now.minute % portfolio_freq == 0:
                    sharpe_strategy, sharpe_bnh = calculate_portfolio_sharpe()
                    print("Portfolio Sharpe Ratio:", sharpe_strategy, "Buy and Hold Sharpe Ratio:", sharpe_bnh)
                    get_pnl_data()
        except Exception as error:
            error_message = f"Portfolio script encountered error: {error}"
            send_tg(error_message)
            print(error_message)
        time.sleep(1)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("Trading Ended!")