# Configuration file generated/updated by Strategy_maker.py

ORDER_TYPE: limit
RUN_FREQ: 5
ASSET:
  symbol: SOLUSDT
  glassnode_symbol: SOL
  since: 1589126400
STRATEGIES:
- name: Cody30
  x: 480
  y: 0.2
  api: https://api.glassnode.com/v1/metrics/distribution/balance_exchanges
  metric_key: .nan
  api_symbol: USDC
  resolution: 1h
  model: minmax
  type: long_short
  style: momentum
  symbol: SOL
- name: Cody31
  x: 960
  y: 0.2
  api: https://api.glassnode.com/v1/metrics/derivatives/dvol_ohlc
  metric_key: c
  api_symbol: ETH
  resolution: 1h
  model: minmax
  type: long_short
  style: momentum
  symbol: SOL
- name: Cody32
  x: 960
  y: 0.75
  api: https://api.glassnode.com/v1/metrics/breakdowns/marketcap_usd_by_age
  metric_key: aggregated
  api_symbol: SOL
  resolution: 1h
  model: robust_scaling
  type: long_only
  style: momentum
  symbol: SOL
- name: <PERSON><PERSON>
  x: 25
  y: 5.0
  api: https://api.glassnode.com/v1/metrics/market/price_usd_close
  metric_key: .nan
  api_symbol: SOL
  resolution: 24h
  model: rate_of_change
  type: long_only
  style: momentum
  symbol: SOL
- name: Cody34
  x: 40
  y: 0.25
  api: https://api.glassnode.com/v1/metrics/market/price_usd_close
  metric_key: .nan
  api_symbol: SOL
  resolution: 24h
  model: robust_scaling
  type: long_only
  style: momentum
  symbol: SOL
- name: Cody35
  x: 1200
  y: 0.08
  api: https://api.glassnode.com/v1/metrics/market/price_usd_close
  metric_key: .nan
  api_symbol: JTO
  resolution: 1h
  model: ma_diff
  type: long_only
  style: reversion
  symbol: SOL
