# Configuration file generated/updated by Strategy_maker.py

ORDER_TYPE: limit
RUN_FREQ: 5
ASSET:
  symbol: BTCUSDT
  glassnode_symbol: BTC
  since: 1589126400
STRATEGIES:
- name: Cody1
  x: 70
  y: 1.25
  api: https://api.glassnode.com/v1/metrics/addresses/min_1m_usd_count
  metric_key: .nan
  api_symbol: BTC
  resolution: 24h
  model: robust_scaling
  type: long_only
  style: momentum
  symbol: BTC
- name: Cody2
  x: 2880
  y: 0.02
  api: https://api.glassnode.com/v1/metrics/breakdowns/marketcap_realized_usd_by_age
  metric_key: 1y_2y
  api_symbol: BTC
  resolution: 1h
  model: ma_diff
  type: long_only
  style: reversion
  symbol: BTC
- name: Cody3
  x: 1320
  y: 16.0
  api: https://api.glassnode.com/v1/metrics/breakdowns/marketcap_usd_by_age
  metric_key: 6m_12m
  api_symbol: BTC
  resolution: 1h
  model: rate_of_change
  type: long_only
  style: momentum
  symbol: BTC
- name: Cody4
  x: 3600
  y: 0.75
  api: https://api.glassnode.com/v1/metrics/breakdowns/marketcap_usd_by_age
  metric_key: 6m_12m
  api_symbol: BTC
  resolution: 1h
  model: robust_scaling
  type: long_only
  style: momentum
  symbol: BTC
- name: Cody5
  x: 60
  y: 1.0
  api: https://api.glassnode.com/v1/metrics/breakdowns/mvrv_by_wallet_size
  metric_key: above_100k
  api_symbol: BTC
  resolution: 24h
  model: robust_scaling
  type: long_only
  style: momentum
  symbol: BTC
- name: Cody6
  x: 1680
  y: 0.5
  api: https://api.glassnode.com/v1/metrics/indicators/net_unrealized_profit_loss
  metric_key: .nan
  api_symbol: BTC
  resolution: 1h
  model: robust_scaling
  type: long_only
  style: momentum
  symbol: BTC
- name: Cody7
  x: 40
  y: 1.0
  api: https://api.glassnode.com/v1/metrics/derivatives/options_25delta_skew_3_months
  metric_key: .nan
  api_symbol: BTC
  resolution: 24h
  model: robust_scaling
  type: long_only
  style: reversion
  symbol: BTC
- name: Cody8
  x: 10
  y: 1.5
  api: https://api.glassnode.com/v1/metrics/derivatives/options_25delta_skew_3_months
  metric_key: .nan
  api_symbol: BTC
  resolution: 24h
  model: box_cox
  type: short_only
  style: reversion
  symbol: BTC
- name: Cody9
  x: 40
  y: 1.75
  api: https://api.glassnode.com/v1/metrics/market/price_usd_close
  metric_key: .nan
  api_symbol: BTC
  resolution: 24h
  model: z_score
  type: long_only
  style: momentum
  symbol: BTC
- name: Cody10
  x: 80
  y: 0.5
  api: https://api.glassnode.com/v1/metrics/market/price_usd_close
  metric_key: .nan
  api_symbol: BTC
  resolution: 24h
  model: robust_scaling
  type: long_only
  style: momentum
  symbol: BTC
- name: Cody11
  x: 70
  y: 0.06
  api: https://api.glassnode.com/v1/metrics/breakdowns/price_realized_usd_by_pnl
  metric_key: more_300_pct
  api_symbol: BTC
  resolution: 24h
  model: ma_diff
  type: long_only
  style: momentum
  symbol: BTC
- name: Cody12
  x: 150
  y: 0.75
  api: https://api.glassnode.com/v1/metrics/indicators/rhodl_ratio
  metric_key: .nan
  api_symbol: BTC
  resolution: 24h
  model: robust_scaling
  type: long_only
  style: momentum
  symbol: BTC
- name: Cody13
  x: 60
  y: 0.25
  api: https://api.glassnode.com/v1/metrics/signals/btc_sharpe_signal
  metric_key: .nan
  api_symbol: BTC
  resolution: 24h
  model: box_cox
  type: long_only
  style: momentum
  symbol: BTC
- name: Cody14
  x: 40
  y: 140.0
  api: https://api.glassnode.com/v1/metrics/indicators/sol_2y_3y
  metric_key: .nan
  api_symbol: BTC
  resolution: 24h
  model: ma_cross
  type: long_only
  style: momentum
  symbol: BTC
- name: Cody15
  x: 60
  y: 0.04
  api: https://api.glassnode.com/v1/metrics/breakdowns/sopr_by_age
  metric_key: 1y_2y
  api_symbol: BTC
  resolution: 24h
  model: ma_diff
  type: long_only
  style: momentum
  symbol: BTC
- name: Cody16
  x: 2160
  y: 0.05
  api: https://api.glassnode.com/v1/metrics/supply/hodl_waves
  metric_key: 1y_2y
  api_symbol: BTC
  resolution: 1h
  model: minmax
  type: long_short
  style: reversion
  symbol: BTC
- name: Cody17
  x: 1440
  y: 0.75
  api: https://api.glassnode.com/v1/metrics/transactions/inscriptions_count_sum
  metric_key: audio
  api_symbol: BTC
  resolution: 1h
  model: robust_scaling
  type: long_short
  style: momentum
  symbol: BTC
