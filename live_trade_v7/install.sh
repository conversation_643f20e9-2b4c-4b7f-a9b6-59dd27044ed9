#!/bin/bash

echo "Starting installation process..."

# Detect operating system
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux (Ubuntu/Debian) setup
    echo "Detected Linux system, updating packages..."
    sudo apt-get update
    
    echo "Installing the latest Python version..."
    sudo apt update && sudo apt upgrade -y
    sudo apt install screen python3 python3-pip python3-venv -y
elif [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS setup
    echo "Detected macOS system..."
    
    # Check if Python 3 is installed
    if ! command -v python3 &> /dev/null; then
        echo "Python 3 is not installed. Please install it using Homebrew with: brew install python3"
        exit 1
    fi
    
    # Check if screen is installed
    if ! command -v screen &> /dev/null; then
        echo "Installing screen using Homebrew..."
        brew install screen
    fi
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" || "$OSTYPE" == "win32" ]]; then
    # Windows setup
    echo "Detected Windows system..."
    
    # Check if Python 3 is installed
    if ! command -v python &> /dev/null; then
        echo "Python 3 is not installed. Please install it from https://www.python.org/downloads/windows/"
        echo "Make sure to check 'Add Python to PATH' during installation."
        exit 1
    fi
    
    # Windows doesn't have screen, inform user about alternatives
    echo "Note: The 'screen' utility is not available on Windows."
    echo "For similar functionality, you can use Windows Terminal or PowerShell tabs."
else
    echo "Unsupported operating system: $OSTYPE"
    exit 1
fi

# Set Python command based on OS
PYTHON_CMD="python3"
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" || "$OSTYPE" == "win32" ]]; then
    PYTHON_CMD="python"
fi

# Remove existing virtual environment if it exists
if [ -d "venv" ]; then
    echo "Removing existing virtual environment..."
    rm -rf venv
fi

# Create virtual environment
echo "Installing virtual environment..."
$PYTHON_CMD -m venv venv

# Activate the virtual environment
echo "Activating the virtual environment..."
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" || "$OSTYPE" == "win32" ]]; then
    source venv/Scripts/activate
else
    source venv/bin/activate
fi

# Update pip to latest version
echo "Updating pip..."
pip install --upgrade pip

# Install packages individually with increased timeout
echo "Installing python packages individually with increased timeout..."
if [ -f "packages.txt" ]; then
    while read package; do
        if [[ -n "$package" && "$package" != \#* ]]; then
            echo "Installing $package..."
            pip install --timeout 60 --retries 5 $package || echo "Warning: Failed to install $package, continuing..."
        fi
    done < packages.txt
else
    echo "Error: packages.txt not found!"
    exit 1
fi

# Install additional required packages
echo "Installing additional required packages..."
pip install --timeout 60 --retries 5 pyyaml openpyxl schedule

# Run Fund_Manager.py to set up all funds
echo "================================================"
echo "Running Fund_Manager.py to set up all funds..."
$PYTHON_CMD Fund_Manager.py

echo "Installation completed successfully!"
echo "================================================"
