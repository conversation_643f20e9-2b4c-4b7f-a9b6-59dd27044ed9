import os
import pandas as pd
from pathlib import Path
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class GlassnodeCache:
    """用於緩存 Glassnode 數據的工具類"""
    
    def __init__(self, base_dir="data/glassnode_cache"):
        """
        初始化緩存管理器
        
        參數:
            base_dir: 緩存文件的基本目錄
        """
        self.cache_dir = Path(base_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
    
    def get_cache_path(self, api_url, api_symbol, resolution, metric_key, is_price=False):
        """
        生成緩存文件路徑
        
        參數:
            api_url: API URL
            api_symbol: 幣種符號 (BTC, ETH 等)
            resolution: 時間解析度 (1h, 24h 等)
            metric_key: 指標鍵值
            is_price: 是否為價格數據
            
        返回:
            Path: 緩存文件路徑
        """
        # 使用當前日期作為緩存文件名的一部分，每天更新一次
        today_str = datetime.now().strftime("%Y%m%d")
        
        if is_price:
            # 對價格數據使用統一的命名方式
            filename = f"price_usd_close_{api_symbol}_{resolution}_{today_str}.csv"
        else:
            # 對其他指標使用唯一的命名方式
            url_hash = api_url.replace("https://api.glassnode.com/v1/metrics/", "").replace("/", "_")
            filename = f"{url_hash}_{api_symbol}_{resolution}"
            
            if metric_key and metric_key != ".nan":
                filename += f"_{metric_key.replace('/', '_').replace(' ', '_')}"
            
            # 處理特殊字符
            filename = filename.replace("%", "pct")
            filename += f"_{today_str}.csv"
        
        return self.cache_dir / filename
    
    def read_cache(self, api_url, api_symbol, resolution, metric_key, is_price=False, use_multiprocessing=False):
        """
        嘗試讀取緩存數據
        
        參數:
            api_url: API URL
            api_symbol: 幣種符號
            resolution: 時間解析度
            metric_key: 指標鍵值
            is_price: 是否為價格數據
            use_multiprocessing: 是否使用多進程模式
            
        返回:
            DataFrame 或 None: 成功時返回DataFrame，失敗時返回None
        """
        cache_path = self.get_cache_path(api_url, api_symbol, resolution, metric_key, is_price)
        
        if cache_path.exists():
            try:
                # 如果緩存文件存在，直接讀取
                if not use_multiprocessing:
                    logger.info(f"從本地緩存加載數據: {cache_path}")
                else:
                    print(f"從本地緩存加載數據: {cache_path}")
                
                # 使用明確的日期格式和解析器，移除index_col=0因為我哋唔再包含索引列
                df = pd.read_csv(cache_path, parse_dates=True)
                
                # 確保索引是 DatetimeIndex 類型
                if not isinstance(df.index, pd.DatetimeIndex):
                    # 將t列設為索引
                    if 't' in df.columns:
                        df['t'] = pd.to_datetime(df['t'])
                        df.set_index('t', inplace=True)
                
                return df
            except Exception as e:
                # 如果讀取緩存出錯，記錄錯誤並返回None
                error_msg = f"讀取緩存出錯: {str(e)}，將重新下載數據"
                if use_multiprocessing:
                    print(error_msg)
                else:
                    logger.warning(error_msg)
                
                # 刪除損壞的緩存文件
                try:
                    cache_path.unlink(missing_ok=True)
                except:
                    pass
                
                return None
        
        return None
    
    def write_cache(self, df, api_url, api_symbol, resolution, metric_key, is_price=False, use_multiprocessing=False):
        """
        寫入數據到緩存
        
        參數:
            df: 要緩存的DataFrame
            api_url: API URL
            api_symbol: 幣種符號
            resolution: 時間解析度
            metric_key: 指標鍵值
            is_price: 是否為價格數據
            use_multiprocessing: 是否使用多進程模式
            
        返回:
            bool: 成功時返回True，失敗時返回False
        """
        if df is None or df.empty:
            return False
            
        cache_path = self.get_cache_path(api_url, api_symbol, resolution, metric_key, is_price)
        
        try:
            # 確保索引是日期時間類型
            df_copy = df.copy()
            if not isinstance(df_copy.index, pd.DatetimeIndex):
                try:
                    df_copy.index = pd.to_datetime(df_copy.index)
                except:
                    pass
            
            # 保存到緩存文件，加入index=False參數避免寫入無用嘅索引列
            df_copy.to_csv(cache_path, index=False)
            
            if not use_multiprocessing:
                logger.info(f"數據已緩存到: {cache_path}")
            else:
                print(f"數據已緩存到: {cache_path}")
                
            return True
        except Exception as e:
            error_msg = f"保存緩存失敗: {str(e)}"
            if use_multiprocessing:
                print(error_msg)
            else:
                logger.warning(error_msg)
            
            return False
    
    def clear_old_cache(self, days_to_keep=7):
        """
        清理舊的緩存文件
        
        參數:
            days_to_keep: 保留的天數
        """
        today = datetime.now()
        files = list(self.cache_dir.glob("*.csv"))
        
        for file_path in files:
            file_stat = file_path.stat()
            file_time = datetime.fromtimestamp(file_stat.st_mtime)
            days_old = (today - file_time).days
            
            if days_old > days_to_keep:
                try:
                    file_path.unlink()
                    logger.info(f"刪除過期緩存文件: {file_path.name}")
                except Exception as e:
                    logger.warning(f"刪除緩存文件 {file_path.name} 失敗: {str(e)}") 