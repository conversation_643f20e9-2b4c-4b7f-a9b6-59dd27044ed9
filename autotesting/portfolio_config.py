import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import requests
import time
import logging
from tqdm import tqdm
import sys
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed
import statsmodels.api as sm
from scipy import stats
from pathlib import Path
import shutil
import glob
import json
import argparse  # 添加 argparse 模塊

# 導入自定義模塊
# Note: Ensure these modules exist or adjust imports as needed
try:
    import backtesting
    from glassnode_cache import GlassnodeCache  # 導入緩存類
except ImportError as e:
    print(f"Warning: Could not import custom modules: {e}. Ensure they are in the Python path.")
    # Define placeholders if necessary for the script to load
    class GlassnodeCache:
        def clear_old_cache(self, days_to_keep): pass
        def read_cache(self, *args, **kwargs): return None, None
        def write_cache(self, *args, **kwargs): pass
    class backtesting:
        API_KEY = os.environ.get("GLASSNODE_API_KEY", "YOUR_API_KEY") # Example placeholder
        time_variable = 365 # Example placeholder
        SHIFT_PERIODS = 1 # Example placeholder
        def get_shift_periods(res): return 1
        def get_time_variable(res): return 365
        def prepare_data(df_val, df_price): return pd.DataFrame() # Placeholder
        def process_glassnode_data(data, metric_key=None): return pd.DataFrame() # Placeholder


# 常量
RESULTS_DIR = 'portfolio_results'
os.makedirs(RESULTS_DIR, exist_ok=True)

# 當前報告目錄
CURRENT_REPORT_DIR = None

# 配置 logger (先不設置文件處理器，等報告目錄創建後再添加)
logger = logging.getLogger('portfolio_backtest')
# Prevent adding handlers multiple times if this module is reloaded
if not logger.handlers:
    logger.setLevel(logging.INFO)
    console = logging.StreamHandler()
    console.setFormatter(logging.Formatter('%(levelname)s - %(message)s'))
    logger.addHandler(console)

DEFAULT_TRAIN_RATIO = 0.7  # 預設訓練集比例
TRANSACTION_COST = 0.05 / 100  # 交易成本 0.05%
LONG_LEVERAGE = 1.0  # 做多槓桿倍數
SHORT_LEVERAGE = 1.0  # 做空槓桿倍數

# 配置默認值
DEFAULT_CONFIG = {
    "rolling_sharpe_filter": False,  # 是否使用 rolling sharpe 篩選策略
    "generate_individual_curves": False,  # 是否生成單一策略的 equity curve 圖
    "generate_strategy_heatmaps": False,  # 是否為每個策略生成熱力圖
    "weight_method": "sharpe",  # 權重方法: equal, sharpe, calmar, sortino
    "min_sr": 0.0,  # 最小夏普比率閾值
    "use_multiprocessing": True,  # 是否使用多進程
    "max_workers": 10,  # 多進程處理的進程數
    "long_leverage": 1.0,  # 長倉槓桿倍數
    "short_leverage": 1.0,  # 短倉槓桿倍數
    "min_start_date": "2022-01-01",  # 策略數據最小開始日期
    # 滾動夏普比率篩選閾值
    "threshold_avg_sr": 1.5,       # 閾值1：全段數據平均滾動夏普比率最低要求
    "threshold_recent_sr": 1.5,    # 閾值2：最近期間嘅滾動夏普比率最低要求
    "recent_period_months": 6,    # 閾值3：定義"近期"嘅月數
}

# 用於存儲當前報告目錄的全局變量
# CURRENT_REPORT_DIR is already defined above

# 設置日誌到報告目錄
def setup_report_logger(report_dir):
    """
    設置日誌保存到報告目錄

    參數:
        report_dir (str): 報告目錄路徑
    """
    global CURRENT_REPORT_DIR
    CURRENT_REPORT_DIR = report_dir

    # 創建日誌目錄
    report_log_dir = os.path.join(report_dir, 'logs')
    os.makedirs(report_log_dir, exist_ok=True)

    # 為當前執行創建時間戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # 創建新的日誌文件
    report_log_file = os.path.join(report_log_dir, f"portfolio_backtest_{timestamp}.log")

    # 清除任何已有的文件處理器
    for handler in logger.handlers[:]:
        if isinstance(handler, logging.FileHandler):
            # Close the handler before removing it
            handler.close()
            logger.removeHandler(handler)

    # 添加新的文件處理器
    report_handler = logging.FileHandler(report_log_file)
    report_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    logger.addHandler(report_handler)

    logger.info(f"已設置日誌保存到報告目錄: {report_log_dir}")

    return report_log_file

# 加載或創建配置文件
def load_or_create_config():
    """加載或創建配置"""
    config_path = 'backtest_config.json'
    if os.path.exists(config_path):
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            # 確保所有必要配置都存在
            for key, default_value in DEFAULT_CONFIG.items():
                if key not in config:
                    config[key] = default_value
        except Exception as e:
            logger.error(f"加載配置文件時出錯: {str(e)}，將使用默認配置")
            config = DEFAULT_CONFIG.copy()
    else:
        # 直接使用默認配置，不再生成文件
        logger.info("使用默認配置")
        config = DEFAULT_CONFIG.copy()

    return config 