# Imports from other modules and standard libraries
from portfolio_config import (
    logger, RESULTS_DIR, DEFAULT_TRAIN_RATIO, TRANSACTION_COST,
    LONG_LEVERAGE, SHORT_LEVERAGE, CURRENT_REPORT_DIR,
    GlassnodeCache, backtesting, setup_report_logger, load_or_create_config
)
# Import the class to be monkey-patched
from portfolio_backtester import PortfolioBacktest

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta # Import timedelta
import requests
import time
import logging
from tqdm import tqdm
import sys
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed
import statsmodels.api as sm
from scipy import stats
from pathlib import Path
import shutil
import glob
import json
import argparse
import traceback # Import traceback

# --- Define methods to be added to PortfolioBacktest class --- 

def generate_report(self):
    """生成完整的回測報告"""
    if not self.strategy_dfs or self.portfolio_df is None:
        logger.error("無完整數據，請先運行 run_all_strategies() 和 generate_portfolio()")
        return

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    # Ensure report directory is based on self.output_dir or CURRENT_REPORT_DIR
    # If self.output_dir is already a report dir, use it. Otherwise create a new one inside.
    if os.path.basename(self.output_dir).startswith('report_'):
        report_dir = self.output_dir
    elif CURRENT_REPORT_DIR and os.path.basename(CURRENT_REPORT_DIR).startswith('report_'):
        report_dir = CURRENT_REPORT_DIR
    else:
        # Default behavior: create a new report dir inside the main output dir
        report_dir = os.path.join(self.output_dir, f"report_{timestamp}")

    os.makedirs(report_dir, exist_ok=True)
    logger.info(f"生成回測報告到 {report_dir}")

    # 設置日誌保存到報告目錄 (this updates CURRENT_REPORT_DIR globally)
    setup_report_logger(report_dir)

    # 創建報告專用的data目錄
    report_data_dir = os.path.join(report_dir, 'data')
    os.makedirs(report_data_dir, exist_ok=True)
    logger.info(f"已創建報告專用的data目錄: {report_data_dir}")

    # 複製主目錄中的data文件到報告目錄（如果存在）
    # This assumes 'data' exists at the root level, adjust if needed
    main_data_dir = 'data'
    if os.path.exists(main_data_dir):
        try:
            for file_name in os.listdir(main_data_dir):
                src_file = os.path.join(main_data_dir, file_name)
                dst_file = os.path.join(report_data_dir, file_name)
                if os.path.isfile(src_file):
                    shutil.copy2(src_file, dst_file)
            logger.info(f"已複製主目錄data文件到報告目錄")
        except Exception as e:
            logger.error(f"複製data文件時出錯: {str(e)}")

    # 1. 計算策略指標 (alpha/beta、performance、rolling sharpe)，直接在報告目錄中生成
    metrics_dir = os.path.join(report_dir, 'metrics')

    # 直接在報告目錄計算指標，避免在 portfolio_results 目錄中重複生成
    logger.info("計算策略指標...")
    self.metrics_results = self.calculate_strategy_metrics(metrics_dir=metrics_dir)

    # 2. 生成篩選後的策略Excel文件，只在報告目錄中生成
    filtered_excel_path = self.generate_filtered_strategies_excel(target_dir=report_dir)
    if filtered_excel_path:
        logger.info(f"已生成篩選後的策略Excel文件: {filtered_excel_path}")

    # 3. 繪製單一策略 equity curves (如果開啟)
    if hasattr(self, 'generate_individual_curves') and self.generate_individual_curves:
        equity_curves_dir = os.path.join(report_dir, 'equity_curves')
        os.makedirs(equity_curves_dir, exist_ok=True)
        self.plot_equity_curves(save_dir=equity_curves_dir)
        logger.info("已生成單一策略 equity curves")
    else:
        logger.info("單一策略 equity curves 生成已關閉")

    # 4. 繪製組合 equity curve
    portfolio_path = os.path.join(report_dir, 'portfolio_equity_curve.png')
    self.plot_portfolio(save_path=portfolio_path)

    # 5. 繪製權重分佈圖
    weights_path = os.path.join(report_dir, 'portfolio_weights.png')
    self.plot_weight_distribution(save_path=weights_path)

    # 6. 繪製最近30天的組合表現
    recent_portfolio_path = os.path.join(report_dir, 'portfolio_recent_30days.png')
    self.plot_recent_portfolio(days=30, save_path=recent_portfolio_path)

    # 保存策略 DataFrame 到報告目錄的 data 文件夾
    current_results = self.results_summary if hasattr(self, 'results_summary') else self.results
    if not hasattr(self, 'portfolio_weights'):
         logger.warning("無權重數據，無法保存帶權重的策略 CSV 文件")
    else:
         for name, result in current_results.items():
            if name in self.portfolio_weights:  # 只處理有權重的策略
                # 獲取策略資訊
                if name not in self.strategy_dfs:
                    logger.warning(f"找不到策略 {name} 的 DataFrame，無法保存 CSV")
                    continue
                df = self.strategy_dfs[name].copy()
                resolution = result.get('resolution', '24h')
                weight = self.portfolio_weights.get(name, 0)

                # 確保 DataFrame 包含必要的列
                if 'chg' not in df.columns and 'percentage_change' in df.columns:
                    df['chg'] = df['percentage_change']

                # 重設索引以便 CSV 輸出包含時間列
                df_to_save = df.copy()
                if isinstance(df_to_save.index, pd.DatetimeIndex):
                    df_to_save = df_to_save.reset_index()

                # 將時間列名統一為 't'
                if 'index' in df_to_save.columns and 't' not in df_to_save.columns:
                    df_to_save = df_to_save.rename(columns={'index': 't'})
                elif df_to_save.index.name == 't':
                     df_to_save = df_to_save.reset_index()

                # 使用適當的命名約定，包含時間框架信息
                csv_file = os.path.join(report_data_dir, f"{name.replace('/', '_')}_{resolution}_w{weight:.4f}.csv")

                # 只保存必要的列，減少文件大小
                columns_to_save = ['t', 'pnl', 'pos', 'chg']
                available_columns = [col for col in columns_to_save if col in df_to_save.columns]
                if not available_columns:
                     logger.warning(f"策略 {name} 沒有可保存的列，跳過")
                     continue
                df_final_save = df_to_save[available_columns]

                # 保存 CSV 文件
                df_final_save.to_csv(csv_file, index=False)
                logger.info(f"保存策略 {name} ({resolution}) 數據到 {csv_file}")

    # 7. 生成策略性能指標表
    strategies_metrics = []
    for name, result in current_results.items():
        strategies_metrics.append({
            'Strategy': name,
            'Sharpe Ratio': result.get('sr', np.nan),
            'Annual Return': result.get('ar', np.nan),
            'Max Drawdown': result.get('mdd', np.nan),
            'Calmar Ratio': result.get('cr', np.nan),
            'Trades': result.get('trades', np.nan),
            'Trades/Year': result.get('trades_per_year', np.nan)
        })

    strategies_df = pd.DataFrame(strategies_metrics)
    strategies_df = strategies_df.sort_values('Sharpe Ratio', ascending=False)

    # 添加組合行
    if hasattr(self, 'portfolio_metrics') and self.portfolio_metrics:
        portfolio_row = pd.DataFrame([{
            'Strategy': 'PORTFOLIO',
            'Sharpe Ratio': self.portfolio_metrics.get('sr', np.nan),
            'Annual Return': self.portfolio_metrics.get('ar', np.nan),
            'Max Drawdown': self.portfolio_metrics.get('mdd', np.nan),
            'Calmar Ratio': self.portfolio_metrics.get('cr', np.nan),
            'Trades': np.nan,
            'Trades/Year': np.nan
        }])
        strategies_df = pd.concat([portfolio_row, strategies_df], ignore_index=True)
    else:
        logger.warning("無組合指標數據，無法添加到指標表")

    # 保存指標表
    metrics_path = os.path.join(report_dir, 'strategies_metrics.csv')
    strategies_df.to_csv(metrics_path, index=False)

    # 8. 生成描述性HTML報告
    html_path = os.path.join(report_dir, 'report.html')

    weighting_method = self.portfolio_metrics.get('weighting_method', 'N/A').capitalize() if hasattr(self, 'portfolio_metrics') else "N/A"
    
    # 獲取資產信息 - 支持多資產
    if hasattr(self, 'assets') and self.assets:
        assets_list = self.assets
        asset_symbol_display = ", ".join(assets_list)
    else:
        # 舊版相容 - 僅使用單一資產
        asset_symbol = self.asset_info.get('glassnode_symbol', 'Unknown') if hasattr(self, 'asset_info') else "Unknown"
        assets_list = [asset_symbol]
        asset_symbol_display = asset_symbol

    # 處理日期格式化
    start_date = "N/A"
    end_date = "N/A"
    if self.portfolio_df is not None and not self.portfolio_df.empty:
        try:
            if isinstance(self.portfolio_df.index[0], (pd.Timestamp, datetime)):
                start_date = self.portfolio_df.index[0].strftime('%Y-%m-%d')
                end_date = self.portfolio_df.index[-1].strftime('%Y-%m-%d')
            else:
                start_date = str(self.portfolio_df.index[0])
                end_date = str(self.portfolio_df.index[-1])
        except Exception as e:
            logger.warning(f"格式化日期時出錯: {str(e)}")
            start_date = "起始日期"
            end_date = "結束日期"

    # 分析每個資產的策略分佈
    asset_strategy_counts = {}
    for strategy in self.strategies:
        asset = strategy.get('symbol', 'Unknown').upper()
        if asset not in asset_strategy_counts:
            asset_strategy_counts[asset] = 0
        asset_strategy_counts[asset] += 1

    html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Portfolio Backtest Report - {asset_symbol_display}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        h1, h2, h3 {{ color: #333; }}
        table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        tr:nth-child(even) {{ background-color: #f9f9f9; }}
        .summary {{ background-color: #e6f7ff; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
        .image {{ text-align: center; margin: 20px 0; }}
        .image img {{ max-width: 90%; height: auto; border: 1px solid #ccc; }}
    </style>
</head>
<body>
    <h1>Portfolio Backtest Report - {asset_symbol_display}</h1>
    <div class="summary">
        <h2>Summary</h2>
        <p>
            <strong>Assets:</strong> {asset_symbol_display}<br>
            <strong>Number of Strategies:</strong> {len(self.strategies)}<br>
            <strong>Weighting Method:</strong> {weighting_method}<br>
            <strong>Date Range:</strong> {start_date} to {end_date}<br>
            <strong>Report Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        </p>
    </div>

    <h2>Assets Distribution</h2>
    <table>
        <tr><th>Asset</th><th>Number of Strategies</th></tr>
"""

    # 添加每個資產的策略數量
    for asset, count in asset_strategy_counts.items():
        html_content += f"""        <tr><td>{asset}</td><td>{count}</td></tr>
"""

    html_content += """    </table>

    <h2>Portfolio Performance</h2>
    """
    if hasattr(self, 'portfolio_metrics') and self.portfolio_metrics:
        html_content += f"""    <table>
        <tr><th>Metric</th><th>Value</th></tr>
        <tr><td>Sharpe Ratio</td><td>{self.portfolio_metrics.get('sr', np.nan):.4f}</td></tr>
        <tr><td>Annual Return</td><td>{self.portfolio_metrics.get('ar', np.nan):.4f}</td></tr>
        <tr><td>Max Drawdown</td><td>{self.portfolio_metrics.get('mdd', np.nan):.4f}</td></tr>
        <tr><td>Calmar Ratio</td><td>{self.portfolio_metrics.get('cr', np.nan):.4f}</td></tr>
    </table>
"""
    else:
        html_content += "<p>Portfolio metrics not available.</p>"

    html_content += """    <div class="image">
        <h2>Portfolio Equity Curve</h2>
        <img src="portfolio_equity_curve.png" alt="Portfolio Equity Curve">
    </div>

    <div class="image">
        <h2>Recent 30 Days Performance</h2>
        <img src="portfolio_recent_30days.png" alt="Recent 30 Days Performance">
    </div>

    <div class="image">
        <h2>Portfolio Weight Distribution</h2>
        <img src="portfolio_weights.png" alt="Portfolio Weights">
    </div>

    <h2>Strategy Performance Metrics</h2>
    <table>
        <tr>
            <th>Strategy</th>
            <th>Asset</th>
            <th>Weight</th>
            <th>Sharpe Ratio</th>
            <th>Annual Return</th>
            <th>Max Drawdown</th>
            <th>Calmar Ratio</th>
            <th>Trades/Year</th>
        </tr>
"""

    # 添加組合行
    if hasattr(self, 'portfolio_metrics') and self.portfolio_metrics:
        html_content += f"""        <tr>
            <td><strong>PORTFOLIO</strong></td>
            <td><strong>{asset_symbol_display}</strong></td>
            <td><strong>1.000</strong></td>
            <td><strong>{self.portfolio_metrics.get('sr', np.nan):.4f}</strong></td>
            <td><strong>{self.portfolio_metrics.get('ar', np.nan):.4f}</strong></td>
            <td><strong>{self.portfolio_metrics.get('mdd', np.nan):.4f}</strong></td>
            <td><strong>{self.portfolio_metrics.get('cr', np.nan):.4f}</strong></td>
            <td>-</td>
        </tr>
"""

    # 添加每個策略的行，並包含資產信息
    current_results = self.results_summary if hasattr(self, 'results_summary') else self.results
    if hasattr(self, 'portfolio_weights'):
         for name in self.portfolio_weights:
            if name in current_results:
                result = current_results[name]
                weight = self.portfolio_weights.get(name, 0)
                
                # 查找策略對應的資產
                asset = "Unknown"
                for strategy in self.strategies:
                    if strategy.get('name') == name:
                        asset = strategy.get('symbol', 'Unknown').upper()
                        break
                
                html_content += f"""        <tr>
            <td>{name}</td>
            <td>{asset}</td>
            <td>{weight:.4f}</td>
            <td>{result.get('sr', np.nan):.4f}</td>
            <td>{result.get('ar', np.nan):.4f}</td>
            <td>{result.get('mdd', np.nan):.4f}</td>
            <td>{result.get('cr', np.nan):.4f}</td>
            <td>{result.get('trades_per_year', np.nan):.1f}</td>
        </tr>
"""

    # 根據是否生成單一策略圖表顯示不同內容
    if self.generate_individual_curves:
        html_content += """    </table>

    <h2>Individual Strategy Equity Curves</h2>
    <p>Individual equity curves for each strategy can be found in the 'equity_curves' directory.</p>
"""
    else:
        html_content += """    </table>

    <h2>Individual Strategy Equity Curves</h2>
    <p>Individual equity curves generation is disabled.</p>
"""

    html_content += """    
    <h2>Notes</h2>
    <ul>
        <li>Transaction cost: {TRANSACTION_COST*100:.2f}% per trade</li>
        <li>Long Leverage: {LONG_LEVERAGE:.2f}x, Short Leverage: {SHORT_LEVERAGE:.2f}x</li>
        <li>Metrics calculations assume daily data where applicable.</li>
    </ul>
</body>
</html>
"""

    with open(html_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

    logger.info(f"已生成HTML報告: {html_path}")
    logger.info(f"報告目錄: {report_dir}")

    # Update global report directory variable (already done by setup_report_logger)
    # global CURRENT_REPORT_DIR
    # CURRENT_REPORT_DIR = report_dir

    return report_dir

def calculate_strategy_metrics(self, metrics_dir=None):
    """
    計算策略指標: alpha/beta、表現指標、滾動夏普比率等

    參數:
        metrics_dir (str): 指標結果輸出目錄，如果為 None，使用 self.output_dir/metrics

    返回:
        dict: 包含不同類型指標的字典
    """
    if not self.strategy_dfs:
        logger.error("無可用策略數據，請先運行 run_all_strategies()")
        return None

    if metrics_dir is None:
        # Use CURRENT_REPORT_DIR if available
        base_dir = CURRENT_REPORT_DIR if CURRENT_REPORT_DIR else self.output_dir
        metrics_dir = os.path.join(base_dir, 'metrics')
        # Clear existing metrics dir only if it's not inside a specific report dir already
        # This logic might need refinement based on intended use
        # if os.path.exists(metrics_dir) and not base_dir.startswith('report_'):
        #     logger.info(f"清空現有的 metrics 目錄: {metrics_dir}")
        #     shutil.rmtree(metrics_dir)

    os.makedirs(metrics_dir, exist_ok=True)
    logger.info(f"計算策略指標，結果將保存到 {metrics_dir}")

    # 存儲各類型結果
    results = {
        'alpha_metrics': [],
        'performance_metrics': [],
        'rolling_sharpe': {},
        'filtered_strategies': {} # Initialize filtered_strategies
    }
    current_results = self.results_summary if hasattr(self, 'results_summary') else self.results

    # 1. 計算 alpha、beta、R方值指標
    logger.info("計算 alpha/beta 指標...")
    for name, df in self.strategy_dfs.items():
        try:
            if 'pnl' not in df.columns or 'percentage_change' not in df.columns:
                logger.warning(f"策略 {name} 缺少必要的列 (pnl或percentage_change)，跳過alpha指標計算")
                continue

            df_temp = df.copy()
            df_temp['chg'] = df_temp['percentage_change']
            resolution = current_results.get(name, {}).get('resolution', '24h')

            alpha_ann, beta, r_sq, alpha_p, beta_p = self.calculate_alpha_beta(df_temp)

            # Store only if calculation was successful (beta is not the default 0.0)
            # The check `beta != 0.0` might be too strict if beta can genuinely be 0. Better check p-values or R-squared.
            # Using r_squared > -1 as a basic check that calculation happened.
            if r_sq > -1:
                 results['alpha_metrics'].append({
                    'strategy': name,
                    'alpha': alpha_ann,
                    'beta': beta,
                    'r_squared': r_sq,
                    'alpha_pvalue': alpha_p,
                    'beta_pvalue': beta_p
                })
                 logger.info(f"策略 {name} alpha指標計算完成: Alpha={alpha_ann:.5f} (p={alpha_p:.5f}), Beta={beta:.5f} (p={beta_p:.5f}), R²={r_sq:.5f}")
            else:
                 logger.warning(f"策略 {name} alpha指標計算未能成功")

        except Exception as e:
            logger.error(f"計算策略 {name} 的 alpha/beta 指標時出錯: {str(e)}")
            logger.error(traceback.format_exc())

    if results['alpha_metrics']:
        alpha_df = pd.DataFrame(results['alpha_metrics'])
        alpha_path = os.path.join(metrics_dir, 'strategy_alpha_metrics.csv')
        alpha_df.to_csv(alpha_path, index=False)
        logger.info(f"已保存 alpha 指標到 {alpha_path}")

    # 2. 計算表現指標 (整體期間和最近365天)
    logger.info("計算表現指標...")
    for name, df_orig in self.strategy_dfs.items():
        df = df_orig.copy() # Work on a copy
        try:
            if 'pnl' not in df.columns:
                logger.warning(f"策略 {name} 缺少必要的列 (pnl)，跳過表現指標計算")
                continue

            resolution = current_results.get(name, {}).get('resolution', '24h')
            df_clean = df.dropna(subset=['pnl'])
            if len(df_clean) < 2:
                logger.warning(f"策略 {name} 清理后數據不足 ({len(df_clean)} 行)，跳過表現指標計算")
                continue

            time_variable = backtesting.get_time_variable(resolution)
            returns = df_clean['pnl'].values
            num_trades = df_clean[df_clean['pnl'] != 0] # Count non-zero pnl as trades?
            num_trades_count = len(num_trades)

            annual_return = np.mean(returns) * time_variable
            sharpe_ratio = annual_return / (np.std(returns) * np.sqrt(time_variable)) if np.std(returns) != 0 else np.nan

            cumulative_returns = np.cumsum(returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = running_max - cumulative_returns
            max_drawdown = np.max(drawdowns) if len(drawdowns) > 0 else 0
            calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else np.nan
            trades_per_year = num_trades_count * time_variable / len(df_clean) if len(df_clean)>0 else 0

            pos_0_pct, pos_1_pct, pos_neg1_pct = np.nan, np.nan, np.nan
            if 'pos' in df_clean.columns:
                total_periods = len(df_clean)
                pos_0_pct = (df_clean['pos'] == 0).sum() / total_periods * 100
                pos_1_pct = (df_clean['pos'] == 1).sum() / total_periods * 100 # Assuming pos is 1 for long
                pos_neg1_pct = (df_clean['pos'] == -1).sum() / total_periods * 100 # Assuming pos is -1 for short

            # Recent 365 days calculation
            recent_sharpe_ratio, recent_calmar_ratio, recent_annual_return = np.nan, np.nan, np.nan
            recent_trades_per_year, recent_pos_0_pct, recent_pos_1_pct, recent_pos_neg1_pct = np.nan, np.nan, np.nan, np.nan

            try:
                df_date = df_clean.copy()
                if not isinstance(df_date.index, pd.DatetimeIndex):
                    if 't' in df_date.columns:
                         df_date['t'] = pd.to_datetime(df_date['t'])
                         df_date = df_date.set_index('t')
                    else:
                         df_date.index = pd.to_datetime(df_date.index)

                if isinstance(df_date.index, pd.DatetimeIndex) and not df_date.empty:
                    latest_date = df_date.index.max()
                    date_365_days_ago = latest_date - pd.Timedelta(days=365)
                    recent_df = df_date[df_date.index >= date_365_days_ago]

                    if len(recent_df) >= 30:
                        recent_returns = recent_df['pnl'].values
                        recent_num_trades = len(recent_df[recent_df['pnl'] != 0])
                        recent_annual_return = np.mean(recent_returns) * time_variable
                        recent_sharpe_ratio = recent_annual_return / (np.std(recent_returns) * np.sqrt(time_variable)) if np.std(recent_returns) != 0 else np.nan

                        recent_cumulative_returns = np.cumsum(recent_returns)
                        recent_running_max = np.maximum.accumulate(recent_cumulative_returns)
                        recent_drawdowns = recent_running_max - recent_cumulative_returns
                        recent_max_drawdown = np.max(recent_drawdowns) if len(recent_drawdowns) > 0 else 0
                        recent_calmar_ratio = recent_annual_return / recent_max_drawdown if recent_max_drawdown > 0 else np.nan
                        recent_trades_per_year = recent_num_trades * time_variable / len(recent_df) if len(recent_df)>0 else 0

                        if 'pos' in recent_df.columns:
                            recent_total_periods = len(recent_df)
                            recent_pos_0_pct = (recent_df['pos'] == 0).sum() / recent_total_periods * 100
                            recent_pos_1_pct = (recent_df['pos'] == 1).sum() / recent_total_periods * 100
                            recent_pos_neg1_pct = (recent_df['pos'] == -1).sum() / recent_total_periods * 100
                    else:
                        logger.warning(f"策略 {name} 最近 365 天數據不足 ({len(recent_df)} 行)，跳過計算最近指標")
                else:
                     logger.warning(f"策略 {name} 無法轉換為日期索引或為空，跳過計算最近指標")

            except Exception as e:
                logger.warning(f"計算策略 {name} 最近 365 天指標時出錯: {str(e)}")

            results['performance_metrics'].append({
                'strategy': name,
                'sharpe_ratio': sharpe_ratio,
                'calmar_ratio': calmar_ratio,
                'annual_return': annual_return,
                'trades_per_year': trades_per_year,
                'pos_0_pct': pos_0_pct,
                'pos_1_pct': pos_1_pct,
                'pos_neg1_pct': pos_neg1_pct,
                'recent_sharpe_ratio': recent_sharpe_ratio,
                'recent_calmar_ratio': recent_calmar_ratio,
                'recent_annual_return': recent_annual_return,
                'recent_trades_per_year': recent_trades_per_year,
                'recent_pos_0_pct': recent_pos_0_pct,
                'recent_pos_1_pct': recent_pos_1_pct,
                'recent_pos_neg1_pct': recent_pos_neg1_pct
            })

            logger.info(f"策略 {name} 表現指標計算完成: SR={sharpe_ratio:.4f}, CR={calmar_ratio:.4f}, AR={annual_return:.4f}, 交易/年={trades_per_year:.1f}")
            if not np.isnan(recent_sharpe_ratio):
                logger.info(f"策略 {name} 最近 365 天表現: SR={recent_sharpe_ratio:.4f}, CR={recent_calmar_ratio:.4f}, AR={recent_annual_return:.4f}, 交易/年={recent_trades_per_year:.1f}")

        except Exception as e:
            logger.error(f"計算策略 {name} 的表現指標時出錯: {str(e)}")
            logger.error(traceback.format_exc())

    if results['performance_metrics']:
        performance_df = pd.DataFrame(results['performance_metrics'])
        performance_path = os.path.join(metrics_dir, 'strategy_performance_metrics.csv')
        performance_df.to_csv(performance_path, index=False)
        logger.info(f"已保存表現指標到 {performance_path}")

    # 3. 計算滾動夏普比率
    logger.info("計算滾動夏普比率...")
    all_rolling_sharpe = {}

    for name, df_orig in self.strategy_dfs.items():
        df = df_orig.copy()
        try:
            if 'pnl' not in df.columns:
                logger.warning(f"策略 {name} 缺少必要的列 (pnl)，跳過滾動夏普比率計算")
                continue

            df_date = df.copy()
            if not isinstance(df_date.index, pd.DatetimeIndex):
                if 't' in df_date.columns:
                    try:
                         df_date['t'] = pd.to_datetime(df_date['t'])
                         df_date = df_date.set_index('t')
                    except Exception as e:
                         logger.warning(f"策略 {name} 無法轉換 't' 列為日期時間: {e}，跳過滾動計算")
                         continue
                else:
                    try:
                         df_date.index = pd.to_datetime(df_date.index)
                    except Exception as e:
                        logger.warning(f"策略 {name} 無法轉換索引為日期時間: {e}，跳過滾動計算")
                        continue
            # Ensure index is sorted after potential conversion
            df_date = df_date.sort_index()

            if df_date.empty:
                 logger.warning(f"策略 {name} DataFrame 為空，跳過滾動計算")
                 continue

            # 按日重採樣 PnL (確保即使只有一天數據也能處理)
            daily_pnl = df_date['pnl'].resample('D').sum()

            if daily_pnl.empty:
                 logger.warning(f"策略 {name} 重採樣後無日數據，跳過滾動計算")
                 continue

            # 轉換為DataFrame以方便操作
            daily_pnl = daily_pnl.reset_index()
            daily_pnl.columns = ['date', 'pnl']
            
            # 按日期排序
            daily_pnl = daily_pnl.sort_values('date')
            
            logger.info(f"策略 {name} 原始數據點: {len(df_date)}, 日數據點: {len(daily_pnl)}")
            logger.info(f"日期範圍: {daily_pnl['date'].min()} 到 {daily_pnl['date'].max()}")
            
            # 獲取每月的第一天
            min_date = daily_pnl['date'].min()
            max_date = daily_pnl['date'].max()
            
            # 創建月初日期列表
            first_days = []
            current_date = min_date.replace(day=1)
            while current_date <= max_date:
                first_days.append(current_date)
                # 計算下個月的第一天
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)
            
            # 初始化結果列表
            dates = []
            sharpe_ratios = []
            
            # 設置滑動窗口大小（默認為 365 天）
            window_days = 365
            
            # 為每個月初計算滾動夏普比率
            for date in first_days:
                # 找到最接近的日期索引
                closest_idx = (daily_pnl['date'] - date).abs().idxmin()
                
                # 獲取窗口數據
                if closest_idx >= window_days:
                    window_data = daily_pnl.iloc[closest_idx - window_days + 1:closest_idx + 1]
                    
                    # 如果數據不足（允許有 10% 的缺失）
                    if len(window_data) < window_days * 0.9:
                        continue
                    
                    # 計算回報
                    returns = window_data['pnl'].values
                    
                    # 計算夏普比率
                    sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(365) if np.std(returns) != 0 else np.nan
                    
                    # 存儲結果
                    dates.append(date)
                    sharpe_ratios.append(sharpe_ratio)
            
            # 創建結果 DataFrame
            rolling_sharpe_df = pd.DataFrame({
                'date': dates,
                'sharpe_ratio': sharpe_ratios
            })
            
            # 添加月份標籤
            rolling_sharpe_df['month'] = rolling_sharpe_df['date'].dt.strftime('%Y-%m')
            
            # 存儲結果
            if not rolling_sharpe_df.empty:
                all_rolling_sharpe[name] = rolling_sharpe_df
                logger.info(f"策略 {name} 滾動夏普比率計算完成: 數據點: {len(rolling_sharpe_df)}, 日期範圍: {rolling_sharpe_df['date'].min()} 到 {rolling_sharpe_df['date'].max()}, 平均 SR: {rolling_sharpe_df['sharpe_ratio'].mean():.4f}")
            else:
                logger.warning(f"策略 {name} 無法計算滾動夏普比率 (可能數據不足或標準差為零)")

        except Exception as e:
            logger.error(f"計算策略 {name} 的滾動夏普比率時出錯: {str(e)}")
            logger.error(traceback.format_exc())

    # 儲存滾動夏普比率 & 進行篩選
    if all_rolling_sharpe:
        all_data = []
        for strategy_name, df in all_rolling_sharpe.items():
            df_copy = df.copy()
            df_copy['strategy'] = strategy_name
            all_data.append(df_copy[['strategy', 'month', 'sharpe_ratio']])

        if all_data:
             combined_df = pd.concat(all_data, ignore_index=True)

             # Convert month period to string for compatibility if needed
             combined_df['month'] = combined_df['month'].astype(str)

             # Check for duplicates (for debugging, can be removed later)
             duplicates = combined_df[combined_df.duplicated(subset=['strategy', 'month'], keep=False)]
             if not duplicates.empty:
                 logger.warning(f"Found duplicate strategy/month entries before pivoting:\\n{duplicates}")
                 # Aggregate to handle potential duplicates before pivoting by taking the mean
                 combined_df = combined_df.groupby(['strategy', 'month'], as_index=False).agg(sharpe_ratio=('sharpe_ratio', 'mean'))
                 logger.info("Aggregated monthly results by strategy and month (mean) before pivoting.")
             
             # Pivot the table for monthly Sharpe ratios
             transposed_df = None # Initialize
             if not combined_df.empty:
                 try:
                     transposed_df = combined_df.pivot(index='strategy', columns='month', values='sharpe_ratio')
                     # Sort columns (months) chronologically
                     transposed_df = transposed_df.sort_index(axis=1)
                     # Save transposed data
                     monthly_sharpe_path = os.path.join(metrics_dir, 'strategy_monthly_sharpe.csv')
                     transposed_df.to_csv(monthly_sharpe_path)
                     logger.info(f"已保存月度 Sharpe Ratio 到 {monthly_sharpe_path}")
                 except ValueError as e:
                     logger.error(f"Error pivoting or saving monthly Sharpe data: {e}")
                     logger.error(f"Combined DF sample before error:\\n{combined_df.head()}")
                     # Create an empty DataFrame to avoid downstream errors if pivot fails
                     transposed_df = pd.DataFrame()
             else:
                 logger.warning("Combined monthly Sharpe DataFrame is empty, skipping pivot and save.")
                 transposed_df = pd.DataFrame()


             # --- Rolling Sharpe Filter Calculation ---

             results['rolling_sharpe'] = all_rolling_sharpe # Store the original dict

             # 應用篩選條件
             filtered_strategies_result = self.filter_strategies_by_rolling_sharpe(all_rolling_sharpe, metrics_dir)
             results['filtered_strategies'] = filtered_strategies_result
        else:
             logger.warning("無法合併滾動夏普比率數據")

    else:
        logger.warning("無法計算任何策略的滾動夏普比率")

    # Return the consolidated results dictionary
    self.metrics_results = results # Store results in the instance
    return results

def filter_strategies_by_rolling_sharpe(self, all_rolling_sharpe, metrics_dir):
    """
    根據滾動夏普比率篩選策略

    參數:
        all_rolling_sharpe (dict): 所有策略嘅滾動夏普比率
        metrics_dir (str): 指標保存目錄

    返回:
        dict: 篩選結果
    """
    logger.info("開始根據滾動夏普比率篩選策略...")

    if not all_rolling_sharpe:
        logger.warning("無滾動夏普比率數據可用，無法進行篩選")
        return {}

    # 從配置讀取篩選閾值
    config = load_or_create_config()
    threshold_avg_sr = config.get("threshold_avg_sr", 1.5)       # 閾值1：平均滾動夏普比率最低要求
    threshold_recent_sr = config.get("threshold_recent_sr", 1.5)   # 閾值2：近期滾動夏普比率最低要求
    recent_period_months = config.get("recent_period_months", 6)   # 定義"近期"嘅月數，默認6個月

    logger.info(f"使用閾值: 平均SR>={threshold_avg_sr}, 近期{recent_period_months}個月SR>={threshold_recent_sr}")

    # 初始化結果字典
    filtered_results = {
        'original_count': len(all_rolling_sharpe),
        'filter1_avg_sr': {'threshold': threshold_avg_sr, 'passed': {}, 'filtered_out': {}},
        'filter2_recent_sr': {'threshold': threshold_recent_sr, 'passed': {}, 'filtered_out': {}},
        'final_passed': {}
    }

    # 1. 先排除平均 Rolling Sharpe < 閾值1 嘅策略
    avg_sharpes = {}
    
    for name, df in all_rolling_sharpe.items():
        avg_sr = df['sharpe_ratio'].mean()
        avg_sharpes[name] = avg_sr
        
        if not np.isnan(avg_sr) and avg_sr >= threshold_avg_sr:
            filtered_results['filter1_avg_sr']['passed'][name] = avg_sr
        else:
            filtered_results['filter1_avg_sr']['filtered_out'][name] = avg_sr
    
    # 如果第一階段篩選後沒有策略通過，降低閾值再試
    if not filtered_results['filter1_avg_sr']['passed']:
        logger.warning(f"沒有策略通過平均 Rolling Sharpe > {threshold_avg_sr} 的條件，嘗試降低閾值至 0.0")
        threshold_avg_sr = 0.0
        filtered_results['filter1_avg_sr']['threshold'] = threshold_avg_sr
        
        for name, avg_sr in avg_sharpes.items():
            if not np.isnan(avg_sr) and avg_sr >= threshold_avg_sr:
                filtered_results['filter1_avg_sr']['passed'][name] = avg_sr
            else:
                filtered_results['filter1_avg_sr']['filtered_out'][name] = avg_sr
        
        # 如果還是沒有策略通過，使用所有策略
        if not filtered_results['filter1_avg_sr']['passed']:
            logger.warning("即使閾值降至 0.0 也沒有策略通過，使用所有策略")
            for name, avg_sr in avg_sharpes.items():
                filtered_results['filter1_avg_sr']['passed'][name] = avg_sr
    
    # 2. 然後排除近 6 個月 Rolling Sharpe 平均值 < 閾值2 嘅策略
    recent_avg_srs = {}
    
    for name in filtered_results['filter1_avg_sr']['passed'].keys():
        df = all_rolling_sharpe[name]
        
        # 確保按日期排序
        df_sorted = df.sort_values('date')
        
        # 獲取最後N個月的數據（如果有的話）
        months = min(recent_period_months, len(df_sorted))
        if months > 0:
            recent_sr = df_sorted['sharpe_ratio'].iloc[-months:].mean()
            recent_avg_srs[name] = recent_sr
            
            if not np.isnan(recent_sr) and recent_sr >= threshold_recent_sr:
                filtered_results['filter2_recent_sr']['passed'][name] = recent_sr
                # 這些是通過所有篩選的最終策略
                filtered_results['final_passed'][name] = {
                    'avg_sr': avg_sharpes[name],
                    'recent_sr': recent_avg_srs.get(name, np.nan)
                }
            else:
                filtered_results['filter2_recent_sr']['filtered_out'][name] = recent_sr
        else:
            # 數據不足6個月，也視為通過
            filtered_results['filter2_recent_sr']['passed'][name] = np.nan
            recent_avg_srs[name] = np.nan
            filtered_results['final_passed'][name] = {
                'avg_sr': avg_sharpes[name],
                'recent_sr': np.nan
            }
    
    # 如果第二階段篩選後沒有策略通過，降低閾值再試
    if not filtered_results['filter2_recent_sr']['passed']:
        logger.warning(f"沒有策略通過近期 Rolling Sharpe > {threshold_recent_sr} 的條件，嘗試降低閾值至 0.0")
        threshold_recent_sr = 0.0
        filtered_results['filter2_recent_sr']['threshold'] = threshold_recent_sr
        
        for name, recent_sr in recent_avg_srs.items():
            if not np.isnan(recent_sr) and recent_sr >= threshold_recent_sr:
                filtered_results['filter2_recent_sr']['passed'][name] = recent_sr
                filtered_results['final_passed'][name] = {
                    'avg_sr': avg_sharpes[name],
                    'recent_sr': recent_avg_srs.get(name, np.nan)
                }
            else:
                filtered_results['filter2_recent_sr']['filtered_out'][name] = recent_sr
        
        # 如果還是沒有策略通過，使用所有策略
        if not filtered_results['filter2_recent_sr']['passed']:
            logger.warning("即使閾值降至 0.0 也沒有策略通過，使用第一階段通過的所有策略")
            for name in filtered_results['filter1_avg_sr']['passed'].keys():
                filtered_results['filter2_recent_sr']['passed'][name] = recent_avg_srs.get(name, np.nan)
                filtered_results['final_passed'][name] = {
                    'avg_sr': avg_sharpes[name],
                    'recent_sr': recent_avg_srs.get(name, np.nan)
                }
    
    # 輸出篩選結果日誌
    logger.info(f"篩選結果:")
    logger.info(f"原始策略數量: {filtered_results['original_count']}")
    logger.info(f"1. 平均 Rolling Sharpe >= {threshold_avg_sr}: {len(filtered_results['filter1_avg_sr']['passed'])}/{filtered_results['original_count']} 通過")
    logger.info(f"2. 近 {recent_period_months} 個月平均 Rolling Sharpe >= {threshold_recent_sr}: {len(filtered_results['filter2_recent_sr']['passed'])}/{len(filtered_results['filter1_avg_sr']['passed'])} 通過")
    logger.info(f"最終通過所有篩選條件的策略數量: {len(filtered_results['final_passed'])}")

    # 保存篩選結果
    if filtered_results['final_passed']:
        # 將最終通過的策略轉換為 DataFrame 保存
        passed_data = []
        for name, metrics in filtered_results['final_passed'].items():
            row = {
                'strategy': name,
                'avg_sharpe_ratio': metrics['avg_sr'],
                'recent_sr': metrics['recent_sr']  # 更改列名，移除"6m"
            }
            passed_data.append(row)
            
        passed_df = pd.DataFrame(passed_data)
        passed_path = os.path.join(metrics_dir, 'filtered_strategies.csv')
        passed_df.to_csv(passed_path, index=False)
        logger.info(f"已將通過篩選的策略保存到 {passed_path}")
        
        # 將通過各階段篩選的策略名稱保存到文本文件，便於查看
        summary_path = os.path.join(metrics_dir, 'strategy_filtering_summary.txt')
        with open(summary_path, 'w') as f:
            f.write(f"策略篩選總結\n")
            f.write(f"==================\n\n")
            f.write(f"原始策略總數: {filtered_results['original_count']}\n\n")
            
            f.write(f"篩選條件 1: 平均 Rolling Sharpe >= {threshold_avg_sr}\n")
            f.write(f"通過數量: {len(filtered_results['filter1_avg_sr']['passed'])}\n")
            f.write(f"通過策略: {', '.join(filtered_results['filter1_avg_sr']['passed'].keys())}\n\n")
            
            f.write(f"篩選條件 2: 近 {recent_period_months} 個月平均 Rolling Sharpe >= {threshold_recent_sr}\n")
            f.write(f"通過數量: {len(filtered_results['filter2_recent_sr']['passed'])}\n")
            f.write(f"通過策略: {', '.join(filtered_results['filter2_recent_sr']['passed'].keys())}\n\n")
            
            f.write(f"最終通過所有篩選條件的策略: {len(filtered_results['final_passed'])}\n")
            f.write(f"{', '.join(filtered_results['final_passed'].keys())}\n")
        
        logger.info(f"已將篩選條件總結保存到 {summary_path}")
        
        # 將通過篩選的策略名稱保存到實例變量
        self.filtered_strategies = list(filtered_results['final_passed'].keys())
        logger.info(f"已將 {len(self.filtered_strategies)} 個通過篩選的策略儲存到實例變量中供其他函數使用")
    else:
        logger.warning("沒有策略通過所有篩選條件，篩選結果為空")
        self.filtered_strategies = []
        
    return filtered_results

def plot_recent_portfolio(self, days=30, save_path=None):
        """
        繪製最近指定天數嘅組合 equity curve

        參數:
            days (int): 要顯示嘅最近天數，默認30天
            save_path (str): 保存路徑，如果為 None，使用默認路徑
        """
        if not self.portfolio_df is not None:
            logger.error("無可用組合數據，請先運行 generate_portfolio()")
            return None

        if save_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            weighting_method = self.portfolio_metrics['weighting_method'] if hasattr(self, 'portfolio_metrics') else "equal"
            save_path = os.path.join(self.output_dir, f"portfolio_recent_{days}days_{weighting_method}_{timestamp}.png")
        
        try:
            # 確保圖表開始前關閉所有現有圖形
            plt.close('all')
            
            # 設置字體和樣式
            plt.rcParams.update({
                'font.family': 'DejaVu Sans',
                'font.size': 10,
                'lines.linewidth': 2,
                'axes.grid': True,
                'grid.alpha': 0.3,
                'axes.labelsize': 10
            })
            
            # 獲取最近N天的數據
            df = self.portfolio_df.copy()
            
            # 輸出調試信息
            logger.info(f"Portfolio DataFrame 列名: {df.columns.tolist()}")
            logger.info(f"Portfolio DataFrame 索引類型: {type(df.index)}")
            logger.info(f"是否有 bnh_cumu 列: {'bnh_cumu' in df.columns}")
            
            # 確保索引是日期時間類型
            if not isinstance(df.index, pd.DatetimeIndex):
                try:
                    if 't' in df.columns:
                        df = df.set_index('t')
                    else:
                        df.index = pd.to_datetime(df.index)
                except Exception as e:
                    logger.warning(f"無法將索引轉換為日期時間格式: {str(e)}")
            
            # 獲取最新日期
            latest_date = df.index.max()
            logger.info(f"最新日期: {latest_date}")
            
            # 計算N天前的日期
            start_date = latest_date - pd.Timedelta(days=days)
            logger.info(f"{days}天前日期: {start_date}")
            
            # 篩選最近N天的數據
            recent_df = df[df.index >= start_date].copy()
            logger.info(f"最近{days}天數據點數量: {len(recent_df)}")
            
            # 輸出更多調試信息
            if len(recent_df) > 0:
                logger.info(f"最近數據第一天: {recent_df.index[0]}")
                logger.info(f"最近數據最後一天: {recent_df.index[-1]}")
            
            # 如果數據不足，提供警告但繼續
            if len(recent_df) < 2:
                logger.warning(f"最近 {days} 天只有 {len(recent_df)} 個數據點，可能無法繪製有意義的圖表")
                if len(recent_df) == 0:
                    return None

            # 重置累積收益，使其從0開始
            initial_cumu = recent_df['cumu'].iloc[0]
            recent_df['norm_cumu'] = recent_df['cumu'] - initial_cumu
            
            # 手動創建基準Buy & Hold曲線（不依賴現有數據）
            # 1. 從每日價格變化計算
            if 'percentage_change' in recent_df.columns:
                recent_df['computed_bnh'] = (1 + recent_df['percentage_change']).cumprod() - 1
                logger.info("已計算基準 Buy & Hold 曲線 (從 percentage_change)")
            elif 'pnl' in recent_df.columns and 'pos' in recent_df.columns:
                # 2. 嘗試從pnl和pos估算價格變化
                # 假設 pnl = pos * price_change - transaction_cost
                estimated_price_change = recent_df['pnl'] / recent_df['pos'].where(recent_df['pos'] != 0, 1)
                # 替換無效值
                estimated_price_change = estimated_price_change.replace([np.inf, -np.inf], np.nan).fillna(0)
                recent_df['computed_bnh'] = (1 + estimated_price_change).cumprod() - 1
                logger.info("已估算基準 Buy & Hold 曲線 (從 pnl/pos)")
            
            # 處理已有的Buy & Hold曲線
            if 'bnh_cumu' in recent_df.columns:
                logger.info("發現 bnh_cumu 列，嘗試使用它作為基準")
                initial_bnh = recent_df['bnh_cumu'].iloc[0]
                recent_df['norm_bnh_cumu'] = recent_df['bnh_cumu'] - initial_bnh
                logger.info(f"基準 Buy & Hold 開始值: {initial_bnh}, 結束值: {recent_df['bnh_cumu'].iloc[-1]}")
                logger.info(f"基準 Buy & Hold 回報: {(recent_df['norm_bnh_cumu'].iloc[-1])*100:.2f}%")
            
            # 選擇最合適的Buy & Hold曲線用於繪圖
            bnh_column_for_plot = None
            
            # 打印出數據框的列名，檢查是否有pos列
            logger.info(f"recent_df 列名: {recent_df.columns.tolist()}")
            logger.info(f"是否包含 'pos' 列: {'pos' in recent_df.columns}")
            if 'pos' in recent_df.columns:
                logger.info(f"pos 列的前5個值: {recent_df['pos'].head().tolist()}")
            else:
                logger.warning("數據中不存在 'pos' 列，將嘗試從其他列推斷或創建")
                # 嘗試從策略數據推導組合倉位
                try:
                    if hasattr(self, 'portfolio_weights') and hasattr(self, 'strategy_dfs'):
                        logger.info("嘗試從策略數據推導組合倉位...")
                        recent_df['pos'] = 0.0
                        for name, weight in self.portfolio_weights.items():
                            if name in self.strategy_dfs:
                                strategy_df = self.strategy_dfs[name]
                                if isinstance(strategy_df.index, pd.DatetimeIndex) and 'pos' in strategy_df.columns:
                                    # 僅選取在recent_df索引範圍內的數據
                                    matching_idx = strategy_df.index.intersection(recent_df.index)
                                    if not matching_idx.empty:
                                        for idx in matching_idx:
                                            recent_df.loc[idx, 'pos'] += strategy_df.loc[idx, 'pos'] * weight
                        logger.info(f"成功創建 'pos' 列，前5個值: {recent_df['pos'].head().tolist()}")
                except Exception as e:
                    logger.error(f"嘗試推導倉位時出錯: {str(e)}")

            # 嘗試計算正確的 Buy & Hold 曲線
            try:
                # 首先嘗試從自動獲取真實加密貨幣價格數據
                try:
                    # 獲取資產符號
                    asset_symbol = self.asset_info.get('glassnode_symbol', 'BTC')
                    
                    # 檢查有沒有直接可用的價格資料
                    recent_price_df = None
                    for name, strategy_df in self.strategy_dfs.items():
                        # 尋找包含價格數據的策略
                        if 'price' in strategy_df.columns:
                            # 確保時間索引一致
                            if isinstance(strategy_df.index, pd.DatetimeIndex):
                                price_df = strategy_df[strategy_df.index >= start_date]
                                if len(price_df) > 0:
                                    logger.info(f"找到可用的{asset_symbol}價格數據: 策略 {name}")
                                    recent_price_df = price_df
                                    break
                    
                    # 如果找到價格數據
                    if recent_price_df is not None:
                        # 使用真實價格計算 Buy & Hold 收益
                        initial_price = recent_price_df['price'].iloc[0]
                        # 複製索引對應的價格到recent_df
                        price_series = recent_price_df['price']
                        price_dict = dict(zip(price_series.index, price_series.values))
                        
                        # 為recent_df中的每個日期找到最接近的價格
                        crypto_prices = []
                        for date in recent_df.index:
                            if date in price_dict:
                                crypto_prices.append(price_dict[date])
                            else:
                                # 找出最接近的日期
                                closest_date = min(price_dict.keys(), key=lambda x: abs(x - date))
                                crypto_prices.append(price_dict[closest_date])
                        
                        # 計算Buy & Hold收益曲線
                        recent_df['real_bnh'] = [price/initial_price - 1 for price in crypto_prices]
                        bnh_column_for_plot = 'real_bnh'
                        logger.info(f"已計算真實{asset_symbol} Buy & Hold曲線")
                    else:
                        # 如果沒找到價格數據，使用備用方案
                        logger.warning(f"沒有找到{asset_symbol}的價格數據，使用估算值")
                        raise ValueError("No price data found")
                        
                except Exception as price_err:
                    logger.error(f"獲取真實價格數據時出錯: {str(price_err)}")
                    
                    # 備選方案 - 嘗試使用價格接口獲取數據
                    try:
                        from datetime import datetime, timedelta
                        import requests
                        
                        # 獲取API key (如果已經定義)
                        api_key = backtesting.API_KEY if hasattr(backtesting, 'API_KEY') else None
                        
                        if api_key:
                            # Glassnode API路徑
                            api_url = "https://api.glassnode.com/v1/metrics/market/price_usd_close"
                            
                            # 設置時間範圍
                            start_timestamp = int((start_date - timedelta(days=5)).timestamp())
                            end_timestamp = int((latest_date + timedelta(days=1)).timestamp())
                            
                            # 獲取幣種符號
                            asset_symbol = self.asset_info.get('glassnode_symbol', 'BTC')
                            
                            # 發送請求
                            logger.info(f"嘗試從Glassnode獲取{asset_symbol}價格數據")
                            res = requests.get(api_url, params={
                                "a": asset_symbol, 
                                "s": start_timestamp, 
                                "u": end_timestamp, 
                                "api_key": api_key, 
                                "i": "24h"  # 日線數據
                            }, timeout=10)
                            
                            if res.status_code == 200:
                                # 解析數據
                                price_data = res.json()
                                if price_data and len(price_data) > 0:
                                    # 將數據轉為DataFrame
                                    price_df = pd.DataFrame(price_data)
                                    price_df['t'] = pd.to_datetime(price_df['t'], unit='s')
                                    price_df = price_df.set_index('t')
                                    
                                    # 獲取初始價格和最終價格
                                    initial_price = price_df['v'].iloc[0]
                                    
                                    # 複製索引對應的價格到recent_df
                                    price_series = price_df['v']
                                    price_dict = dict(zip(price_series.index, price_series.values))
                                    
                                    # 為recent_df中的每個日期找到最接近的價格
                                    crypto_prices = []
                                    for date in recent_df.index:
                                        if date in price_dict:
                                            crypto_prices.append(price_dict[date])
                                        else:
                                            # 找出最接近的日期
                                            closest_date = min(price_dict.keys(), key=lambda x: abs(x - date))
                                            crypto_prices.append(price_dict[closest_date])
                                    
                                    # 計算Buy & Hold收益曲線
                                    recent_df['real_bnh'] = [price/initial_price - 1 for price in crypto_prices]
                                    bnh_column_for_plot = 'real_bnh'
                                    logger.info(f"已從Glassnode計算真實{asset_symbol} Buy & Hold曲線")
                                else:
                                    logger.warning(f"從Glassnode獲取的{asset_symbol}價格數據為空")
                                    raise ValueError("Empty price data from API")
                            else:
                                logger.warning(f"Glassnode API返回錯誤: {res.status_code} - {res.text}")
                                raise ValueError(f"API error: {res.status_code}")
                        else:
                            logger.warning("沒有找到API key，無法獲取價格數據")
                            raise ValueError("No API key found")
                            
                    except Exception as api_err:
                        logger.error(f"從API獲取價格數據時出錯: {str(api_err)}")
                
                # 如果上述方法都失敗，則嘗試使用備選方案
                if bnh_column_for_plot is None:
                    # 檢查是否有原始資產價格數據可用
                    asset_price_col = None
                    for col in recent_df.columns:
                        if 'price' in col.lower() or 'close' in col.lower():
                            asset_price_col = col
                            break
                    
                    # 如果有價格數據，直接重新計算 Buy & Hold 曲線
                    if asset_price_col:
                        logger.info(f"使用 {asset_price_col} 重新計算 Buy & Hold 曲線")
                        recent_df['recalculated_bnh'] = recent_df[asset_price_col] / recent_df[asset_price_col].iloc[0] - 1
                        bnh_column_for_plot = 'recalculated_bnh'
                    # 嘗試使用已有的 BnH 曲線，但先檢查數據是否合理
                    elif 'norm_bnh_cumu' in recent_df.columns and not recent_df['norm_bnh_cumu'].isnull().all():
                        # 檢查是否有極端值
                        if recent_df['norm_bnh_cumu'].min() > -1.0 and recent_df['norm_bnh_cumu'].max() < 2.0:
                            bnh_column_for_plot = 'norm_bnh_cumu'
                            logger.info("使用 norm_bnh_cumu 作為Buy & Hold曲線")
                        else:
                            logger.warning("norm_bnh_cumu 包含極端值，不使用")
                    elif 'computed_bnh' in recent_df.columns:
                        # 檢查是否有極端值
                        if recent_df['computed_bnh'].min() > -1.0 and recent_df['computed_bnh'].max() < 2.0:
                            bnh_column_for_plot = 'computed_bnh'
                            logger.info("使用計算得出的 computed_bnh 作為Buy & Hold曲線")
                        else:
                            logger.warning("computed_bnh 包含極端值，不使用")
                    
                    # 如果仍然沒有可用的曲線，嘗試使用基礎資產收益率計算
                    if bnh_column_for_plot is None and 'pnl' in recent_df.columns:
                        logger.info("嘗試從基礎資產收益率計算 Buy & Hold 曲線")
                        # 假設BH等於每日市場的平均變化
                        recent_df['fallback_bnh'] = recent_df['pnl'].cumsum() * 0.8  # 使用80%的組合收益作為假BnH
                        bnh_column_for_plot = 'fallback_bnh'
            except Exception as e:
                logger.error(f"計算Buy & Hold曲線時出錯: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
            
            if bnh_column_for_plot is None:
                logger.warning("無法獲取有效的Buy & Hold曲線數據")
            
            # 計算期間表現指標
            time_variable = 365  # 年化因子
            
            # 原權重指標
            daily_return_mean = recent_df['pnl'].mean()
            daily_return_std = recent_df['pnl'].std()
            
            sr = daily_return_mean / daily_return_std * np.sqrt(time_variable) if daily_return_std != 0 else np.nan
            ar = daily_return_mean * time_variable
            total_return = recent_df['norm_cumu'].iloc[-1] if len(recent_df) > 0 else 0
            
            dd = recent_df['norm_cumu'].cummax() - recent_df['norm_cumu']
            mdd = dd.max()
            cr = ar / mdd if mdd != 0 else np.nan
            
            # Buy & Hold 指標
            bnh_total_return = np.nan
            sr_bnh = np.nan
            ar_bnh = np.nan
            mdd_bnh = np.nan
            cr_bnh = np.nan
            
            if bnh_column_for_plot:
                # 獲取最終收益率
                bnh_final_value = recent_df[bnh_column_for_plot].iloc[-1]
                bnh_total_return = bnh_final_value if 'recalculated_bnh' in bnh_column_for_plot else bnh_final_value
                
                # 從曲線差分計算每日變化
                bnh_daily_changes = recent_df[bnh_column_for_plot].diff().fillna(0)
                
                # 檢查是否有異常大的變化
                max_change = bnh_daily_changes.abs().max()
                if max_change > 0.5:  # 如果單日變化超過50%，視為異常
                    logger.warning(f"Buy & Hold 曲線存在異常大的日變化: {max_change:.2%}")
                    # 將異常值替換為合理範圍的值
                    bnh_daily_changes = bnh_daily_changes.clip(lower=-0.1, upper=0.1)
                
                daily_bnh_return_mean = bnh_daily_changes.mean()
                daily_bnh_return_std = bnh_daily_changes.std()
                
                sr_bnh = daily_bnh_return_mean / daily_bnh_return_std * np.sqrt(time_variable) if daily_bnh_return_std != 0 else np.nan
                ar_bnh = daily_bnh_return_mean * time_variable
                
                bnh_values = recent_df[bnh_column_for_plot]
                bnh_dd = bnh_values.cummax() - bnh_values
                mdd_bnh = bnh_dd.max()
                cr_bnh = ar_bnh / mdd_bnh if mdd_bnh != 0 and mdd_bnh > 0.0001 else np.nan
            
            # 計算勝率
            if 'pnl' in recent_df.columns:
                win_days = len(recent_df[recent_df['pnl'] > 0])
                total_days = len(recent_df)
                win_rate = win_days / total_days * 100 if total_days > 0 else np.nan
            else:
                win_rate = np.nan
            
            # 計算每日做多做空策略數量
            # 從策略權重和各個策略的數據中獲取倉位信息
            strategy_positions = {}
            dates = recent_df.index.tolist()
            
            # 初始化計數列
            recent_df['long_strategies_count'] = 0
            recent_df['short_strategies_count'] = 0
            recent_df['net_position_count'] = 0
            
            # 遍歷所有策略，獲取每個策略在每個日期的倉位
            for name, weight in self.portfolio_weights.items():
                # 獲取策略的 DataFrame
                if name not in self.strategy_dfs:
                    logger.warning(f"策略 {name} 在 strategy_dfs 中找不到，跳過")
                    continue
                    
                strategy_df = self.strategy_dfs[name]
                
                # 確保索引是日期時間類型
                if not isinstance(strategy_df.index, pd.DatetimeIndex):
                    try:
                        if 't' in strategy_df.columns:
                            strategy_df = strategy_df.set_index('t')
                        else:
                            strategy_df.index = pd.to_datetime(strategy_df.index)
                    except Exception as e:
                        logger.warning(f"無法將策略 {name} 的索引轉換為日期時間格式，跳過: {str(e)}")
                        continue
                
                # 篩選最近N天的數據
                strategy_recent_df = strategy_df[strategy_df.index >= start_date].copy()
                
                # 如果策略數據為空，跳過
                if len(strategy_recent_df) == 0:
                    logger.warning(f"策略 {name} 在最近 {days} 天沒有數據，跳過")
                    continue
                
                # 對於每個日期，檢查策略的倉位
                for date in dates:
                    if date in strategy_recent_df.index and 'pos' in strategy_recent_df.columns:
                        # 獲取這個日期的倉位
                        pos = strategy_recent_df.loc[date, 'pos']
                        
                        # 根據倉位正負累加做多/做空策略數量
                        if pos > 0:
                            recent_df.loc[date, 'long_strategies_count'] += 1
                        elif pos < 0:
                            recent_df.loc[date, 'short_strategies_count'] -= 1  # 改為減1，使做空策略數量為負數
            
            # 計算淨倉位數 (做多 + 做空)，因為做空已經是負數，所以直接相加
            recent_df['net_position_count'] = recent_df['long_strategies_count'] + recent_df['short_strategies_count']
            
            logger.info(f"倉位統計:")
            logger.info(f"  平均做多策略數量: {recent_df['long_strategies_count'].mean():.1f}")
            logger.info(f"  平均做空策略數量: {recent_df['short_strategies_count'].mean():.1f}")
            logger.info(f"  平均淨倉位數: {recent_df['net_position_count'].mean():.1f}")
            
            # 創建 4x1 布局的圖表 (改為四個子圖，增加策略數量圖)
            fig, (ax1, ax2, ax3, ax4) = plt.subplots(4, 1, figsize=(12, 16), 
                                        gridspec_kw={'height_ratios': [3, 1, 2, 2], 'hspace': 0.1})
            
            # 主圖 - 繪製累積收益曲線
            ax1.plot(recent_df.index, recent_df['norm_cumu'], 
                   label=f'Portfolio (Return: {total_return*100:.2f}%, Win Rate: {win_rate:.1f}%)', 
                   color='blue', linewidth=2)
            
            # Buy & Hold
            if bnh_column_for_plot:
                ax1.plot(recent_df.index, recent_df[bnh_column_for_plot], 
                       label=f'Buy & Hold (Return: {bnh_total_return*100:.2f}%)', 
                       color='gray', linestyle='--', linewidth=1.5)
                logger.info(f"已繪製 Buy & Hold 曲線 (使用 {bnh_column_for_plot})")
            else:
                logger.warning("無法繪製 Buy & Hold 曲線")
            
            # 設置主圖網格和標籤
            ax1.grid(True, alpha=0.3)
            ax1.set_ylabel('Cumulative Return', fontsize=10)
            
            # 調整 y 軸，確保顯示完整
            all_cumu_values = []
            if 'norm_cumu' in recent_df.columns:
                all_cumu_values.extend(recent_df['norm_cumu'].tolist())
            if bnh_column_for_plot:
                all_cumu_values.extend(recent_df[bnh_column_for_plot].tolist())
            
            # 過濾 NaN 值
            all_cumu_values = [v for v in all_cumu_values if not pd.isna(v)]
            
            if all_cumu_values:
                y_min = min(min(all_cumu_values), 0)  # 確保 y 軸至少從 0 開始
                y_max = max(all_cumu_values)
                # 擴大範圍
                y_range = y_max - y_min
                y_min = y_min - y_range * 0.1
                y_max = y_max + y_range * 0.1
                ax1.set_ylim(y_min, y_max)
            
            # 中間圖 - 繪製倉位
            try:
                if 'pos' in recent_df.columns:
                    logger.info(f"開始繪製倉位圖，pos 列存在，包含 {len(recent_df['pos'])} 個數據點")
                    logger.info(f"pos 列的值範圍: 最小={recent_df['pos'].min()}, 最大={recent_df['pos'].max()}")
                    
                    # 確保數據中不包含NaN值
                    if recent_df['pos'].isnull().any():
                        logger.warning(f"pos 列中包含 {recent_df['pos'].isnull().sum()} 個 NaN 值，將填充為0")
                        recent_df['pos'] = recent_df['pos'].fillna(0)
                    
                    # 繪製倉位
                    pos_line = ax2.plot(recent_df.index, recent_df['pos'], 
                           label='Portfolio Position', color='blue', linewidth=1.5)
                    
                    logger.info(f"倉位線已繪製，繪圖對象: {pos_line}")
                else:
                    logger.warning("'pos' 列不存在於數據中，無法繪製倉位圖")
                    # 添加文字說明
                    ax2.text(0.5, 0.5, 'Position data not available', 
                           ha='center', va='center', transform=ax2.transAxes)
            except Exception as e:
                logger.error(f"繪製倉位圖時出錯: {str(e)}")
                ax2.text(0.5, 0.5, 'Error drawing position chart', 
                       ha='center', va='center', transform=ax2.transAxes)
            
            ax2.set_ylabel('Position', fontsize=10)
            ax2.grid(True, alpha=0.3)
            
            # 設置倉位圖例
            ax2.legend(loc='upper left', fontsize=8)
            
            # 確保倉位圖的垂直範圍合適
            if 'pos' in recent_df.columns and not recent_df['pos'].isnull().all():
                pos_min = recent_df['pos'].min()
                pos_max = recent_df['pos'].max()
                pos_range = pos_max - pos_min
                pos_margin = max(0.1, pos_range * 0.1)  # 至少10%的邊距
                
                if pos_min == pos_max:  # 如果所有值都相同
                    pos_min = pos_min - 0.5
                    pos_max = pos_max + 0.5
                else:
                    pos_min = pos_min - pos_margin
                    pos_max = pos_max + pos_margin
                
                logger.info(f"設置倉位圖的y軸範圍: {pos_min} 到 {pos_max}")
                ax2.set_ylim(pos_min, pos_max)
            
            # 下方圖 - 獨立顯示每日PnL柱狀圖
            if 'pnl' in recent_df.columns:
                # 創建柱狀圖
                bars = ax3.bar(recent_df.index, recent_df['pnl'], 
                              alpha=0.7,
                              color=recent_df['pnl'].apply(lambda x: 'green' if x > 0 else 'red'),
                              label='Daily PnL')
                
                # 添加PnL百分比標籤
                for i, bar in enumerate(bars):
                    height = bar.get_height()
                    pnl_value = recent_df['pnl'].iloc[i]
                    pnl_pct = pnl_value * 100  # 轉為百分比
                    
                    # 判斷標籤顯示位置 (正值在上方，負值在下方)
                    if pnl_value >= 0:
                        va = 'bottom'
                        y_offset = 0.0001  # 減少偏移量，讓文字更貼近柱子
                        label_position = height + y_offset
                    else:
                        va = 'top'
                        y_offset = -0.0001  # 減少偏移量，讓文字更貼近柱子
                        label_position = height + y_offset
                    
                    # 根據數值大小調整標籤格式
                    if abs(pnl_pct) >= 1:
                        # 大於等於1%顯示一位小數
                        pnl_text = f"{pnl_pct:.1f}%"
                    else:
                        # 小於1%顯示兩位小數
                        pnl_text = f"{pnl_pct:.2f}%"
                    
                    ax3.text(bar.get_x() + bar.get_width()/2, label_position,
                           pnl_text, ha='center', va=va, 
                           fontsize=8, rotation=90)  # 添加白色半透明背景
            
            ax3.set_ylabel('Daily PnL', fontsize=10)
            ax3.grid(True, alpha=0.3)
            
            # 設置PnL圖圖例
            ax3.legend(loc='upper left', fontsize=8)
            
            # 添加第四張圖：顯示策略倉位數量
            # 創建堆疊柱狀圖，顯示做多和做空策略數量
            ax4.bar(recent_df.index, recent_df['long_strategies_count'], 
                   alpha=0.7, color='green', label='Long Strategies')
            ax4.bar(recent_df.index, recent_df['short_strategies_count'], 
                   alpha=0.7, color='red', label='Short Strategies')
            
            # 添加水平零線
            ax4.axhline(y=0, color='gray', linestyle='--', alpha=0.7)
            
            # 為每個柱添加策略數量標籤
            for i, date in enumerate(recent_df.index):
                long_count = int(recent_df.loc[date, 'long_strategies_count'])
                short_count = int(recent_df.loc[date, 'short_strategies_count'])  # 已經是負數
                net_count = long_count + short_count  # 淨值(做多 + 做空)
                
                # 創建格式為 "L:x / S:y / z" 的標籤
                label_text = f"L:{long_count} / S:{short_count} / {net_count}"
                
                # 計算標籤位置 - 統一放在柱狀圖的中下方
                # 確定標籤放置的高度
                if long_count > 0 and short_count < 0:
                    # 同時有做多柱和做空柱時，標籤放在做空柱底部
                    label_position = short_count + (abs(short_count) / 10)
                elif long_count > 0:
                    # 只有做多柱時，標籤放在底部1/4位置
                    label_position = long_count / 10
                elif short_count < 0:
                    # 只有做空柱時，也統一放在底部1/4位置
                    label_position = short_count + (abs(short_count) / 10)
                else:
                    # 無策略時，標籤放在零線附近偏下
                    label_position = -0.2
                
                # 將標籤添加到適當位置，統一使用 bottom 對齊
                ax4.text(date, label_position, label_text, 
                        ha='center', va='bottom', fontsize=8, rotation=90, color='black')
            
            # 設置標籤和圖例
            ax4.set_ylabel('Strategy Count', fontsize=10)
            ax4.set_xlabel('Date', fontsize=10)
            ax4.grid(True, alpha=0.3)
            ax4.legend(loc='upper left', fontsize=8)
            
            # 主圖添加圖例
            ax1.legend(loc='upper left', fontsize=9)
            
            # 隱藏上方、中間圖和PnL圖的 x 軸標籤，只在底部顯示
            plt.setp(ax1.get_xticklabels(), visible=False)
            plt.setp(ax2.get_xticklabels(), visible=False)
            plt.setp(ax3.get_xticklabels(), visible=False)
            
            # 共享 x 軸
            ax2.sharex(ax1)
            ax3.sharex(ax1)
            ax4.sharex(ax1)

        # 設置 x 軸日期格式
            if len(recent_df) > 30:  # 如果數據超過30天
                ax4.xaxis.set_major_locator(mdates.WeekdayLocator(byweekday=mdates.MO))  # 每周一
                ax4.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
                ax4.xaxis.set_minor_locator(mdates.DayLocator())  # 每一天
            else:
                ax4.xaxis.set_major_locator(mdates.DayLocator())
                ax4.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
            
            # 設置標題
            asset_symbol = self.asset_info.get('glassnode_symbol', 'Unknown')
            weighting_method = self.portfolio_metrics['weighting_method'].capitalize() if hasattr(self, 'portfolio_metrics') else "Equal"
            
            # 獲取策略總數 - 改為使用實際參與組合的策略數量，而不是總策略數量
            n_strategies = len(self.portfolio_weights)
            
            title = f"Recent {days} Days Portfolio Performance - {asset_symbol} ({weighting_method} Weighting) - {n_strategies} Strategies"
            
            # 添加表現指標
            metrics_info = (
                f"Portfolio: SR={sr:.2f}, CR={cr:.2f}, MDD={mdd:.4f}, AR={ar:.2f}, Return={total_return*100:.2f}%\n"
                f"Buy & Hold: SR={sr_bnh:.2f}, CR={cr_bnh:.2f}, MDD={mdd_bnh:.4f}, AR={ar_bnh:.2f}, Return={bnh_total_return*100:.2f}%"
            )
            title += f"\n{metrics_info}"
            
            # 調整布局，確保沒有重疊
            fig.tight_layout()
            
            # 先調整 top 留出標題空間
            fig.subplots_adjust(top=0.86)  # 將 top 值從0.85降低到0.80，為標題預留更多空間
            
            # 設置標題，調整 y 位置配合 top 空間
            fig.suptitle(title, fontsize=12, y=0.90)  # 將 y 從0.92調整到0.90，與圖表1拉開更大距離
            
            # 自動格式化 x 軸日期標籤，避免重疊
            plt.gcf().autofmt_xdate()
            
            # 保存圖表
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"已保存最近 {days} 天 equity curve 到 {save_path}")
            
            # 返回保存路徑
            return save_path
            
        except Exception as e:
            logger.error(f"繪製最近 {days} 天 equity curve 時出錯: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            plt.close('all')  # 確保發生錯誤時也關閉圖形
            return None

def generate_filtered_strategies_excel(self, target_dir=None):
    """
    生成篩選後的策略組合 Excel 文件
    """
    logger.info("嘗試生成篩選後的策略組合 Excel 文件...")
    save_dir = target_dir if target_dir is not None else self.output_dir
    logger.info(f"將篩選後的策略保存至: {save_dir}")
    os.makedirs(save_dir, exist_ok=True)

    # Ensure metrics_results exist, recalculate if necessary
    if not hasattr(self, 'metrics_results') or not self.metrics_results:
        logger.warning("沒有找到 metrics_results，嘗試重新計算策略指標...")
        try:
            metrics_dir = os.path.join(self.output_dir, 'metrics') # Recalculate in default location if needed
            os.makedirs(metrics_dir, exist_ok=True)
            self.metrics_results = self.calculate_strategy_metrics(metrics_dir=metrics_dir)
            if not self.metrics_results:
                 raise ValueError("策略指標計算失敗")
            logger.info("重新計算策略指標完成")
        except Exception as e:
            logger.error(f"重新計算策略指標時出錯: {str(e)}")
            logger.error(traceback.format_exc())
            # Fallback: use high SR strategies if metrics calculation failed
            try:
                 current_results = self.results_summary if hasattr(self, 'results_summary') else self.results
                 top_strategies = [name for name, result in current_results.items() if result.get('sr', -np.inf) > 1.0]
                 if top_strategies:
                      logger.info(f"回退：找到 {len(top_strategies)} 個 Sharpe Ratio > 1.0 的策略")
                      self.metrics_results = {'filtered_strategies': {'final_passed': {name: {'sr': current_results[name]['sr']} for name in top_strategies}}}
                 else:
                      logger.warning("回退失敗：沒有找到 Sharpe Ratio > 1.0 的策略")
                      self.metrics_results = {'filtered_strategies': {'final_passed': {}}} # Ensure structure exists
            except Exception as fallback_e:
                 logger.error(f"回退策略時出錯: {fallback_e}")
                 self.metrics_results = {'filtered_strategies': {'final_passed': {}}} # Ensure structure exists

    # Get filtered strategies
    filtered_data = self.metrics_results.get('filtered_strategies', {})
    passed_strategies_dict = filtered_data.get('final_passed', {})

    if not passed_strategies_dict:
        logger.warning("沒有策略通過篩選條件，將使用所有策略生成文件")
        passed_strategies_dict = {name: {} for name in self.strategy_dfs.keys()} # Use all if none passed

    passed_strategy_names = list(passed_strategies_dict.keys())
    logger.info(f"用於生成Excel的策略數量: {len(passed_strategy_names)}")

    # Prepare data for DataFrame
    data = []
    current_results = self.results_summary if hasattr(self, 'results_summary') else self.results
    for strategy_config in self.strategies:
        name = strategy_config['name']
        if name in passed_strategy_names:
            # Get performance metrics if available
            perf_metrics = current_results.get(name, {})
            
            # 標準化處理 model 和 type，轉為全小寫並用底線連接
            model = strategy_config.get('model', '')
            strategy_type = strategy_config.get('type', '')
            
            # 將 model 標準化為全小寫，空格轉為底線
            if model:
                model = model.lower().replace(' ', '_')
            
            # 將 type 標準化為全小寫，空格轉為底線
            if strategy_type:
                strategy_type = strategy_type.lower().replace(' ', '_')
            
            # 按要求順序設置欄位，並使用與config_summary.xlsx一致的欄位名稱格式
            row = {
                'API': strategy_config.get('api', ''),
                'Metric Key': strategy_config.get('metric_key', ''),
                'Resolution': strategy_config.get('resolution', ''),
                'API Symbol': strategy_config.get('api_symbol', ''),
                'Strategy Name': name,
                'x': strategy_config.get('x', ''),
                'y': strategy_config.get('y', ''),
                'Asset': strategy_config.get('symbol', ''),
                'Model': model,  # 使用標準化後的 model
                'Style': strategy_config.get('style', ''),
                'Type': strategy_type,  # 使用標準化後的 type
                'Sharpe Ratio': perf_metrics.get('sr', np.nan),
                'Annual Return': perf_metrics.get('ar', np.nan),
                'Max Drawdown': perf_metrics.get('mdd', np.nan),
                'Calmar Ratio': perf_metrics.get('cr', np.nan)
            }
            data.append(row)

    if not data:
         logger.error("沒有篩選後的策略數據可供保存")
         return None

    # 創建 DataFrame 並保持列順序
    df = pd.DataFrame(data)
    
    # 確保列的順序正確，使用與config_summary.xlsx一致的欄位名稱格式
    column_order = [
        'API', 'Metric Key', 'Resolution', 'API Symbol', 'Strategy Name',
        'x', 'y', 'Asset', 'Model', 'Style', 'Type',
        'Sharpe Ratio', 'Annual Return', 'Max Drawdown', 'Calmar Ratio'
    ]
    
    # 篩選實際存在的列（防止某些列不存在）
    actual_columns = [col for col in column_order if col in df.columns]
    df = df[actual_columns]
    
    # Sort by Sharpe Ratio
    df = df.sort_values('Sharpe Ratio', ascending=False)

    # Save to Excel
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_path = os.path.join(save_dir, f"filtered_strategies_{timestamp}.xlsx")
    
    try:
        # 使用ExcelWriter，以便能夠自動調整列寬
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Filtered Strategies')
            
            # 獲取工作表
            worksheet = writer.sheets['Filtered Strategies']
            
            # 自動調整所有列的寬度以適應內容
            for idx, col in enumerate(df.columns):
                # 找出該列中最長的內容
                column_width = max(
                    df[col].astype(str).apply(len).max(),  # 數據中最長內容的長度
                    len(str(col)) + 2                      # 列名的長度加上一些額外空間
                )
                # 設置列寬 (字符數 * 1.2 提供一些額外空間)
                column_letter = worksheet.cell(row=1, column=idx+1).column_letter
                worksheet.column_dimensions[column_letter].width = column_width * 1.0
            
            # 添加自動篩選功能
            worksheet.auto_filter.ref = worksheet.dimensions
            
        logger.info(f"已將篩選後的 {len(df)} 個策略保存到 {output_path}")
        print(f"篩選後的策略已保存到文件: {output_path}")
        return output_path
    except Exception as e:
        logger.error(f"保存篩選後的策略 Excel 文件時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        # Fallback to CSV
        try:
            csv_path = os.path.join(save_dir, f"filtered_strategies_{timestamp}.csv")
            df.to_csv(csv_path, index=False)
            logger.info(f"已將篩選後的策略保存為 CSV 格式: {csv_path}")
            print(f"已將篩選後的策略保存為 CSV 格式: {csv_path}")
            return csv_path
        except Exception as csv_error:
            logger.error(f"保存為 CSV 也失敗: {str(csv_error)}")
        return None

def plot_portfolio(self, save_path=None):
        """
        繪製組合 equity curve
        
        參數:
            save_path (str): 保存路徑，如果為 None，使用默認路徑
        """
        if not self.portfolio_df is not None:
            logger.error("無可用組合數據，請先運行 generate_portfolio()")
            return None
            
        if save_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            weighting_method = self.portfolio_metrics['weighting_method'] if hasattr(self, 'portfolio_metrics') else "equal"
            save_path = os.path.join(self.output_dir, f"portfolio_equity_curve_{weighting_method}_{timestamp}.png")
            
        try:
            # 確保圖表開始前關閉所有現有圖形
            plt.close('all')
            
            # 設置字體和樣式
            plt.rcParams.update({
                'font.family': 'DejaVu Sans',
                'font.size': 10,
                'lines.linewidth': 2,
                'axes.grid': True,
                'grid.alpha': 0.3,
                'axes.labelsize': 10
            })
            
            # 創建 2x1 布局的圖表
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), 
                                          gridspec_kw={'height_ratios': [3, 1], 'hspace': 0.1})
            
            # 主圖 - 繪製組合 equity curve
            ax1.plot(self.portfolio_df.index, self.portfolio_df['cumu'], 
                   label='Portfolio', color='blue', linewidth=2)
                   
            # 繪製 Buy & Hold 曲線（如果有）
            if 'bnh_cumu' in self.portfolio_df.columns:
                ax1.plot(self.portfolio_df.index, self.portfolio_df['bnh_cumu'], 
                       label='Buy & Hold', color='gray', linestyle='--', linewidth=1.5)
                # 計算 Buy & Hold 性能指標
                try:
                    bnh_returns = self.portfolio_df['percentage_change'].values if 'percentage_change' in self.portfolio_df.columns else []
                    if len(bnh_returns) > 0:
                        bnh_ar = np.mean(bnh_returns) * 365  # 年化回報
                        bnh_vol = np.std(bnh_returns) * np.sqrt(365)  # 年化波動率
                        bnh_sr = bnh_ar / bnh_vol if bnh_vol != 0 else np.nan  # 夏普比率
                        bnh_dd = (self.portfolio_df['bnh_cumu'].cummax() - self.portfolio_df['bnh_cumu']).max()  # 最大回撤
                        bnh_cr = bnh_ar / bnh_dd if bnh_dd != 0 else np.nan  # 卡爾瑪比率
                        
                        # 更新 Buy & Hold 標籤，加入性能指標
                        ax1.get_lines()[1].set_label(f'Buy & Hold (SR={bnh_sr:.3f}, AR={bnh_ar:.3f}, MDD={bnh_dd:.3f}, CR={bnh_cr:.3f})')
                except Exception as e:
                    logger.warning(f"計算 Buy & Hold 性能指標時出錯: {str(e)}")
                    
            # 填充背景顏色
            ax1.fill_between(self.portfolio_df.index, 0, self.portfolio_df['cumu'], 
                           alpha=0.1, color='blue')
                           
            # 標記每個季度的起始日期
            start_date = self.portfolio_df.index.min()
            end_date = self.portfolio_df.index.max()
            
            # 生成季度第一天的日期列表
            dates = [start_date]
            current_date = pd.Timestamp(start_date.year, ((start_date.month - 1) // 3 + 1) * 3 + 1, 1) # 下一個季度的第一天
            
            while current_date < end_date:
                dates.append(current_date)
                # 增加3個月
                if current_date.month >= 10:
                    current_date = pd.Timestamp(current_date.year + 1, 1, 1)
                else:
                    current_date = pd.Timestamp(current_date.year, current_date.month + 3, 1)
            
            # 添加最後一天
            dates.append(end_date)
            
            # 標記每個季度的起始日期為垂直線
            for date in dates[1:-1]:  # 排除第一個和最後一個
                ax1.axvline(x=date, color='black', linestyle='--', alpha=0.5)
                
            # 添加網格線
            ax1.grid(True, which='both', linestyle='--', alpha=0.3)
            
            # 下方圖 - 使用堆疊的柱狀圖顯示策略權重
            if not hasattr(self, 'portfolio_weights') or not self.portfolio_weights:
                logger.warning("無組合權重數據，跳過繪製權重圖")
                ax2.text(0.5, 0.5, 'No portfolio weight data available', 
                       ha='center', va='center', transform=ax2.transAxes)
            else:
                try:
                    # 計算每個日期的倉位
                    pos_series = None
                    if 'pos' in self.portfolio_df.columns:
                        pos_series = self.portfolio_df['pos']  # 直接使用預先計算好的倉位
                    else:
                        # 確保每個策略的 DataFrame 都有相同的索引
                        pos_values = {date: 0.0 for date in self.portfolio_df.index}
                        for name, weight in self.portfolio_weights.items():
                            if name in self.strategy_dfs:
                                strategy_df = self.strategy_dfs[name]
                                if 'pos' in strategy_df.columns:
                                    # 確保策略 DataFrame 的索引是日期
                                    if not isinstance(strategy_df.index, pd.DatetimeIndex):
                                        if 't' in strategy_df.columns:
                                            strategy_df = strategy_df.set_index('t')
                                        else:
                                            try:
                                                strategy_df.index = pd.to_datetime(strategy_df.index)
                                            except Exception as e:
                                                logger.warning(f"無法將策略 {name} 的索引轉換為日期，跳過: {str(e)}")
                                                continue
                                                
                                    for date in pos_values.keys():
                                        if date in strategy_df.index:
                                            pos_values[date] += strategy_df.loc[date, 'pos'] * weight
                        
                        pos_series = pd.Series(pos_values)
                        
                    if pos_series is not None:
                        # 繪製倉位 (線條)
                        ax2.plot(pos_series.index, pos_series, label='Position', color='green', linewidth=1.5)
                        # 填充背景顏色
                        ax2.fill_between(pos_series.index, 0, pos_series, alpha=0.1, color='green', where=(pos_series >= 0))
                        ax2.fill_between(pos_series.index, 0, pos_series, alpha=0.1, color='red', where=(pos_series < 0))
                    else:
                        logger.warning("無倉位數據，跳過繪製倉位")
                        ax2.text(0.5, 0.5, 'No position data available', 
                               ha='center', va='center', transform=ax2.transAxes)
                except Exception as e:
                    logger.error(f"繪製倉位圖時出錯: {str(e)}")
                    logger.error(traceback.format_exc())
            
            # 共享 x 軸
            ax2.sharex(ax1)
            
            # 設置 x 軸標籤格式
            ax1.xaxis.set_major_locator(mdates.MonthLocator())
            ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            plt.gcf().autofmt_xdate() # 自動調整日期標籤，避免重疊
            
            # 添加網格線
            ax2.grid(True, which='both', linestyle='--', alpha=0.3)
            
            # 設置軸標籤
            ax1.set_ylabel('Cumulative Return', fontsize=10)
            ax2.set_ylabel('Position', fontsize=10)
            ax2.set_xlabel('Date', fontsize=10)
            
            # 設置標題
            if hasattr(self, 'portfolio_metrics') and self.portfolio_metrics:
                sr = self.portfolio_metrics.get('sr', np.nan)
                ar = self.portfolio_metrics.get('ar', np.nan)
                mdd = self.portfolio_metrics.get('mdd', np.nan)
                cr = self.portfolio_metrics.get('cr', np.nan)
                weighting_method = self.portfolio_metrics.get('weighting_method', '').capitalize()
                
                # 更新主圖中的 Portfolio 標籤，加入性能指標
                ax1.get_lines()[0].set_label(f'Portfolio (SR={sr:.3f}, AR={ar:.3f}, MDD={mdd:.3f}, CR={cr:.3f})')
                
                # 獲取資產信息 - 支持多資產
                if hasattr(self, 'assets') and self.assets:
                    assets_list = self.assets
                    asset_symbol_display = ", ".join(assets_list)
                else:
                    # 舊版相容 - 僅使用單一資產
                    asset_symbol = self.asset_info.get('glassnode_symbol', 'Unknown') if hasattr(self, 'asset_info') else "Unknown"
                    asset_symbol_display = asset_symbol
                
                # 添加標題
                n_strategies = len(self.portfolio_weights)
                plt.suptitle(f'Portfolio Performance - {asset_symbol_display} - {n_strategies} Strategies ({weighting_method} Weighting)', 
                          fontsize=16, fontweight='bold')
                
                # 計算期間信息並添加到主圖標題
                start_str = self.portfolio_df.index[0].strftime('%Y-%m-%d')
                end_str = self.portfolio_df.index[-1].strftime('%Y-%m-%d')
                n_days = (self.portfolio_df.index[-1] - self.portfolio_df.index[0]).days
                
                period_title = f'Period: {start_str} to {end_str} ({n_days} days)'
                ax1.set_title(period_title, fontsize=10)
            
            # 添加圖例
            ax1.legend(loc='upper left', fontsize=9)
            ax2.legend(loc='upper left', fontsize=9)
            
            # 調整佈局
            plt.tight_layout()
            
            # 為超圖標題留出空間
            plt.subplots_adjust(top=0.9)
            
            # 保存圖表
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"已保存組合 equity curve 到 {save_path}")
            
            return save_path
        except Exception as e:
            logger.error(f"繪製組合 equity curve 時出錯: {str(e)}")
            logger.error(traceback.format_exc())
            return None

def plot_weight_distribution(self, save_path=None):
        """
        繪製權重分佈柱狀圖
        
        參數:
            save_path (str): 保存路徑，如果為 None，使用默認路徑
        """
        if not hasattr(self, 'portfolio_weights') or not self.portfolio_weights:
            logger.error("無可用 portfolio_weights，無法繪製權重分佈")
            return None
            
        if save_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            weighting_method = self.portfolio_metrics['weighting_method'] if hasattr(self, 'portfolio_metrics') else "equal"
            save_path = os.path.join(self.output_dir, f"portfolio_weights_{weighting_method}_{timestamp}.png")
            
        try:
            # 確保圖表開始前關閉所有現有圖形
            plt.close('all')
            
            # 設置字體和樣式
            plt.rcParams.update({
                'font.family': 'DejaVu Sans',
                'font.size': 10,
                'axes.grid': True,
                'grid.alpha': 0.3,
                'axes.labelsize': 10
            })
            
            # 獲取策略名稱和權重
            names = list(self.portfolio_weights.keys())
            weights = list(self.portfolio_weights.values())
            
            # 確保已排序
            sorted_data = sorted(zip(names, weights), key=lambda x: x[1], reverse=True)
            names, weights = zip(*sorted_data) if sorted_data else ([], [])
            
            # 獲取每個策略對應的資產
            strategy_assets = {}
            for name in names:
                for strategy in self.strategies:
                    if strategy.get('name') == name:
                        strategy_assets[name] = strategy.get('symbol', 'Unknown').upper()
                        break
                if name not in strategy_assets:
                    strategy_assets[name] = 'Unknown'
            
            # 根據資產對策略分組
            assets_strategies = {}
            for name, asset in strategy_assets.items():
                if asset not in assets_strategies:
                    assets_strategies[asset] = []
                assets_strategies[asset].append(name)
            
            # 準備多個子圖: 一個總覽圖 + 每個資產一個圖
            asset_list = sorted(assets_strategies.keys())
            n_assets = len(asset_list)
            
            if n_assets <= 1:
                # 如果只有一種資產，只顯示一個圖
                fig, ax = plt.subplots(figsize=(14, 8))
                
                # 設置條形圖
                bars = ax.bar(names, weights, color='blue', alpha=0.7)
                
                # 添加值標籤
                for bar in bars:
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                           f'{height:.4f}', ha='center', va='bottom', rotation=90, fontsize=9)
                
                # 設置標題和標籤
                weighting_method = self.portfolio_metrics.get('weighting_method', '').capitalize() if hasattr(self, 'portfolio_metrics') else "Unknown"
                asset_symbol = asset_list[0] if asset_list else "Unknown"
                
                ax.set_title(f'Portfolio Weights - {asset_symbol} ({weighting_method} Weighting) - {len(names)} Strategies', fontsize=16)
                ax.set_ylabel('Weight', fontsize=12)
                ax.set_xlabel('Strategy', fontsize=12)
                
                # 旋轉並調整 x 軸標籤
                plt.setp(ax.get_xticklabels(), rotation=45, ha='right', rotation_mode='anchor', fontsize=10)
                
                # 調整佈局
                plt.grid(axis='y', linestyle='--', alpha=0.7)
                plt.tight_layout()
            else:
                # 如果有多種資產，創建縱向子圖
                # 創建一個主圖 (總覽) 和每個資產一個子圖
                fig, axs = plt.subplots(n_assets+1, 1, figsize=(14, 4+n_assets*4), squeeze=False)
                
                # 總覽圖 - 顯示所有策略的權重，按資產上色
                ax_overview = axs[0, 0]
                
                # 為每個資產準備不同的顏色
                cmap = plt.get_cmap('tab10')
                colors = {asset: cmap(i % 10) for i, asset in enumerate(asset_list)}
                
                # 用顏色列表繪製條形圖
                bar_colors = [colors[strategy_assets[name]] for name in names]
                bars = ax_overview.bar(names, weights, color=bar_colors, alpha=0.7)
                
                # 添加值標籤
                for bar in bars:
                    height = bar.get_height()
                    ax_overview.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                                   f'{height:.4f}', ha='center', va='bottom', rotation=90, fontsize=9)
                
                # 設置總覽圖標題和標籤
                weighting_method = self.portfolio_metrics.get('weighting_method', '').capitalize() if hasattr(self, 'portfolio_metrics') else "Unknown"
                asset_display = ", ".join(asset_list)
                
                ax_overview.set_title(f'Portfolio Weights - {asset_display} ({weighting_method} Weighting) - {len(names)} Strategies', fontsize=16)
                ax_overview.set_ylabel('Weight', fontsize=12)
                
                # 為總覽圖添加圖例
                legend_elements = [plt.Rectangle((0,0),1,1, color=colors[asset], alpha=0.7) for asset in asset_list]
                ax_overview.legend(legend_elements, asset_list, loc='upper right')
                
                # 旋轉並調整總覽圖的 x 軸標籤
                plt.setp(ax_overview.get_xticklabels(), rotation=45, ha='right', rotation_mode='anchor', fontsize=10)
                ax_overview.grid(axis='y', linestyle='--', alpha=0.7)
                
                # 為每個資產創建子圖
                for i, asset in enumerate(asset_list):
                    ax = axs[i+1, 0]
                    
                    # 獲取該資產的策略及其權重
                    asset_names = assets_strategies[asset]
                    asset_weights = [self.portfolio_weights[name] for name in asset_names]
                    
                    # 排序
                    sorted_data = sorted(zip(asset_names, asset_weights), key=lambda x: x[1], reverse=True)
                    asset_names, asset_weights = zip(*sorted_data) if sorted_data else ([], [])
                    
                    # 設置條形圖
                    bars = ax.bar(asset_names, asset_weights, color=colors[asset], alpha=0.7)
                    
                    # 添加值標籤
                    for bar in bars:
                        height = bar.get_height()
                        ax.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                               f'{height:.4f}', ha='center', va='bottom', rotation=90, fontsize=9)
                    
                    # 設置子圖標題和標籤
                    ax.set_title(f'{asset} Strategies ({len(asset_names)})', fontsize=14)
                    ax.set_ylabel('Weight', fontsize=12)
                    
                    # 如果是最後一個子圖，添加 x 軸標籤
                    if i == n_assets-1:
                        ax.set_xlabel('Strategy', fontsize=12)
                    
                    # 旋轉並調整 x 軸標籤
                    plt.setp(ax.get_xticklabels(), rotation=45, ha='right', rotation_mode='anchor', fontsize=10)
                    ax.grid(axis='y', linestyle='--', alpha=0.7)
            
            # 調整佈局
            plt.tight_layout()
            
            # 保存圖表
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"已保存權重分佈圖到 {save_path}")
            
            return save_path
        except Exception as e:
            logger.error(f"繪製權重分佈圖時出錯: {str(e)}")
            logger.error(traceback.format_exc())
            return None

def generate_nofilter_report(self, report_dir):
    """生成無過濾的回測報告"""
    if not self.strategy_dfs or self.portfolio_df is None:
        logger.error("無完整數據，請先運行 run_all_strategies() 和 generate_portfolio()")
        return

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Ensure the directory exists
    os.makedirs(report_dir, exist_ok=True)
    logger.info(f"生成無過濾回測報告到 {report_dir}")

    # 設置日誌保存到報告目錄
    setup_report_logger(report_dir)

    # 創建報告專用的data目錄
    report_data_dir = os.path.join(report_dir, 'data')
    os.makedirs(report_data_dir, exist_ok=True)
    logger.info(f"已創建無過濾報告專用的data目錄: {report_data_dir}")

    # 1. 繪製組合 equity curve - 無過濾版
    portfolio_path = os.path.join(report_dir, 'portfolio_equity_curve_nofilter.png')
    self.plot_portfolio(save_path=portfolio_path)

    # 2. 繪製權重分佈圖 - 無過濾版
    weights_path = os.path.join(report_dir, 'portfolio_weights_nofilter.png')
    self.plot_weight_distribution(save_path=weights_path)

    # 3. 繪製最近30天的組合表現 - 無過濾版
    recent_portfolio_path = os.path.join(report_dir, 'portfolio_recent_30days_nofilter.png')
    self.plot_recent_portfolio(days=30, save_path=recent_portfolio_path)

    # 保存策略 DataFrame 到報告目錄的 data 文件夾
    current_results = self.results_summary if hasattr(self, 'results_summary') else self.results
    if not hasattr(self, 'portfolio_weights'):
         logger.warning("無權重數據，無法保存帶權重的策略 CSV 文件")
    else:
         for name, result in current_results.items():
            if name in self.portfolio_weights:  # 只處理有權重的策略
                # 獲取策略資訊
                if name not in self.strategy_dfs:
                    logger.warning(f"找不到策略 {name} 的 DataFrame，無法保存 CSV")
                    continue
                df = self.strategy_dfs[name].copy()
                resolution = result.get('resolution', '24h')
                weight = self.portfolio_weights.get(name, 0)

                # 確保 DataFrame 包含必要的列
                if 'chg' not in df.columns and 'percentage_change' in df.columns:
                    df['chg'] = df['percentage_change']

                # 重設索引以便 CSV 輸出包含時間列
                df_to_save = df.copy()
                if isinstance(df_to_save.index, pd.DatetimeIndex):
                    df_to_save = df_to_save.reset_index()

                # 將時間列名統一為 't'
                if 'index' in df_to_save.columns and 't' not in df_to_save.columns:
                    df_to_save = df_to_save.rename(columns={'index': 't'})
                elif df_to_save.index.name == 't':
                     df_to_save = df_to_save.reset_index()

                # 使用適當的命名約定，包含時間框架信息
                csv_file = os.path.join(report_data_dir, f"{name.replace('/', '_')}_{resolution}_w{weight:.4f}.csv")

                # 只保存必要的列，減少文件大小
                columns_to_save = ['t', 'pnl', 'pos', 'chg']
                available_columns = [col for col in columns_to_save if col in df_to_save.columns]
                if not available_columns:
                     logger.warning(f"策略 {name} 沒有可保存的列，跳過")
                     continue
                df_final_save = df_to_save[available_columns]

                # 保存 CSV 文件
                df_final_save.to_csv(csv_file, index=False)
                logger.info(f"保存策略 {name} ({resolution}) 數據到 {csv_file}")

    # 4. 生成策略性能指標表 - 無過濾版
    strategies_metrics = []
    for name, result in current_results.items():
        strategies_metrics.append({
            'Strategy': name,
            'Sharpe Ratio': result.get('sr', np.nan),
            'Annual Return': result.get('ar', np.nan),
            'Max Drawdown': result.get('mdd', np.nan),
            'Calmar Ratio': result.get('cr', np.nan),
            'Trades': result.get('trades', np.nan),
            'Trades/Year': result.get('trades_per_year', np.nan)
        })

    strategies_df = pd.DataFrame(strategies_metrics)
    strategies_df = strategies_df.sort_values('Sharpe Ratio', ascending=False)

    # 添加組合行
    if hasattr(self, 'portfolio_metrics') and self.portfolio_metrics:
        portfolio_row = pd.DataFrame([{
            'Strategy': 'PORTFOLIO (NO FILTER)',
            'Sharpe Ratio': self.portfolio_metrics.get('sr', np.nan),
            'Annual Return': self.portfolio_metrics.get('ar', np.nan),
            'Max Drawdown': self.portfolio_metrics.get('mdd', np.nan),
            'Calmar Ratio': self.portfolio_metrics.get('cr', np.nan),
            'Trades': np.nan,
            'Trades/Year': np.nan
        }])
        strategies_df = pd.concat([portfolio_row, strategies_df], ignore_index=True)
    else:
        logger.warning("無組合指標數據，無法添加到指標表")

    # 保存指標表
    metrics_path = os.path.join(report_dir, 'strategies_metrics_nofilter.csv')
    strategies_df.to_csv(metrics_path, index=False)

    # 5. 生成描述性HTML報告 - 無過濾版
    html_path = os.path.join(report_dir, 'report_nofilter.html')

    weighting_method = self.portfolio_metrics.get('weighting_method', 'N/A').capitalize() if hasattr(self, 'portfolio_metrics') else "N/A"
    
    # 獲取資產信息 - 支持多資產
    if hasattr(self, 'assets') and self.assets:
        assets_list = self.assets
        asset_symbol_display = ", ".join(assets_list)
    else:
        # 舊版相容 - 僅使用單一資產
        asset_symbol = self.asset_info.get('glassnode_symbol', 'Unknown') if hasattr(self, 'asset_info') else "Unknown"
        assets_list = [asset_symbol]
        asset_symbol_display = asset_symbol

    # 處理日期格式化
    start_date = "N/A"
    end_date = "N/A"
    if self.portfolio_df is not None and not self.portfolio_df.empty:
        try:
            if isinstance(self.portfolio_df.index[0], (pd.Timestamp, datetime)):
                start_date = self.portfolio_df.index[0].strftime('%Y-%m-%d')
                end_date = self.portfolio_df.index[-1].strftime('%Y-%m-%d')
            else:
                start_date = str(self.portfolio_df.index[0])
                end_date = str(self.portfolio_df.index[-1])
        except Exception as e:
            logger.warning(f"格式化日期時出錯: {str(e)}")
            start_date = "起始日期"
            end_date = "結束日期"

    # 分析每個資產的策略分佈
    asset_strategy_counts = {}
    for strategy in self.strategies:
        asset = strategy.get('symbol', 'Unknown').upper()
        if asset not in asset_strategy_counts:
            asset_strategy_counts[asset] = 0
        asset_strategy_counts[asset] += 1

    # 計算有多少策略被包含在投資組合中
    included_strategies_count = len(self.portfolio_weights) if hasattr(self, 'portfolio_weights') else 0
    total_strategies_count = len(self.strategies)
    
    # 計算使用率百分比
    usage_percentage = (included_strategies_count / total_strategies_count * 100) if total_strategies_count > 0 else 0

    html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>No-Filter Portfolio Backtest Report - {asset_symbol_display}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        h1, h2, h3 {{ color: #333; }}
        table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        tr:nth-child(even) {{ background-color: #f9f9f9; }}
        .summary {{ background-color: #fff2e6; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
        .nofilter-banner {{ background-color: #FFEB3B; color: #333; padding: 10px; text-align: center; margin-bottom: 20px; font-weight: bold; }}
        .image {{ text-align: center; margin: 20px 0; }}
        .image img {{ max-width: 90%; height: auto; border: 1px solid #ccc; }}
        .comparison {{ display: flex; justify-content: space-between; margin-bottom: 20px; }}
        .comparison-col {{ flex: 1; padding: 10px; }}
    </style>
</head>
<body>
    <div class="nofilter-banner">
        NO FILTER REPORT - All Strategies Included Without Filtering
    </div>
    
    <h1>No-Filter Portfolio Backtest Report - {asset_symbol_display}</h1>
    
    <div class="summary">
        <h2>Summary</h2>
        <p>
            <strong>Assets:</strong> {asset_symbol_display}<br>
            <strong>Total Strategies:</strong> {total_strategies_count}<br>
            <strong>Strategies Used in Portfolio:</strong> {included_strategies_count} ({usage_percentage:.1f}%)<br>
            <strong>Weighting Method:</strong> {weighting_method}<br>
            <strong>Date Range:</strong> {start_date} to {end_date}<br>
            <strong>Report Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br>
            <strong>Filtering:</strong> <span style="color:red;font-weight:bold;">NONE - All strategies included</span>
        </p>
    </div>

    <h2>Assets Distribution</h2>
    <table>
        <tr><th>Asset</th><th>Number of Strategies</th></tr>
"""

    # 添加每個資產的策略數量
    for asset, count in asset_strategy_counts.items():
        html_content += f"""        <tr><td>{asset}</td><td>{count}</td></tr>
"""

    html_content += """    </table>

    <h2>Portfolio Performance</h2>
    """
    if hasattr(self, 'portfolio_metrics') and self.portfolio_metrics:
        html_content += f"""    <table>
        <tr><th>Metric</th><th>Value</th></tr>
        <tr><td>Sharpe Ratio</td><td>{self.portfolio_metrics.get('sr', np.nan):.4f}</td></tr>
        <tr><td>Annual Return</td><td>{self.portfolio_metrics.get('ar', np.nan):.4f}</td></tr>
        <tr><td>Max Drawdown</td><td>{self.portfolio_metrics.get('mdd', np.nan):.4f}</td></tr>
        <tr><td>Calmar Ratio</td><td>{self.portfolio_metrics.get('cr', np.nan):.4f}</td></tr>
    </table>
"""
    else:
        html_content += "<p>Portfolio metrics not available.</p>"

    html_content += """    <div class="image">
        <h2>Portfolio Equity Curve (No Filter)</h2>
        <img src="portfolio_equity_curve_nofilter.png" alt="Portfolio Equity Curve">
    </div>

    <div class="image">
        <h2>Recent 30 Days Performance (No Filter)</h2>
        <img src="portfolio_recent_30days_nofilter.png" alt="Recent 30 Days Performance">
    </div>

    <div class="image">
        <h2>Portfolio Weight Distribution (No Filter)</h2>
        <img src="portfolio_weights_nofilter.png" alt="Portfolio Weights">
    </div>

    <h2>Strategy Performance Metrics (No Filter)</h2>
    <table>
        <tr>
            <th>Strategy</th>
            <th>Asset</th>
            <th>Weight</th>
            <th>Sharpe Ratio</th>
            <th>Annual Return</th>
            <th>Max Drawdown</th>
            <th>Calmar Ratio</th>
            <th>Trades/Year</th>
        </tr>
"""

    # 添加組合行
    if hasattr(self, 'portfolio_metrics') and self.portfolio_metrics:
        html_content += f"""        <tr>
            <td><strong>PORTFOLIO (NO FILTER)</strong></td>
            <td><strong>{asset_symbol_display}</strong></td>
            <td><strong>1.000</strong></td>
            <td><strong>{self.portfolio_metrics.get('sr', np.nan):.4f}</strong></td>
            <td><strong>{self.portfolio_metrics.get('ar', np.nan):.4f}</strong></td>
            <td><strong>{self.portfolio_metrics.get('mdd', np.nan):.4f}</strong></td>
            <td><strong>{self.portfolio_metrics.get('cr', np.nan):.4f}</strong></td>
            <td>-</td>
        </tr>
"""

    # 添加每個策略的行，並包含資產信息
    current_results = self.results_summary if hasattr(self, 'results_summary') else self.results
    if hasattr(self, 'portfolio_weights'):
         # 創建策略資產映射字典
         strategy_assets = {}
         for name in self.portfolio_weights.keys():
             asset = "Unknown"
             for strategy in self.strategies:
                 if strategy.get('name') == name:
                     asset = strategy.get('symbol', 'Unknown').upper()
                     break
             strategy_assets[name] = asset
             
         for name in self.portfolio_weights:
            if name in current_results:
                result = current_results[name]
                weight = self.portfolio_weights.get(name, 0)
                
                # 查找策略對應的資產
                asset = strategy_assets.get(name, "Unknown")
                
                html_content += f"""        <tr>
            <td>{name}</td>
            <td>{asset}</td>
            <td>{weight:.4f}</td>
            <td>{result.get('sr', np.nan):.4f}</td>
            <td>{result.get('ar', np.nan):.4f}</td>
            <td>{result.get('mdd', np.nan):.4f}</td>
            <td>{result.get('cr', np.nan):.4f}</td>
            <td>{result.get('trades_per_year', np.nan):.1f}</td>
        </tr>
"""

    html_content += """    </table>
    
    <h2>Comparison with Filtered Portfolio</h2>
    <p>This report shows portfolio performance <strong>without any filtering</strong>. Compare with the filtered portfolio report to see the impact of strategy filtering on performance.</p>

    <h2>Notes</h2>
    <ul>
        <li>All strategies are included in this portfolio without any filtering, to provide a baseline for comparison.</li>
        <li>Transaction cost: {TRANSACTION_COST*100:.2f}% per trade</li>
        <li>Long Leverage: {LONG_LEVERAGE:.2f}x, Short Leverage: {SHORT_LEVERAGE:.2f}x</li>
        <li>Metrics calculations assume daily data where applicable.</li>
    </ul>
</body>
</html>
"""

    with open(html_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

    logger.info(f"已生成無過濾HTML報告: {html_path}")
    logger.info(f"無過濾報告目錄: {report_dir}")

    return report_dir

# --- Monkey-patch the methods to the PortfolioBacktest class --- 
PortfolioBacktest.generate_report = generate_report
PortfolioBacktest.calculate_strategy_metrics = calculate_strategy_metrics
PortfolioBacktest.filter_strategies_by_rolling_sharpe = filter_strategies_by_rolling_sharpe
PortfolioBacktest.plot_recent_portfolio = plot_recent_portfolio
PortfolioBacktest.generate_filtered_strategies_excel = generate_filtered_strategies_excel
PortfolioBacktest.plot_portfolio = plot_portfolio
PortfolioBacktest.plot_weight_distribution = plot_weight_distribution
PortfolioBacktest.generate_nofilter_report = generate_nofilter_report

# End of portfolio_analyzer.py 