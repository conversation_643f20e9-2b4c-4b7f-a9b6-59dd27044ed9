# Imports from other modules and standard libraries
from portfolio_config import (
    logger, RESULTS_DIR, DEFAULT_TRAIN_RATIO, TRANSACTION_COST,
    LONG_LEVERAGE, SHORT_LEVERAGE, CURRENT_REPORT_DIR,
    GlassnodeCache, backtesting, setup_report_logger, load_or_create_config, DEFAULT_CONFIG
)
from portfolio_backtester import PortfolioBacktest
# Import the analyzer module to ensure methods are patched 
import portfolio_analyzer

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import time
import logging
import multiprocessing
import argparse
import json # Import json for config loading
import shutil # Import shutil for directory operations
import sys # Import sys module
import traceback # Import traceback module
import inspect

def main():
    """主函數"""
    # 解析命令行參數
    parser = argparse.ArgumentParser(description='Portfolio Backtest 工具')
    parser.add_argument('--config', type=str, default='config_summary.xlsx', # Default to the excel config
                        help='配置文件路徑 (預設: config_summary.xlsx)')
    parser.add_argument('--output-dir', type=str, default=None,
                        help='輸出目錄 (預設: portfolio_results/report_TIMESTAMP)')
    parser.add_argument('--weight-method', type=str, default=None,
                        help='權重方法: equal, sharpe, calmar, sortino (預設: sharpe)')
    parser.add_argument('--min-sr', type=float, default=None,
                        help='組合策略最小 Sharpe Ratio 閾值 (預設: 0.0)')
    parser.add_argument('--rolling-sharpe-filter', action=argparse.BooleanOptionalAction, default=None,
                        help='是否使用 rolling sharpe 篩選策略 (預設: True)')
    parser.add_argument('--min-start-date', type=str, default=None,
                        help='策略數據最小開始日期 (格式: YYYY-MM-DD, 預設: 2021-01-01)')
    parser.add_argument('--long-leverage', type=float, default=None,
                        help=f'長倉槓桿倍數 (預設: {DEFAULT_CONFIG["long_leverage"]})')
    parser.add_argument('--short-leverage', type=float, default=None,
                        help=f'短倉槓桿倍數 (預設: {DEFAULT_CONFIG["short_leverage"]})')
    parser.add_argument('--generate-individual-curves', action=argparse.BooleanOptionalAction, default=None,
                        help='是否生成單一策略 equity curve (預設: False)')
    parser.add_argument('--generate-strategy-heatmaps', action=argparse.BooleanOptionalAction, default=None,
                        help='是否為每個策略生成熱力圖 (預設: False)')
    parser.add_argument('--use-multiprocessing', action=argparse.BooleanOptionalAction, default=None,
                        help=f'是否使用多進程 (預設: {DEFAULT_CONFIG["use_multiprocessing"]})')
    parser.add_argument('--max-workers', type=int, default=None,
                        help=f'最大進程數 (預設: {DEFAULT_CONFIG["max_workers"]})')
    parser.add_argument('--threshold-avg-sr', type=float, default=None,
                        help='平均滾動夏普比率最低要求 (預設: 1.5)')
    parser.add_argument('--threshold-recent-sr', type=float, default=None,
                        help='近期滾動夏普比率最低要求 (預設: 1.5)')
    parser.add_argument('--recent-period-months', type=int, default=None,
                        help='定義"近期"嘅月數 (預設: 6)')
    parser.add_argument('--asset', type=str, default=None,
                        help='指定處理特定資產，例如 BTC, ETH (預設: 處理全部)')
    parser.add_argument('--generate-nofilter-report', action=argparse.BooleanOptionalAction, default=False,
                        help='是否生成無過濾的報告，包含所有策略，不進行任何篩選 (預設: False)')

    args = parser.parse_args()

    # --- 配置加載與合併 --- 
    # 1. 加載 JSON 配置文件 (backtest_config.json) 的設置
    json_config = load_or_create_config()

    # 2. 創建一個基於 JSON 配置的最終配置字典
    config = json_config.copy()

    # 3. 用命令行參數覆蓋 JSON 配置 (如果提供了命令行參數)
    # This allows command-line args to have the highest priority
    # Need to check for None because argparse defaults might be False/0 etc.
    if args.weight_method is not None: config['weight_method'] = args.weight_method
    if args.min_sr is not None: config['min_sr'] = args.min_sr
    if args.rolling_sharpe_filter is not None: config['rolling_sharpe_filter'] = args.rolling_sharpe_filter
    if args.min_start_date is not None: config['min_start_date'] = args.min_start_date
    if args.long_leverage is not None: config['long_leverage'] = args.long_leverage
    if args.short_leverage is not None: config['short_leverage'] = args.short_leverage
    if args.generate_individual_curves is not None: config['generate_individual_curves'] = args.generate_individual_curves
    if args.generate_strategy_heatmaps is not None: config['generate_strategy_heatmaps'] = args.generate_strategy_heatmaps
    if args.use_multiprocessing is not None: config['use_multiprocessing'] = args.use_multiprocessing
    if args.max_workers is not None: config['max_workers'] = args.max_workers
    if args.asset is not None: config['asset'] = args.asset
    # 處理閾值參數
    if args.threshold_avg_sr is not None: config['threshold_avg_sr'] = args.threshold_avg_sr
    if args.threshold_recent_sr is not None: config['threshold_recent_sr'] = args.threshold_recent_sr
    if args.recent_period_months is not None: config['recent_period_months'] = args.recent_period_months

    # --- 報告目錄與日誌設置 --- 
    # 建立當前時間報告目錄 (在主 RESULTS_DIR 下)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    # Allow overriding the entire report directory path via command line
    if args.output_dir:
         report_dir = args.output_dir
         # If the provided output_dir exists and is not empty, maybe warn or clear?
         # For now, just use it.
         os.makedirs(report_dir, exist_ok=True)
    else:
         report_dir = os.path.join(RESULTS_DIR, f"report_{timestamp}")
         os.makedirs(report_dir, exist_ok=True)

    # 初始化報告目錄結構
    logs_dir = os.path.join(report_dir, 'logs')
    data_dir = os.path.join(report_dir, 'data') # For CSV outputs
    metrics_dir = os.path.join(report_dir, 'metrics') # For metrics CSVs
    equity_curves_dir = os.path.join(report_dir, 'equity_curves') # For individual curves
    os.makedirs(logs_dir, exist_ok=True)
    os.makedirs(data_dir, exist_ok=True)
    os.makedirs(metrics_dir, exist_ok=True)
    # Only create curves dir if needed later

    # 設置全局報告目錄變量並配置logger
    report_log_file = setup_report_logger(report_dir)

    # --- 全局變量與參數設置 --- 
    # 更新全局槓桿倍數 (在 portfolio_config 中定義)
    # Need to import and modify the globals in portfolio_config if they are intended to be dynamic
    # This approach is generally discouraged. Better pass leverage as args to methods.
    # For now, assume LONG_LEVERAGE and SHORT_LEVERAGE in portfolio_config are updated
    # by external means or are constants. If they need to be set here:
    # import portfolio_config
    # portfolio_config.LONG_LEVERAGE = config.get('long_leverage', portfolio_config.LONG_LEVERAGE)
    # portfolio_config.SHORT_LEVERAGE = config.get('short_leverage', portfolio_config.SHORT_LEVERAGE)
    # Or pass them to PortfolioBacktest init if the class uses them internally.
    # Let's assume PortfolioBacktest uses the values passed during initialization if needed,
    # or relies on the constants defined in portfolio_config. The original code uses global vars.
    # We will log the values used from the final config. 
    long_leverage = config.get('long_leverage', LONG_LEVERAGE) # Get final value
    short_leverage = config.get('short_leverage', SHORT_LEVERAGE) # Get final value

    logger.info(f"--- Portfolio Backtest 開始 ---")
    logger.info(f"日誌文件: {report_log_file}")
    logger.info(f"報告目錄: {report_dir}")
    logger.info(f"--- 主要參數 ---")
    logger.info(f"  權重方法: {config.get('weight_method')}")
    logger.info(f"  組合最小 SR: {config.get('min_sr')}")
    logger.info(f"  使用篩選策略 (Rolling SR): {config.get('rolling_sharpe_filter')}")
    logger.info(f"  數據最小開始日期: {config.get('min_start_date')}")
    logger.info(f"  長倉槓桿: {long_leverage}")
    logger.info(f"  短倉槓桿: {short_leverage}")
    logger.info(f"  生成單策略圖表: {config.get('generate_individual_curves', False)}")
    logger.info(f"  生成策略熱力圖: {config.get('generate_strategy_heatmaps', False)}")
    logger.info(f"  指定處理資產: {config.get('asset', '全部')}")
    # 添加閾值參數日誌
    logger.info(f"--- 策略篩選閾值 ---")
    logger.info(f"  平均滾動夏普比率閾值: {config.get('threshold_avg_sr', 1.5)}")
    logger.info(f"  近期滾動夏普比率閾值: {config.get('threshold_recent_sr', 1.5)}")
    logger.info(f"  定義「近期」嘅月數: {config.get('recent_period_months', 6)}")
    logger.info(f"--- Shift 參數 (來自 backtesting 模塊) ---")
    try:
         logger.info(f"  24h: {backtesting.get_shift_periods('24h')}")
         logger.info(f"  1h: {backtesting.get_shift_periods('1h')}")
         logger.info(f"  10m: {backtesting.get_shift_periods('10m')}")
    except Exception as e:
         logger.warning(f"無法獲取 shift periods: {e}")
    logger.info(f"--- 緩存信息 ---")
    logger.info("使用 Glassnode 數據本地緩存功能，緩存目錄: data/glassnode_cache")
    logger.info("緩存有效期: 7 天")
    logger.info(f"-----------------------------")

    # --- 實例化與執行 --- 
    bt_instance = None # Initialize
    try:
        # 標準模式
        config_file_path = args.config # Use the config file specified in args
        logger.info(f"處理 Excel 文件 {config_file_path}")
        if not os.path.exists(config_file_path):
            logger.error(f"錯誤：找不到指定的配置文件 {config_file_path}")
            return None # Exit if config file not found

        bt_instance = PortfolioBacktest(
            config_file=config_file_path,
            output_dir=report_dir,
            use_multiprocessing=config.get('use_multiprocessing'),
            max_workers=config.get('max_workers'),
            generate_individual_curves=config.get('generate_individual_curves', False), # 從配置中讀取
            generate_strategy_heatmaps=config.get('generate_strategy_heatmaps', False), # 從配置中讀取
            min_start_date=config.get('min_start_date'),
            asset_filter=config.get('asset')  # 傳遞指定資產參數
        )

        # 1. Run strategies
        bt_instance.run_all_strategies()
        if not bt_instance.strategy_dfs:
            logger.error("未能成功運行任何策略，終止執行。")
            return bt_instance
             
        # 1.1 打印時間框架策略統計
        bt_instance.print_strategy_timeframes()
        
        # 1.2 計算策略指標，包括rolling sharpe用於篩選
        logger.info("計算策略指標...")
        metrics_dir = os.path.join(report_dir, 'metrics')
        bt_instance.calculate_strategy_metrics(metrics_dir=metrics_dir)
        logger.info("策略指標計算完成")
        
        # 1.3 若啟用，生成每個策略的熱力圖
        if config.get('generate_strategy_heatmaps', False):
            logger.info("開始為每個策略生成熱力圖...")
            
            # 創建 heatmap 目錄
            heatmap_dir = os.path.join(report_dir, 'heatmap')
            os.makedirs(heatmap_dir, exist_ok=True)
            
            # 修改實例的 output_dir 屬性以確保熱力圖保存到正確位置
            bt_instance.output_dir = heatmap_dir
            
            # 使用單獨的函數來調用 generate_strategy_heatmaps 方法，避免與屬性衝突
            def run_heatmap_generation(instance):
                """執行熱力圖生成"""
                import types
                
                # 檢查 generate_strategy_heatmaps 是否是方法或函數
                if isinstance(instance.generate_strategy_heatmaps, (types.MethodType, types.FunctionType)):
                    return instance.generate_strategy_heatmaps()
                else:
                    # 如果是布爾值屬性，則手動調用類方法
                    from portfolio_backtester import PortfolioBacktest
                    # 保存原來的屬性值
                    orig_value = instance.generate_strategy_heatmaps
                    # 臨時設置為 True 確保函數正常執行
                    instance.generate_strategy_heatmaps = True
                    # 調用未綁定的類方法
                    result = PortfolioBacktest.generate_strategy_heatmaps(instance)
                    # 恢復原始值
                    instance.generate_strategy_heatmaps = orig_value
                    return result
            
            try:
                heatmap_dir = run_heatmap_generation(bt_instance)
                logger.info(f"熱力圖生成完成，保存在: {heatmap_dir}")
            except Exception as e:
                logger.error(f"生成熱力圖時出錯: {str(e)}")
                logger.error(traceback.format_exc())

        # 2. Generate portfolio using standard method if not finding optimal N
        bt_instance.generate_portfolio(
            weighting_method=config.get('weight_method'),
            min_sr=float(config.get('min_sr')),
            rolling_sharpe_filter=config.get('rolling_sharpe_filter')
        )

        # 3. Generate report
        if bt_instance.portfolio_df is not None:
             report_result_dir = bt_instance.generate_report()
        else:
             logger.error("無法生成投資組合，跳過生成報告步驟。")

        # 4. Generate Excel with all strategies (optional, could be large)
        all_strategies_excel_path = os.path.join(report_dir, f"all_strategies_{timestamp}.xlsx")
        try:
            all_strategies_data = []
            current_results = bt_instance.results_summary if hasattr(bt_instance, 'results_summary') else bt_instance.results
            for strategy in bt_instance.strategies:
                name = strategy['name']
                perf_metrics = current_results.get(name, {})
                
                # 標準化處理 model 和 type，轉為全小寫並用底線連接
                model = strategy.get('model', '')
                strategy_type = strategy.get('type', '')
                
                # 將 model 標準化為全小寫，空格轉為底線
                if model:
                    model = model.lower().replace(' ', '_')
                
                # 將 type 標準化為全小寫，空格轉為底線
                if strategy_type:
                    strategy_type = strategy_type.lower().replace(' ', '_')
                
                # 按要求順序設置欄位，並使用與config_summary.xlsx一致的欄位名稱格式
                row = {
                    'API': strategy.get('api', ''),
                    'Metric Key': strategy.get('metric_key', ''),
                    'Resolution': strategy.get('resolution', ''),
                    'API Symbol': strategy.get('api_symbol', ''),
                    'Strategy Name': name,
                    'x': strategy.get('x', ''),
                    'y': strategy.get('y', ''),
                    'Asset': strategy.get('symbol', ''),
                    'Model': model,  # 使用標準化後的 model
                    'Style': strategy.get('style', ''),
                    'Type': strategy_type,  # 使用標準化後的 type
                    'Sharpe Ratio': perf_metrics.get('sr', np.nan),
                    'Annual Return': perf_metrics.get('ar', np.nan),
                    'Max Drawdown': perf_metrics.get('mdd', np.nan),
                    'Calmar Ratio': perf_metrics.get('cr', np.nan)
                }
                all_strategies_data.append(row)
            if all_strategies_data:
                # 創建 DataFrame
                all_df = pd.DataFrame(all_strategies_data)
                
                # 確保列的順序正確
                column_order = [
                    'API', 'Metric Key', 'Resolution', 'API Symbol', 'Strategy Name',
                    'x', 'y', 'Asset', 'Model', 'Style', 'Type',
                    'Sharpe Ratio', 'Annual Return', 'Max Drawdown', 'Calmar Ratio'
                ]
                
                # 篩選實際存在的列（防止某些列不存在）
                actual_columns = [col for col in column_order if col in all_df.columns]
                all_df = all_df[actual_columns]
                
                # Sort by Sharpe Ratio
                all_df = all_df.sort_values('Sharpe Ratio', ascending=False)
                
                # 使用ExcelWriter，以便能夠自動調整列寬
                with pd.ExcelWriter(all_strategies_excel_path, engine='openpyxl') as writer:
                    all_df.to_excel(writer, index=False, sheet_name='All Strategies')
                    
                    # 獲取工作表
                    worksheet = writer.sheets['All Strategies']
                    
                    # 自動調整所有列的寬度以適應內容
                    for idx, col in enumerate(all_df.columns):
                        # 找出該列中最長的內容
                        column_width = max(
                            all_df[col].astype(str).apply(len).max(),  # 數據中最長內容的長度
                            len(str(col)) + 2                          # 列名的長度加上一些額外空間
                        )
                        # 設置列寬 (字符數 * 1.0 剛好適合內容)
                        column_letter = worksheet.cell(row=1, column=idx+1).column_letter
                        worksheet.column_dimensions[column_letter].width = column_width * 1.0
                    
                    # 添加自動篩選功能
                    worksheet.auto_filter.ref = worksheet.dimensions
                
                logger.info(f"已將所有 {len(bt_instance.strategies)} 個策略保存到 {all_strategies_excel_path}")
                print(f"所有策略已保存到: {all_strategies_excel_path}")
        except Exception as e:
            logger.error(f"保存所有策略 Excel 文件時出錯: {str(e)}")
            logger.error(traceback.format_exc())

        # 5. Generate non-filtered report if requested
        if args.generate_nofilter_report:
            logger.info("===== 開始生成無過濾的報告 =====")
            # Create a separate directory for the non-filtered report
            nofilter_dir = os.path.join(report_dir, 'nofilter')
            os.makedirs(nofilter_dir, exist_ok=True)
            
            # Set up logging for the non-filtered report
            nofilter_log_file = setup_report_logger(nofilter_dir)
            logger.info(f"無過濾報告目錄: {nofilter_dir}")
            logger.info(f"無過濾報告日誌: {nofilter_log_file}")
            
            # Generate portfolio without filtering
            logger.info("生成無過濾的投資組合...")
            bt_instance.generate_portfolio(
                weighting_method=config.get('weight_method'),
                min_sr=0.0,  # Use 0.0 to include all strategies
                rolling_sharpe_filter=False  # Disable rolling sharpe filter
            )
            
            # Generate a special report for the non-filtered portfolio
            if bt_instance.portfolio_df is not None:
                logger.info("生成無過濾報告...")
                # Save the original metrics results
                original_metrics = bt_instance.metrics_results
                
                # Create a custom generate_report function for non-filtered report
                bt_instance.generate_nofilter_report(nofilter_dir)
                
                # Restore original metrics results
                bt_instance.metrics_results = original_metrics
                logger.info("無過濾報告生成完成")
            else:
                logger.error("無法生成無過濾的投資組合，跳過生成無過濾報告步驟。")
            
            # Restore the original report directory
            setup_report_logger(report_dir)
            logger.info("===== 無過濾報告生成完成 =====")

    except Exception as e:
         logger.critical(f"執行過程中發生嚴重錯誤: {str(e)}")
         logger.critical(traceback.format_exc())
    finally:
         logger.info(f"--- Portfolio Backtest 結束 ---")
         print(f"Portfolio Backtest 結束，報告目錄: {report_dir}")
         # Ensure all log handlers are closed
         for handler in logger.handlers[:]:
             if isinstance(handler, logging.FileHandler):
                 handler.close()
                 logger.removeHandler(handler)

    return bt_instance # Return the instance for potential further inspection


if __name__ == "__main__":
    # Ensure multiprocessing start method is appropriate for OS
    if sys.platform.startswith('win'):
        multiprocessing.freeze_support() # Needed for Windows executable
    # Set start method to 'spawn' for consistency across platforms (optional)
    # try:
    #     multiprocessing.set_start_method('spawn')
    # except RuntimeError:
    #     pass # Already set or not supported

    main_bt_instance = main()
    # You can potentially interact with main_bt_instance here if needed 