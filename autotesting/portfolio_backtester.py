# Imports from config and standard libraries
from portfolio_config import (
    logger, RESULTS_DIR, DEFAULT_TRAIN_RATIO, TRANSACTION_COST,
    LONG_LEVERAGE, SHORT_LEVERAGE, CURRENT_REPORT_DIR,
    GlassnodeCache, backtesting, setup_report_logger, load_or_create_config
)
# Direct import for models
try:
    from models import Strategy, get_base_params
except ImportError:
    logger.error("無法導入 'models' 模組。請確保 models.py 文件存在且包含 Strategy 和 get_base_params。")
    # Define placeholders if import fails to allow script structure loading
    class Strategy: pass
    def get_base_params(): return {}

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import requests
import time
import logging
from tqdm import tqdm
import sys
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed
import statsmodels.api as sm
from scipy import stats
import glob
import json
import traceback # Import traceback
import shutil # Import shutil for directory operations

class PortfolioBacktest:
    def __init__(self, config_file, output_dir=RESULTS_DIR, train_ratio=DEFAULT_TRAIN_RATIO, use_multiprocessing=True, max_workers=None, generate_individual_curves=False, generate_strategy_heatmaps=False, min_start_date=None, asset_filter=None):
        """
        初始化 Portfolio Backtest

        參數:
            config_file (str): Excel 配置文件路徑 (.xlsx)
            output_dir (str): 結果輸出目錄
            train_ratio (float): 訓練集比例
            use_multiprocessing (bool): 是否使用多進程
            max_workers (int): 最大進程數，None 表示使用 CPU 核心數
            generate_individual_curves (bool): 是否生成單一策略的 equity curve 圖
            generate_strategy_heatmaps (bool): 是否為每個策略生成熱力圖
            min_start_date (str or datetime): 策略數據的最小開始日期，格式為 'YYYY-MM-DD'，早於此日期才會被納入分析
            asset_filter (str): 過濾特定資產的策略，例如 'BTC', 'ETH'，None 表示處理所有資產
        """
        self.config_file = config_file
        self.output_dir = output_dir
        self.train_ratio = train_ratio
        self.config = None
        self.strategies = []
        self.asset_info = {}
        self.results = {}
        self.portfolio_df = None
        self.strategy_dfs = {}
        self.metrics_results = None # Initialize metrics_results
        self.portfolio_weights = {} # Initialize portfolio_weights
        self.portfolio_metrics = {} # Initialize portfolio_metrics
        self.asset_filter = asset_filter # 儲存資產過濾條件

        # 多進程設置
        self.use_multiprocessing = use_multiprocessing
        self.max_workers = max_workers if max_workers else min(multiprocessing.cpu_count(), 4)

        # 是否生成單一策略的 equity curve 圖
        self.generate_individual_curves = generate_individual_curves
        
        # 是否為每個策略生成熱力圖
        self.generate_strategy_heatmaps = generate_strategy_heatmaps

        # 設置數據最小開始日期
        if min_start_date:
            if isinstance(min_start_date, str):
                self.min_start_date = pd.to_datetime(min_start_date)
            else:
                self.min_start_date = min_start_date
            logger.info(f"設置策略數據最小開始日期: {self.min_start_date.strftime('%Y-%m-%d')}")
        else:
            self.min_start_date = None

        # 創建輸出目錄
        os.makedirs(output_dir, exist_ok=True)

        # 載入配置
        self.config = self._load_config(self.config_file)
        if self.config:
            self.strategies = self.config.get('STRATEGIES', [])
            
            # 處理資產過濾
            if self.asset_filter:
                original_count = len(self.strategies)
                self.strategies = [s for s in self.strategies if s.get('symbol', '').upper() == self.asset_filter.upper()]
                logger.info(f"資產過濾: 從 {original_count} 個策略中篩選 {len(self.strategies)} 個 {self.asset_filter.upper()} 策略")
            
            # 整理資產列表
            self.assets = self._get_assets_from_strategies()
            
            # 更新資產配置
            self._update_asset_configs()
            
            logger.info(f"已加載配置文件 {config_file}")
            logger.info(f"找到 {len(self.strategies)} 個策略配置")
            logger.info(f"發現 {len(self.assets)} 個不同的資產: {', '.join(self.assets)}")
        else:
            logger.error(f"無法加載配置文件 {config_file}")
            # Handle error appropriately, maybe raise an exception or set defaults
            self.strategies = []
            self.asset_info = {}
            self.assets = []

        if self.use_multiprocessing:
            logger.info(f"多進程模式已開啟")
        else:
            logger.info("多進程模式已關閉，使用單進程處理")
        logger.info(f"單一策略圖表生成: {'開啟' if self.generate_individual_curves else '關閉'}")
        logger.info(f"策略熱力圖生成: {'開啟' if self.generate_strategy_heatmaps else '關閉'}")

        # 清理過期緩存
        try:
            cache = GlassnodeCache()
            cache.clear_old_cache(days_to_keep=7)  # 保留最近7天的緩存
            logger.info("已檢查並清理過期緩存文件")
        except Exception as e:
            logger.warning(f"清理緩存失敗: {str(e)}")

    def _get_assets_from_strategies(self):
        """從策略配置中獲取所有不同的資產"""
        assets = set()
        for strategy in self.strategies:
            asset = strategy.get('symbol', '').upper()
            if asset:
                assets.add(asset)
        return sorted(list(assets))

    def _update_asset_configs(self):
        """為每個資產創建對應的設置"""
        self.asset_info = {}
        
        # 預設 BTC 設置作為基礎
        default_asset_info = {
            'bybit_symbol': 'BTCUSDT',
            'binance_symbol': 'BTCUSDT',
            'hyperliquid_symbol': 'BTC/USDC:USDC',
            'glassnode_symbol': 'BTC',
            'since': 1589126400  # 2020-05-10
        }
        
        # 為每個資產創建配置
        for asset in self.assets:
            if asset == 'BTC':
                self.asset_info['BTC'] = default_asset_info.copy()
            else:
                # 為其他資產創建設置
                self.asset_info[asset] = {
                    'bybit_symbol': f"{asset}USDT",
                    'binance_symbol': f"{asset}USDT",
                    'hyperliquid_symbol': f"{asset}/USDC:USDC",
                    'glassnode_symbol': asset,
                    'since': 1589126400  # 使用與 BTC 相同的起始時間
                }
        
        # 使用日誌記錄資產配置信息
        for asset, info in self.asset_info.items():
            logger.info(f"資產 {asset} 配置: glassnode_symbol={info['glassnode_symbol']}, bybit_symbol={info['bybit_symbol']}")

    def _load_config(self, config_file):
        """讀取 Excel 配置文件"""
        try:
            # 檢查文件是否存在
            if not os.path.exists(config_file):
                raise FileNotFoundError(f"配置文件 {config_file} 不存在")

            # 檢查文件擴展名
            if not config_file.endswith('.xlsx'):
                raise ValueError(f"配置文件 {config_file} 必須是 .xlsx 格式")

            # 讀取 Excel 文件 - 指定 'Metric Key' 列為字符串類型，避免自動轉換百分比
            df = pd.read_excel(config_file, dtype={'Metric Key': str})

            # 打印欄位名稱，用於調試
            file_name = os.path.basename(config_file)
            logger.info(f"檔案 {file_name} 的欄位名稱: {', '.join(df.columns.tolist())}")

            # 更靈活地處理欄位映射
            column_mapping = {}

            # 嘗試找到對應於 x 的欄位 (window, lookback, period 等)
            x_candidates = ['x(window)', 'window(x)', 'X(window)', 'Window(x)', 'window', 'Window', 'lookback', 'Lookback', 'period', 'Period', 'x', 'X']
            for candidate in x_candidates:
                if candidate in df.columns:
                    column_mapping['x'] = candidate
                    logger.info(f"將欄位 '{candidate}' 映射為 'x'")
                    break

            # 嘗試找到對應於 y 的欄位 (threshold, value, level 等)
            y_candidates = ['y(threshold)', 'threshold(y)', 'Y(threshold)', 'Threshold(y)', 'threshold', 'Threshold', 'value', 'Value', 'level', 'Level', 'y', 'Y']
            for candidate in y_candidates:
                if candidate in df.columns:
                    column_mapping['y'] = candidate
                    logger.info(f"將欄位 '{candidate}' 映射為 'y'")
                    break

            # 應用欄位映射
            if 'x' in column_mapping and column_mapping['x'] != 'x':
                df['x'] = df[column_mapping['x']]
            if 'y' in column_mapping and column_mapping['y'] != 'y':
                df['y'] = df[column_mapping['y']]

            # 創建配置字典
            config = {
                'ORDER_TYPE': 'limit',
                'RUN_FREQ': 10,
                'STRATEGIES': []
            }

            # 將 Excel 行轉換為策略配置
            for _, row in df.iterrows():
                # 取得 API 和 metric_key
                api = str(row['API'])
                metric_key = str(row['Metric Key']) if pd.notna(row['Metric Key']) else None

                # 特別處理 realized_supply_density_less_155 API 嘅 metric key
                if 'realized_supply_density_less_155' in api and metric_key is not None:
                    # 檢查 metric_key 是否已經是百分比格式
                    if metric_key not in ["5%", "10%", "15%"]:
                        # 嘗試將數值格式轉換為百分比格式
                        try:
                            metric_key_float = float(metric_key)
                            if metric_key_float == 0.15:
                                metric_key = "15%"
                            elif metric_key_float == 0.1:
                                metric_key = "10%"
                            elif metric_key_float == 0.05:
                                metric_key = "5%"
                            logger.info(f"已將 realized_supply_density_less_155 嘅 metric key {metric_key_float} 轉換為 {metric_key}")
                        except (ValueError, TypeError):
                            # 保留原樣如果轉換失敗
                            pass

                # 確保資產欄位存在，否則使用 API Symbol 作為預設
                asset = str(row['Asset']) if 'Asset' in df.columns and pd.notna(row['Asset']) else str(row['API Symbol'])

                # 數據類型檢查
                try:
                    strategy = {
                        'name': str(row['Strategy Name']),
                        'x': int(float(row['x'])),  # 將 x 值轉換為整數
                        'y': float(row['y']),
                        'api': str(row['API']),
                        'metric_key': metric_key,  # 使用可能已轉換過的 metric_key
                        'api_symbol': str(row['API Symbol']),
                        'resolution': str(row['Resolution']),
                        'model': str(row['Model']),
                        'type': str(row['Type']),
                        'style': str(row['Style']),
                        'symbol': asset  # 使用資產欄位或預設值
                    }
                    
                    # 如果指定資產過濾，且與當前策略資產不符，則跳過
                    if self.asset_filter and strategy['symbol'].upper() != self.asset_filter.upper():
                        continue
                        
                    config['STRATEGIES'].append(strategy)
                except (ValueError, TypeError) as e:
                    logger.error(f"策略 {row['Strategy Name']} 數據格式錯誤: {str(e)}")
                    continue

            if not config['STRATEGIES']:
                raise ValueError("沒有有效的策略配置")

            return config

        except Exception as e:
            logger.error(f"無法加載配置文件 {config_file}: {str(e)}")
            logger.error(traceback.format_exc())
            return None # Return None on error

    def _get_glassnode_data(self, strategy):
        """獲取 Glassnode 數據"""
        api_key = backtesting.API_KEY
        api_url = strategy.get('api')
        api_symbol = strategy.get('api_symbol')
        resolution = strategy.get('resolution')
        metric_key = strategy.get('metric_key')

        # 獲取資產特定的配置
        asset_symbol = strategy.get('symbol', 'BTC').upper()
        asset_config = self.asset_info.get(asset_symbol, self.asset_info.get('BTC', {}))
        
        # 獲取時間範圍
        since = asset_config.get('since', 1589155200)  # 默認 2020-05-11
        until = int(time.time())  # 當前時間

        # 初始化緩存管理器
        cache = GlassnodeCache()

        # 嘗試從緩存讀取數據
        df_value = cache.read_cache(api_url, api_symbol, resolution, metric_key,
                                   is_price=False, use_multiprocessing=self.use_multiprocessing)
        df_price = cache.read_cache("https://api.glassnode.com/v1/metrics/market/price_usd_close",
                                   api_symbol, resolution, None,
                                   is_price=True, use_multiprocessing=self.use_multiprocessing)

        # 如果緩存命中，直接返回數據
        if df_value is not None and df_price is not None:
            return df_value, df_price

        # 如果不是多進程模式，則使用 logger 記錄
        if not self.use_multiprocessing:
            logger.info(f"獲取 Glassnode 數據: {api_url}, Symbol: {api_symbol}, Resolution: {resolution}")
        else:
            # 多進程模式下使用 print，避免日誌衝突
            print(f"獲取 Glassnode 數據: {api_url}, Symbol: {api_symbol}, Resolution: {resolution}")

        try:
            # 添加重試機制，防止因為網絡問題導致數據獲取失敗
            max_retries = 3
            retry_count = 0

            while retry_count < max_retries:
                try:
                    # 獲取指標數據
                    res = requests.get(api_url, params={
                        "a": api_symbol,
                        "s": since,
                        "u": until,
                        "api_key": api_key,
                        "i": resolution
                    }, timeout=30)  # 添加 timeout 參數

                    if res.status_code != 200:
                        error_msg = f"API 請求失敗: {res.status_code} - {res.text}"
                        if self.use_multiprocessing:
                            print(error_msg)
                        else:
                            logger.error(error_msg)
                        retry_count += 1
                        time.sleep(2)  # 稍等一下再重試
                        continue

                    data = res.json()
                    df_value = backtesting.process_glassnode_data(data, metric_key=metric_key)

                    # 獲取價格數據
                    res_price = requests.get("https://api.glassnode.com/v1/metrics/market/price_usd_close", params={
                        "a": api_symbol,
                        "s": since,
                        "u": until,
                        "api_key": api_key,
                        "i": resolution
                    }, timeout=30)  # 添加 timeout 參數

                    if res_price.status_code != 200:
                        error_msg = f"獲取價格數據失敗: {res_price.status_code} - {res_price.text}"
                        if self.use_multiprocessing:
                            print(error_msg)
                        else:
                            logger.error(error_msg)
                        retry_count += 1
                        time.sleep(2)  # 稍等一下再重試
                        continue

                    price_data = res_price.json()
                    df_price = backtesting.process_glassnode_data(price_data)

                    # 將數據保存到緩存
                    cache.write_cache(df_value, api_url, api_symbol, resolution, metric_key,
                                     is_price=False, use_multiprocessing=self.use_multiprocessing)
                    cache.write_cache(df_price, "https://api.glassnode.com/v1/metrics/market/price_usd_close",
                                     api_symbol, resolution, None,
                                     is_price=True, use_multiprocessing=self.use_multiprocessing)

                    return df_value, df_price

                except requests.exceptions.RequestException as e:
                    error_msg = f"請求異常，重試 ({retry_count+1}/{max_retries}): {str(e)}"
                    if self.use_multiprocessing:
                        print(error_msg)
                    else:
                        logger.warning(error_msg)
                    retry_count += 1
                    time.sleep(2)  # 稍等一下再重試

            # 全部重試都失敗
            error_msg = f"獲取 Glassnode 數據失敗，已重試 {max_retries} 次"
            if self.use_multiprocessing:
                print(error_msg)
            else:
                logger.error(error_msg)
            return None, None

        except Exception as e:
            error_msg = f"獲取 Glassnode 數據時出錯: {str(e)}"
            if self.use_multiprocessing:
                print(error_msg)
                print(traceback.format_exc())
            else:
                logger.error(error_msg)
                logger.error(traceback.format_exc())
            return None, None

    def calculate_alpha_beta(self, df_slice):
        """計算 alpha, beta 和相關統計數據"""
        try:
            # 創建回歸數據
            # Ensure required columns exist before proceeding
            if 'percentage_change' not in df_slice.columns or 'pnl' not in df_slice.columns:
                logger.warning("缺少 'percentage_change' 或 'pnl' 列，無法計算 alpha/beta")
                return 0.0, 0.0, 0.0, 1.0, 1.0

            clean_df = df_slice[['percentage_change', 'pnl']].replace([np.inf, -np.inf], np.nan).dropna()

            # 如果數據不足，返回零值
            if len(clean_df) < 2:
                return 0.0, 0.0, 0.0, 1.0, 1.0

            # 添加常數項用於計算截距 (alpha)
            X = sm.add_constant(clean_df['percentage_change'])

            # 執行回歸
            model = sm.OLS(clean_df['pnl'], X).fit()

            # 提取結果
            alpha = model.params['const']
            beta = model.params['percentage_change']
            r_squared = model.rsquared
            alpha_p_value = model.pvalues['const']  # alpha 的 p-value
            beta_p_value = model.pvalues['percentage_change']  # beta 的 p-value

            # 年化 alpha
            # Use the imported backtesting module's time_variable
            alpha_annualized = alpha * backtesting.time_variable

            return round(alpha_annualized, 3), round(beta, 3), round(r_squared, 3), round(alpha_p_value, 3), round(beta_p_value, 3)
        except Exception as e:
            logger.warning(f"計算 alpha/beta 時出錯: {str(e)}")
            return 0.0, 0.0, 0.0, 1.0, 1.0

    def _run_single_strategy(self, strategy):
        """
        運行單個策略的回測
        
        參數:
            strategy (dict): 策略配置
        
        返回:
            tuple: (策略名稱, 策略結果字典, DataFrame)
        """
        strategy_name = strategy.get('name')
        model = strategy.get('model')
        window = strategy.get('x')
        threshold = strategy.get('y')
        strategy_type = strategy.get('type')
        style = strategy.get('style')
        resolution = strategy.get('resolution')
        
        # 如果不是多進程模式，則使用 logger 記錄
        if not self.use_multiprocessing:
            logger.info(f"運行策略 {strategy_name}: {model}, Window={window}, Threshold={threshold}, Type={strategy_type}, Style={style}, Resolution={resolution}")
        else:
            # 多進程模式下使用 print，避免日誌衝突
            print(f"運行策略 {strategy_name}: {model}, Window={window}, Threshold={threshold}, Type={strategy_type}, Style={style}, Resolution={resolution}")
        
        try:
            # 獲取數據
            df_value, df_price = self._get_glassnode_data(strategy)
            if df_value is None or df_price is None:
                print(f"策略 {strategy_name} 無法獲取數據，跳過") if self.use_multiprocessing else logger.error(f"策略 {strategy_name} 無法獲取數據，跳過")
                return strategy_name, None, None
            
            # 準備數據 - 確保日期格式正確
            df = backtesting.prepare_data(df_value, df_price)
            
            # 確保時間戳正確轉換為日期並設為索引
            if 't' in df.columns:
                df = df.set_index('t')
            elif not isinstance(df.index, pd.DatetimeIndex):
                try:
                    df.index = pd.to_datetime(df.index)
                except Exception as e:
                    if self.use_multiprocessing:
                        print(f"無法將索引轉換為日期時間格式: {str(e)}")
                    else:
                        logger.warning(f"無法將索引轉換為日期時間格式: {str(e)}")
            
            # 檢查數據的開始日期是否滿足最小開始日期要求
            if self.min_start_date is not None and len(df) > 0:
                first_date = df.index.min()
                # 確保 first_date 是 Timestamp 類型
                if not isinstance(first_date, pd.Timestamp):
                    try:
                        first_date = pd.to_datetime(first_date)
                    except Exception as e:
                        if self.use_multiprocessing:
                            print(f"無法將開始日期轉換為 Timestamp: {str(e)}")
                        else:
                            logger.warning(f"無法將開始日期轉換為 Timestamp: {str(e)}")
                        # 跳過日期檢查
                        first_date = None
                
                # 只有當成功轉換日期時才執行比較
                if first_date is not None and first_date > self.min_start_date:
                    msg = f"策略 {strategy_name} 數據開始日期 ({first_date.strftime('%Y-%m-%d')}) 晚於要求的最小開始日期 ({self.min_start_date.strftime('%Y-%m-%d')})，跳過"
                    print(msg) if self.use_multiprocessing else logger.warning(msg)
                    return strategy_name, None, None
            
            # 設置 backtesting 相關變量 (在多進程模式下每個進程都有自己的副本)
            # 設置 SHIFT_PERIODS
            shift_periods = backtesting.get_shift_periods(resolution)
            backtesting.SHIFT_PERIODS = shift_periods
            
            # 輸出 shift_periods 日誌
            if not self.use_multiprocessing:
                logger.info(f"策略 {strategy_name} 使用時間框架 {resolution}，設置 shift_periods={shift_periods}")
            else:
                print(f"策略 {strategy_name} 使用時間框架 {resolution}，設置 shift_periods={shift_periods}")
            
            # 更新 backtesting 模組的全局變量
            backtesting.df = df
            backtesting.df_value = df_value
            backtesting.df_price = df_price
            backtesting.factor_name = f"{strategy.get('api_symbol')} {strategy_name}"
            backtesting.underlying = strategy.get('api_symbol')
            backtesting.metric_key = strategy.get('metric_key')
            backtesting.resolution = resolution
            backtesting.time_variable = backtesting.get_time_variable(resolution)
            
            # 取得策略對應的模型函數
            model_func = None
            for model_name, params in get_base_params().items():
                # 將模型名稱轉為小寫，並將空格和連字符都替換為下劃線
                normalized_model_name = model_name.lower().replace(' ', '_').replace('-', '_')
                normalized_input_model = model.lower().replace(' ', '_').replace('-', '_')
                
                # 1. 嘗試完全匹配
                if normalized_model_name == normalized_input_model:
                    model_func = params['func']
                    break
                
                # 2. 嘗試部分匹配（例如 z-score 和 z_score）
                if normalized_model_name.replace('_', '') == normalized_input_model.replace('_', ''):
                    model_func = params['func']
                    logger.info(f"通過移除下劃線匹配到模型: {model} -> {model_name}")
                    break
                
                # 3. 嘗試首字母匹配 (例如 roc 和 rate_of_change)
                if model_name.lower().startswith('r') and normalized_input_model.startswith('r'):
                    if 'rate_of_change' in normalized_model_name and ('roc' in normalized_input_model or 'rate' in normalized_input_model):
                        model_func = params['func']
                        logger.info(f"通過縮寫匹配到模型: {model} -> {model_name}")
                        break
                
                # 4. MA Diff 和 MA Cross 的特殊處理
                if ('ma_diff' in normalized_model_name or 'ma diff' in model_name.lower()) and ('ma_diff' in normalized_input_model or 'ma diff' in model.lower() or 'madiff' in normalized_input_model):
                    model_func = params['func']
                    logger.info(f"匹配到 MA Diff 模型: {model} -> {model_name}")
                    break
                
                if ('ma_cross' in normalized_model_name or 'ma cross' in model_name.lower()) and ('ma_cross' in normalized_input_model or 'ma cross' in model.lower() or 'macross' in normalized_input_model):
                    model_func = params['func']
                    logger.info(f"匹配到 MA Cross 模型: {model} -> {model_name}")
                    break
                    
            if model_func is None:
                print(f"找不到模型 {model}，跳過策略 {strategy_name}") if self.use_multiprocessing else logger.error(f"找不到模型 {model}，跳過策略 {strategy_name}")
                return strategy_name, None, None
            
            # 生成策略信號
            df['pos'] = model_func(df, window, threshold, strategy_type, style)
            
            # 計算策略收益 - 根據不同時間框架使用相應的 shift_periods
            df['pos_t-1'] = df['pos'].shift(shift_periods)
            df['trade'] = abs(df['pos_t-1'] - df['pos'])
            pnl = df['pos_t-1'] * df['percentage_change'] - df['trade'] * TRANSACTION_COST
            df['pnl'] = pnl  # 添加 pnl 到 DataFrame
            df['cumu'] = pnl.cumsum()
            df['bnh_cumu'] = df['percentage_change'].cumsum()
            
            # 記錄列名，以便保存到 CSV 時使用
            df['chg'] = df['percentage_change']  # 添加 chg 列，兼容 Portfolio_Sharpe.py 的需求
            
            # 計算長短倉分量 - 根據不同時間框架使用相應的 shift_periods
            if strategy_type == 'long_short':
                # 長倉部分
                df['long_pos'] = df['pos'].copy()
                df.loc[df['long_pos'] < 0, 'long_pos'] = 0
                df['long_pos'] = df['long_pos'] * LONG_LEVERAGE  # 應用長倉槓桿
                df['long_pos_t-1'] = df['long_pos'].shift(shift_periods)
                df['long_trade'] = abs(df['long_pos_t-1'] - df['long_pos'])
                long_pnl = df['long_pos_t-1'] * df['percentage_change'] - df['long_trade'] * TRANSACTION_COST
                df['long_pnl'] = long_pnl
                df['long_cumu'] = long_pnl.cumsum()
                
                # 短倉部分
                df['short_pos'] = df['pos'].copy()
                df.loc[df['short_pos'] > 0, 'short_pos'] = 0
                df['short_pos'] = df['short_pos'].abs() * SHORT_LEVERAGE  # 轉換為正值並應用槓桿
                df['short_pos_t-1'] = df['short_pos'].shift(shift_periods)
                df['short_trade'] = abs(df['short_pos_t-1'] - df['short_pos'])
                short_pnl = df['short_pos_t-1'] * (-df['percentage_change']) - df['short_trade'] * TRANSACTION_COST
                df['short_pnl'] = short_pnl
                df['short_cumu'] = short_pnl.cumsum()
                
                # 更新總體 pnl 計算，結合長短倉（包含短倉槓桿）
                df['pnl'] = long_pnl + short_pnl
                df['cumu'] = df['pnl'].cumsum()
            
            # 計算性能指標
            sr = pnl.mean() / pnl.std() * np.sqrt(backtesting.time_variable) if pnl.std() != 0 else np.nan
            ar = pnl.mean() * backtesting.time_variable
            dd = df['cumu'].cummax() - df['cumu']
            mdd = dd.max()
            cr = ar / mdd if mdd != 0 else np.nan
            
            # 計算交易統計
            trades = df['trade'].sum()
            trades_per_year = trades / (len(df) / backtesting.time_variable)
            
            performance_msg = f"策略 {strategy_name} 性能指標: SR={sr:.3f}, AR={ar:.3f}, MDD={mdd:.3f}, CR={cr:.3f}, 交易次數={trades:.0f}, Shift={shift_periods}"
            print(performance_msg) if self.use_multiprocessing else logger.info(performance_msg)
            
            # 將結果存入字典
            result = {
                'strategy': strategy,
                'sr': sr,
                'ar': ar,
                'mdd': mdd,
                'cr': cr,
                'trades': trades,
                'trades_per_year': trades_per_year,
                'shift_periods': shift_periods,
                'resolution': resolution
            }
            
            return strategy_name, result, df
            
        except Exception as e:
            error_msg = f"運行策略 {strategy_name} 時出錯: {str(e)}"
            if self.use_multiprocessing:
                print(error_msg)
                import traceback
                print(traceback.format_exc())
            else:
                logger.error(error_msg)
                logger.error(traceback.format_exc())
            return strategy_name, {'error': str(e)}, None

    def run_all_strategies(self):
        """運行所有配置中的策略"""
        logger.info("開始運行所有策略")
        failed_strategies = {} # Dictionary to store failures

        if not self.use_multiprocessing or len(self.strategies) <= 1:
            # 單進程模式或只有一個策略時
            logger.info("使用單進程模式處理策略")
            for strategy in tqdm(self.strategies, desc="運行策略"):
                strategy_name, result, df = self._run_single_strategy(strategy)
                if result is not None and df is not None:
                    self.results[strategy_name] = result
                    self.strategy_dfs[strategy_name] = df
                else:
                    error_msg = f"策略 {strategy_name} 運行失敗"
                    failed_strategies[strategy_name] = (error_msg, None)
                    logger.error(error_msg)
        else:
            # 多進程模式
            logger.info(f"使用多進程模式處理策略，進程數: {self.max_workers}")

            # 使用 ProcessPoolExecutor 進行並行處理
            with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有任務
                future_to_strategy = {
                    executor.submit(self._run_single_strategy, strategy): strategy
                    for strategy in self.strategies
                }

                # 先收集所有結果
                all_results = {}
                all_dfs = {}
                temp_failed = {} # Temporary dict for failures from futures

                # 使用 tqdm 顯示進度
                for future in tqdm(as_completed(future_to_strategy), total=len(future_to_strategy), desc="運行策略"):
                    strategy = future_to_strategy[future]
                    try:
                        strategy_name, result, df = future.result()
                        if result is not None and df is not None:
                            all_results[strategy_name] = result
                            all_dfs[strategy_name] = df
                        else:
                            # 策略返回了None結果
                            strategy_name = strategy.get('name', '未知策略')
                            error_msg = f"策略 {strategy_name} 無有效結果返回"
                            temp_failed[strategy_name] = (error_msg, None)
                    except Exception as e:
                        # Catch errors during future.result() itself (less likely but possible)
                        strategy_name = strategy.get('name', '未知策略')
                        error_msg = f"獲取策略 {strategy_name} 結果時出錯: {str(e)}"
                        tb_str = traceback.format_exc()
                        temp_failed[strategy_name] = (error_msg, tb_str)

                # 一次性更新成功結果字典
                self.results.update(all_results)
                self.strategy_dfs.update(all_dfs)

                # Log failures collected from futures
                failed_strategies.update(temp_failed)
                if failed_strategies:
                    logger.error("--- 以下策略運行失敗 ---")
                    for name, (err, tb) in failed_strategies.items():
                         logger.error(f"策略: {name}")
                         logger.error(f"  錯誤: {err}")
                         if tb:
                              logger.error(f"  Traceback:\n{tb}")
                    logger.error("----------------------")

        successful_count = len(self.strategy_dfs)
        total_count = len(self.strategies)
        failed_count = total_count - successful_count
        logger.info(f"完成所有策略運行，成功: {successful_count}/{total_count} (失敗: {failed_count})")

        # Store a copy of results without the large df for later use if needed
        self.results_summary = {k: {mk: mv for mk, mv in v.items() if mk != 'df'} for k, v in self.results.items()}

        # Log failed strategy names if any
        if failed_strategies:
             logger.warning(f"失敗的策略列表: {list(failed_strategies.keys())}")
        
        # 輸出策略時間框架統計
        self.print_strategy_timeframes()
             
        return self.strategy_dfs

    def print_strategy_timeframes(self):
        """輸出所有策略的時間框架統計信息"""
        if not self.results:
            logger.warning("無策略結果可供統計時間框架")
            return
            
        # 按時間框架分類策略
        timeframe_strategies = {
            '24h': [],
            '1h': [],
            '10m': [],
            'other': []
        }
        
        # 統計各時間框架策略數量
        for name, result in self.results.items():
            # 優先從result的strategy字典中獲取resolution
            resolution = None
            if 'strategy' in result and isinstance(result['strategy'], dict) and 'resolution' in result['strategy']:
                resolution = result['strategy']['resolution']
            else:
                # 如果沒有，嘗試從results的頂層獲取
                resolution = result.get('resolution', '24h')
            
            # 檢查resolution是否是有效的時間框架
            if resolution in timeframe_strategies and resolution != 'other':
                timeframe_strategies[resolution].append(name)
            else:
                timeframe_strategies['other'].append(name)
                logger.warning(f"策略 {name} 使用了未知的時間框架: {resolution}，將分類為'other'")
        
        # 輸出統計結果
        logger.info("策略時間框架統計:")
        logger.info(f"  24h: {len(timeframe_strategies['24h'])} 個策略")
        logger.info(f"  1h: {len(timeframe_strategies['1h'])} 個策略")
        logger.info(f"  10m: {len(timeframe_strategies['10m'])} 個策略")
        if timeframe_strategies['other']:
            logger.info(f"  其他: {len(timeframe_strategies['other'])} 個策略")
        logger.info(f"  總計: {sum(len(strategies) for strategies in timeframe_strategies.values())} 個策略")
        
        # 輸出各時間框架詳細策略列表
        if timeframe_strategies['24h']:
            logger.info("24h 策略列表:")
            for name in timeframe_strategies['24h']:
                logger.info(f"  - {name}")
                
        if timeframe_strategies['1h']:
            logger.info("1h 策略列表:")
            for name in timeframe_strategies['1h']:
                logger.info(f"  - {name}")
                
        if timeframe_strategies['10m']:
            logger.info("10m 策略列表:")
            for name in timeframe_strategies['10m']:
                logger.info(f"  - {name}")
                
        if timeframe_strategies['other']:
            logger.info("其他時間框架策略列表:")
            for name in timeframe_strategies['other']:
                resolution = None
                result = self.results.get(name, {})
                if 'strategy' in result and isinstance(result['strategy'], dict) and 'resolution' in result['strategy']:
                    resolution = result['strategy']['resolution']
                else:
                    resolution = result.get('resolution', '未知')
                logger.info(f"  - {name} (時間框架: {resolution})")

    def generate_portfolio(self, weighting_method='equal', min_sr=0, rolling_sharpe_filter=True):
        """
        根據權重方法生成投資組合
        
        參數:
            weighting_method (str): 權重方法，可選 'equal', 'sharpe', 'calmar', 'sortino'
            min_sr (float): 最小 Sharpe Ratio，僅當 rolling_sharpe_filter=False 時使用
            rolling_sharpe_filter (bool): 是否使用 rolling sharpe 篩選策略
        """
        logger.info(f"使用 {weighting_method} 權重方法生成投資組合")

        # 檢查是否使用經過 rolling sharpe 篩選後的策略
        if rolling_sharpe_filter and hasattr(self, 'metrics_results') and self.metrics_results and 'filtered_strategies' in self.metrics_results:
            # 檢查是否有通過篩選的策略
            filtered_results = self.metrics_results.get('filtered_strategies', {})
            if filtered_results and 'final_passed' in filtered_results:
                strategy_candidates = {name: self.results[name] for name in filtered_results['final_passed'].keys() if name in self.results}
                logger.info(f"找到 {len(strategy_candidates)} 個通過 rolling sharpe 篩選的策略")
                
                if strategy_candidates:
                    # 只使用通過篩選嘅策略
                    logger.info("使用通過 rolling sharpe 篩選的策略:")
                    for name in strategy_candidates.keys():
                        logger.info(f"  - {name}")
                else:
                    # 如果沒有通過篩選的策略，返回空結果
                    logger.warning("沒有策略通過 rolling sharpe 篩選，無法生成投資組合")
                    return None
            else:
                # 如果沒有篩選結果，返回空結果
                logger.warning("沒有 rolling sharpe 篩選結果，無法生成投資組合")
                return None
        else:
            # 如果不使用篩選策略，則使用全部策略
            strategy_candidates = {name: metrics for name, metrics in self.results.items() if metrics['sr'] > min_sr}
            logger.info(f"將使用 {len(strategy_candidates)} 個符合最小 Sharpe Ratio ({min_sr}) 的策略")

        # 確保有足夠的策略
        if not strategy_candidates:
            logger.error(f"沒有可用策略符合最小 Sharpe Ratio ({min_sr}) 的要求，請降低閾值")
            return None

        logger.info(f"將使用以下策略計算共同日期: {list(strategy_candidates.keys())}") # Log selected strategies

        # 確定權重
        weights = {}
        time_variable = backtesting.time_variable # Use global time_variable
        if weighting_method == 'equal':
            # 等權重
            weight_value = 1.0 / len(strategy_candidates)
            weights = {name: weight_value for name in strategy_candidates}

        elif weighting_method == 'sharpe':
            # 按 Sharpe Ratio 加權
            valid_srs = [result['sr'] for result in strategy_candidates.values() if 'sr' in result and not np.isnan(result['sr'])]
            total_sr = sum(valid_srs)
            if total_sr <= 0:
                logger.warning("所有策略的 Sharpe Ratio 總和小於等於 0，使用等權重")
                weight_value = 1.0 / len(strategy_candidates)
                weights = {name: weight_value for name in strategy_candidates}
            else:
                weights = {name: (result['sr'] / total_sr if 'sr' in result and not np.isnan(result['sr']) else 0)
                         for name, result in strategy_candidates.items()}

        elif weighting_method == 'calmar':
            # 按 Calmar Ratio 加權
            valid_crs = [result['cr'] for result in strategy_candidates.values() if 'cr' in result and not np.isnan(result['cr'])]
            total_cr = sum(valid_crs)
            if total_cr <= 0:
                logger.warning("所有策略的 Calmar Ratio 總和小於等於 0，使用等權重")
                weight_value = 1.0 / len(strategy_candidates)
                weights = {name: weight_value for name in strategy_candidates}
            else:
                weights = {name: (result['cr'] / total_cr if 'cr' in result and not np.isnan(result['cr']) else 0)
                         for name, result in strategy_candidates.items()}

        elif weighting_method == 'sortino':
            # 計算 Sortino Ratio (下行風險調整收益)
            sortino_values = {}
            for name, result in strategy_candidates.items():
                if name not in self.strategy_dfs:
                     logger.warning(f"找不到策略 {name} 的 DataFrame，無法計算 Sortino Ratio")
                     sortino_values[name] = np.nan
                     continue
                df = self.strategy_dfs[name] # Get df from strategy_dfs
                if 'pnl' not in df.columns:
                     logger.warning(f"策略 {name} 的 DataFrame 缺少 'pnl' 列，無法計算 Sortino Ratio")
                     sortino_values[name] = np.nan
                     continue

                downside_returns = df['pnl'][df['pnl'] < 0]
                downside_std = downside_returns.std() if len(downside_returns) > 0 else 1e-10
                # Use the correct time_variable
                sortino = df['pnl'].mean() * np.sqrt(time_variable) / downside_std
                sortino_values[name] = sortino

            valid_sortinos = [sr for sr in sortino_values.values() if not np.isnan(sr)]
            total_sortino = sum(valid_sortinos)
            if total_sortino <= 0:
                logger.warning("所有策略的 Sortino Ratio 總和小於等於 0，使用等權重")
                weight_value = 1.0 / len(strategy_candidates)
                weights = {name: weight_value for name in strategy_candidates}
            else:
                weights = {name: (sortino_values[name] / total_sortino if not np.isnan(sortino_values[name]) else 0)
                         for name in strategy_candidates}

        else:
            logger.warning(f"未知權重方法 {weighting_method}，使用等權重")
            weight_value = 1.0 / len(strategy_candidates)
            weights = {name: weight_value for name in strategy_candidates}

        # 規範化權重，確保總和為 1
        total_weight = sum(weights.values())
        if total_weight > 0:
            weights = {name: weight / total_weight for name, weight in weights.items()}
            
        # 保存投資組合權重到實例變量，以便其他函數使用（例如 plot_equity_curves）
        self.portfolio_weights = weights.copy()
        logger.info(f"保存了 {len(self.portfolio_weights)} 個策略的權重到實例變量")

        # 分類不同時間框架的策略
        timeframe_strategies = {
            '24h': [],
            '1h': [],
            '10m': [],
            'other': []
        }
        
        # 記錄各策略的時間框架
        for name in weights.keys():
            result = strategy_candidates.get(name, {})
            # 優先從result的strategy字典中獲取resolution
            resolution = None
            if 'strategy' in result and isinstance(result['strategy'], dict) and 'resolution' in result['strategy']:
                resolution = result['strategy']['resolution']
            else:
                # 如果沒有，嘗試從results的頂層獲取
                resolution = result.get('resolution', '24h')
            
            # 檢查resolution是否是有效的時間框架
            if resolution in timeframe_strategies and resolution != 'other':
                timeframe_strategies[resolution].append(name)
            else:
                timeframe_strategies['other'].append(name)
                logger.warning(f"策略 {name} 使用了未知的時間框架: {resolution}，將分類為'other'並以24h數據處理")
                
        # 輸出各時間框架策略統計
        logger.info("組合中各時間框架策略統計:")
        logger.info(f"  24h: {len(timeframe_strategies['24h'])} 個策略")
        logger.info(f"  1h: {len(timeframe_strategies['1h'])} 個策略")
        logger.info(f"  10m: {len(timeframe_strategies['10m'])} 個策略")
        if timeframe_strategies['other']:
            logger.info(f"  其他: {len(timeframe_strategies['other'])} 個策略")
        logger.info(f"  總計: {sum(len(strategies) for strategies in timeframe_strategies.values())} 個策略")

        # 打印權重分配
        logger.info("權重分配:")
        for name, weight in weights.items():
            logger.info(f"  {name}: {weight:.4f}")

        # 找到所有策略的共同日期
        common_dates = None
        strategies_to_combine = list(weights.keys()) # Use the strategies that got weights assigned
        logger.info(f"開始計算 {len(strategies_to_combine)} 個策略的共同日期...")

        for i, name in enumerate(strategies_to_combine): # Iterate through weighted strategies
            if name not in self.strategy_dfs:
                 logger.warning(f"找不到策略 {name} 的 DataFrame，跳過日期查找")
                 continue
            df = self.strategy_dfs[name] # Get df from strategy_dfs

            # Log index info for the current strategy
            if isinstance(df.index, pd.DatetimeIndex):
                 logger.debug(f"策略 {name} (Index {i+1}/{len(strategies_to_combine)}): Index Type=DatetimeIndex, Range={df.index.min()} to {df.index.max()}, Length={len(df.index)}")
            else:
                 logger.warning(f"策略 {name} (Index {i+1}/{len(strategies_to_combine)}): Index Type={type(df.index)}, Length={len(df.index)} - 可能導致問題")
                 # Attempt conversion again just in case? Or rely on checks in _run_single_strategy
                 try:
                     current_index_name = df.index.name # Store original index name if exists
                     df.index = pd.to_datetime(df.index)
                     df.index.name = current_index_name # Restore index name
                     if not isinstance(df.index, pd.DatetimeIndex):
                          logger.error(f"策略 {name}: 重試轉換索引失敗")
                          continue # Skip this strategy if conversion fails definitively
                     else:
                          logger.info(f"策略 {name}: 索引已成功轉換為 DatetimeIndex")
                 except Exception as e:
                      logger.error(f"策略 {name}: 嘗試轉換索引時出錯: {e}")
                      continue

            # Calculate intersection
            if common_dates is None:
                common_dates = df.index
                logger.debug(f"  初始化 common_dates，長度: {len(common_dates)}")
            else:
                original_len = len(common_dates)
                common_dates = common_dates.intersection(df.index)
                logger.debug(f"  與策略 {name} 取交集後，common_dates 長度從 {original_len} 變為 {len(common_dates)}")
                if len(common_dates) == 0:
                     logger.error(f"  與策略 {name} 取交集後 common_dates 為空！停止查找。")
                     # Log the date ranges to understand why they don't overlap
                     try:
                         # Need the previous common_dates range before intersection
                         # This requires storing the state before intersection, which is tricky here.
                         # Let's log the current strategy's range which caused the failure.
                         logger.error(f"  導致失敗的策略 {name} 範圍: {df.index.min()} to {df.index.max()}")
                     except Exception as log_e:
                         logger.error(f"  記錄日期範圍時出錯: {log_e}")
                     break # Stop intersecting if it becomes empty

        if common_dates is None or len(common_dates) == 0:
            logger.error("所選策略沒有共同的交易日期") # This error message now has more context logged before it
            # Log the strategies that were attempted
            logger.error(f"嘗試合併的策略: {strategies_to_combine}")
            return None

        common_dates = sorted(common_dates)
        logger.info(f"找到共同日期 {len(common_dates)} 個，範圍從 {common_dates[0]} 到 {common_dates[-1]}")

        # 創建組合 DataFrame，確保時間索引正確
        try:
             # Check the type of the elements in common_dates
             if common_dates and isinstance(common_dates[0], pd.Timestamp):
                 index = pd.DatetimeIndex(common_dates)
             else:
                 # Attempt conversion if needed
                 index = pd.to_datetime(common_dates)
             logger.debug(f"創建 Portfolio DataFrame 使用的索引類型: {type(index)}")
        except Exception as e:
            logger.error(f"無法將共同日期轉換為 DatetimeIndex: {str(e)}")
            logger.error(f"common_dates 的前幾個元素: {common_dates[:5]}")
            # Fallback to using the list as index if conversion fails
            index = common_dates

        # 創建 DataFrame
        portfolio_df = pd.DataFrame(index=index)
        portfolio_df['pnl'] = 0.0
        portfolio_df['cumu'] = 0.0
        portfolio_df['bnh_cumu'] = 0.0

        # 合併所有策略的 PnL
        has_bnh = False
        first_strategy_df = None
        for name, weight in weights.items():
            if name not in self.strategy_dfs:
                 logger.warning(f"找不到策略 {name} 的 DataFrame，跳過 PnL 合併")
                 continue
            df = self.strategy_dfs[name] # Get df from strategy_dfs
            if 'pnl' in df.columns:
                # Align index before adding
                portfolio_df['pnl'] += df.loc[common_dates, 'pnl'].fillna(0) * weight
            if not has_bnh and 'bnh_cumu' in df.columns:
                first_strategy_df = df
                has_bnh = True

        # 計算累積收益
        portfolio_df['cumu'] = portfolio_df['pnl'].cumsum()

        # 使用第一個策略的 Buy & Hold 數據
        if first_strategy_df is not None and 'bnh_cumu' in first_strategy_df.columns:
            portfolio_df['bnh_cumu'] = first_strategy_df.loc[common_dates, 'bnh_cumu']
        else:
             logger.warning("找不到任何策略的 Buy & Hold 數據")


        # 計算組合的性能指標
        sr = portfolio_df['pnl'].mean() / portfolio_df['pnl'].std() * np.sqrt(time_variable) if portfolio_df['pnl'].std() != 0 else np.nan
        ar = portfolio_df['pnl'].mean() * time_variable
        dd = portfolio_df['cumu'].cummax() - portfolio_df['cumu']
        mdd = dd.max()
        cr = ar / mdd if mdd != 0 else np.nan

        logger.info(f"組合性能指標: SR={sr:.3f}, AR={ar:.3f}, MDD={mdd:.3f}, CR={cr:.3f}")

        # 保存組合結果
        self.portfolio_df = portfolio_df
        self.portfolio_metrics = {
            'sr': sr,
            'ar': ar,
            'mdd': mdd,
            'cr': cr,
            'weighting_method': weighting_method
        }

        # 保存策略 DataFrame 到 CSV 文件，以便 Portfolio_Sharpe.py 可以讀取
        if CURRENT_REPORT_DIR:
            data_dir = os.path.join(CURRENT_REPORT_DIR, 'data')
            logger.info(f"將策略數據保存到報告目錄: {data_dir}")
        else:
            # 改用 self.output_dir 作為更可靠嘅後備路徑
            data_dir = os.path.join(self.output_dir, 'data')
            logger.warning(f"CURRENT_REPORT_DIR 未設置，將策略數據保存到實例輸出目錄: {data_dir}") # 更新日誌信息

        os.makedirs(data_dir, exist_ok=True)

        # 保存每個策略的數據
        for name, result in strategy_candidates.items(): # Use strategy_candidates
            # 獲取策略資訊
            if name not in self.strategy_dfs:
                 logger.warning(f"找不到策略 {name} 的 DataFrame，跳過 CSV 保存")
                 continue
            df = self.strategy_dfs[name].copy() # Get df from strategy_dfs
            resolution = result.get('resolution', '24h')
            weight = weights.get(name, 0)

            # 確保 DataFrame 包含必要的列
            if 'chg' not in df.columns and 'percentage_change' in df.columns:
                df['chg'] = df['percentage_change']

            # 重設索引以便 CSV 輸出包含時間列
            df_to_save = df.copy()
            if isinstance(df_to_save.index, pd.DatetimeIndex):
                df_to_save = df_to_save.reset_index()

            # 將時間列名統一為 't'
            if 'index' in df_to_save.columns and 't' not in df_to_save.columns:
                df_to_save = df_to_save.rename(columns={'index': 't'})
            elif df_to_save.index.name == 't': # If index name is 't' after reset_index
                 df_to_save = df_to_save.reset_index()

            # 使用適當的命名約定，包含時間框架信息
            csv_file = os.path.join(data_dir, f"{name.replace('/', '_')}_{resolution}_w{weight:.4f}.csv") # Sanitize name

            # 只保存必要的列，減少文件大小
            columns_to_save = ['t', 'pnl', 'pos', 'chg']
            available_columns = [col for col in columns_to_save if col in df_to_save.columns]

            if not available_columns:
                logger.warning(f"策略 {name} 沒有可保存的列，跳過")
                continue

            df_final_save = df_to_save[available_columns]

            # 保存 CSV 文件
            df_final_save.to_csv(csv_file, index=False)
            logger.info(f"保存策略 {name} ({resolution}) 數據到 {csv_file}")

        # 輸出時間框架統計信息
        logger.info("時間框架策略統計:")
        logger.info(f"  24h: {len(timeframe_strategies['24h'])} 個策略")
        logger.info(f"  1h: {len(timeframe_strategies['1h'])} 個策略")
        logger.info(f"  10m: {len(timeframe_strategies['10m'])} 個策略")
        if len(timeframe_strategies['other']) > 0:
            logger.info(f"  其他時間框架: {len(timeframe_strategies['other'])} 個策略")
        
        # 確認實際添加到各時間框架的策略數量
        logger.info("實際添加到各時間框架的策略數量:")
        logger.info(f"  24h: {len(timeframe_strategies['24h'])} 個策略")
        logger.info(f"  1h: {len(timeframe_strategies['1h'])} 個策略")
        logger.info(f"  10m: {len(timeframe_strategies['10m'])} 個策略")
        if len(timeframe_strategies['other']) > 0:
            logger.info(f"  其他時間框架: {len(timeframe_strategies['other'])} 個策略")

        return portfolio_df

    def plot_equity_curves(self, save_dir=None):
        """
        繪製每個策略的 equity curve，完全複製 backtesting.py 的風格
        只繪製最終被用於計算投資組合的已篩選策略
        
        參數:
            save_dir (str): 保存目錄，如果為 None，使用 self.output_dir
        """
        # 首先檢查是否啟用了生成單一策略 equity curves
        if not hasattr(self, 'generate_individual_curves') or not self.generate_individual_curves:
            logger.info("單一策略 equity curves 生成已關閉，跳過生成")
            return
            
        if not self.strategy_dfs:
            logger.error("無可用策略數據，請先運行 run_all_strategies()")
            return
        
        # 確定要繪製的策略列表 - 優先使用投資組合權重中的策略
        strategies_to_plot = []
        
        if hasattr(self, 'portfolio_weights') and self.portfolio_weights:
            # 如果存在投資組合權重，使用這些策略
            strategies_to_plot = list(self.portfolio_weights.keys())
            logger.info(f"將為投資組合中的 {len(strategies_to_plot)} 個已篩選策略生成 equity curve")
        elif hasattr(self, 'optimal_strategies') and self.optimal_strategies:
            # 如果存在最優策略列表，使用這些策略
            strategies_to_plot = self.optimal_strategies
            logger.info(f"將為 {len(strategies_to_plot)} 個最優策略生成 equity curve")
        elif hasattr(self, 'filtered_strategies') and self.filtered_strategies:
            # 如果存在篩選後的策略列表，使用這些策略
            strategies_to_plot = self.filtered_strategies
            logger.info(f"將為 {len(strategies_to_plot)} 個經 rolling sharpe 篩選的策略生成 equity curve")
        else:
            # 如果無法確定已篩選策略，使用所有策略
            logger.warning("找不到已篩選策略列表，將使用所有策略")
            strategies_to_plot = list(self.strategy_dfs.keys())
        
        # 檢查最終要繪製的策略數量
        if not strategies_to_plot:
            logger.warning("沒有需要繪製 equity curve 的策略")
            return
            
        if save_dir is None:
            # Use CURRENT_REPORT_DIR if available, otherwise default
            base_dir = CURRENT_REPORT_DIR if CURRENT_REPORT_DIR else self.output_dir
            save_dir = os.path.join(base_dir, 'equity_curves')
        
        os.makedirs(save_dir, exist_ok=True)
        
        logger.info(f"繪製 {len(strategies_to_plot)} 個篩選後策略的 equity curves 到 {save_dir}")
        
        current_results = self.results_summary if hasattr(self, 'results_summary') else self.results
        
        for strategy_name in strategies_to_plot:
            try:
                # 確保策略數據存在
                if strategy_name not in self.strategy_dfs:
                    logger.warning(f"找不到策略 {strategy_name} 的數據，跳過繪製")
                    continue
                
                df = self.strategy_dfs[strategy_name]
                
                # 確保圖表開始前關閉所有現有圖形
                plt.close('all')
                
                # 獲取策略配置
                strategy_config = None
                for s in self.strategies:
                    if s.get('name') == strategy_name:
                        strategy_config = s
                        break
                
                if strategy_config is None:
                    logger.warning(f"找不到策略 {strategy_name} 的配置，使用默認風格")
                    continue
                
                # 獲取策略信息
                model = strategy_config.get('model', '').replace('_', ' ').title()
                style = strategy_config.get('style', '').title()
                strategy_type = strategy_config.get('type', '').replace('_', ' ').title()
                window = strategy_config.get('x', '')
                threshold = strategy_config.get('y', '')
                
                # 獲取性能指標
                sr = current_results[strategy_name]['sr']
                ar = current_results[strategy_name]['ar']
                mdd = current_results[strategy_name]['mdd']
                cr = current_results[strategy_name]['cr']
                trades_per_year = current_results[strategy_name]['trades_per_year']
                
                # 轉換日期格式 - 確保所有時間索引正確
                if not isinstance(df.index, pd.DatetimeIndex):
                    try:
                        # 檢查是否存在't'列，如果有，先設置為索引
                        if 't' in df.columns and isinstance(df['t'].iloc[0], (pd.Timestamp, datetime)):
                            df = df.set_index('t')
                        else:
                            # 直接嘗試將當前索引轉換為時間格式
                            df.index = pd.to_datetime(df.index)
                        
                        # 再次檢查轉換後的索引格式
                        if not isinstance(df.index, pd.DatetimeIndex):
                            logger.warning(f"轉換後的索引仍然不是DatetimeIndex: {type(df.index)}")
                    except Exception as e:
                        logger.warning(f"無法將索引轉換為日期時間格式: {str(e)}")
                        
                    # 檢查確認索引是否已正確轉換
                    logger.info(f"策略 {strategy_name} 的時間索引類型: {type(df.index)}")
                    if len(df) > 0:
                        logger.info(f"策略 {strategy_name} 的時間範圍: {df.index[0]} 至 {df.index[-1]}")
                
                # 設置 walkforward 參數
                train_ratio = self.train_ratio  # 使用類的 train_ratio
                
                # 複製 backtesting.py 的設置
                time_variable = backtesting.get_time_variable(strategy_config.get('resolution', '24h'))
                shift_periods = backtesting.get_shift_periods(strategy_config.get('resolution', '24h'))
                
                # 分割數據為訓練集和測試集
                split_idx = int(len(df) * train_ratio)
                train_df = df.iloc[:split_idx].copy()
                test_df = df.iloc[split_idx:].copy()
                
                # 確保訓練集和測試集的索引類型
                if not isinstance(train_df.index, pd.DatetimeIndex):
                    try:
                        train_df.index = pd.to_datetime(train_df.index)
                    except Exception as e:
                        logger.warning(f"無法將訓練集索引轉換為日期時間格式: {str(e)}")
                        
                if not isinstance(test_df.index, pd.DatetimeIndex):
                    try:
                        test_df.index = pd.to_datetime(test_df.index)
                    except Exception as e:
                        logger.warning(f"無法將測試集索引轉換為日期時間格式: {str(e)}")
                
                # 記錄訓練集和測試集的時間範圍
                if len(train_df) > 0 and len(test_df) > 0:
                    logger.info(f"訓練集時間範圍: {train_df.index[0]} 至 {train_df.index[-1]}")
                    logger.info(f"測試集時間範圍: {test_df.index[0]} 至 {test_df.index[-1]}")
                
                # 計算訓練集性能指標
                train_df['train_pnl'] = train_df['pnl']
                train_df['train_cumu'] = train_df['train_pnl'].cumsum()
                train_pnl = train_df['train_pnl']  # 定義 train_pnl 變量
                train_sr = round(train_pnl.mean() / train_pnl.std() * np.sqrt(time_variable), 3) if train_pnl.std() != 0 else np.nan
                train_ar = round(train_pnl.mean() * time_variable, 3)
                train_dd = train_df['train_cumu'].cummax() - train_df['train_cumu']
                train_mdd = round(train_dd.max(), 3)
                train_cr = round(train_ar / train_mdd, 3) if train_mdd != 0 else np.nan
                
                # 計算訓練期交易次數
                train_trades = train_df['trade'].sum() if 'trade' in train_df.columns else 0
                train_trades_per_year = round(train_trades / (len(train_df) / time_variable), 1)
                
                # 計算測試集性能指標
                test_df['test_pnl'] = test_df['pnl']
                test_df['test_cumu'] = test_df['test_pnl'].cumsum()
                test_pnl = test_df['test_pnl']  # 定義 test_pnl 變量
                test_sr = round(test_pnl.mean() / test_pnl.std() * np.sqrt(time_variable), 3) if test_pnl.std() != 0 else np.nan
                test_ar = round(test_pnl.mean() * time_variable, 3)
                test_dd = test_df['test_cumu'].cummax() - test_df['test_cumu']
                test_mdd = round(test_dd.max(), 3)
                test_cr = round(test_ar / test_mdd, 3) if test_mdd != 0 else np.nan
                
                # 計算測試期交易次數
                test_trades = test_df['trade'].sum() if 'trade' in test_df.columns else 0
                test_trades_per_year = round(test_trades / (len(test_df) / time_variable), 1)
                
                # 為測試集創建一個由零開始的 Buy & Hold curve
                test_df['test_bnh_cumu'] = test_df['percentage_change'].cumsum()
                
                # 計算測試集 Buy & Hold 的性能指標
                test_bnh_pnl = test_df['percentage_change']
                test_bnh_sr = round(test_bnh_pnl.mean() / test_bnh_pnl.std() * np.sqrt(time_variable), 3) if test_bnh_pnl.std() != 0 else np.nan
                test_bnh_ar = round(test_bnh_pnl.mean() * time_variable, 3)
                test_bnh_dd = test_df['test_bnh_cumu'].cummax() - test_df['test_bnh_cumu']
                test_bnh_mdd = round(test_bnh_dd.max(), 3)
                test_bnh_cr = round(test_bnh_ar / test_bnh_mdd, 3) if test_bnh_mdd != 0 else np.nan
                
                # 計算全時段 Buy & Hold 的性能指標
                bnh_pnl = df['percentage_change']
                bnh_sr = round(bnh_pnl.mean() / bnh_pnl.std() * np.sqrt(time_variable), 3) if bnh_pnl.std() != 0 else np.nan
                bnh_ar = round(bnh_pnl.mean() * time_variable, 3)
                bnh_dd = df['bnh_cumu'].cummax() - df['bnh_cumu']
                bnh_mdd = round(bnh_dd.max(), 3)
                bnh_cr = round(bnh_ar / bnh_mdd, 3) if bnh_mdd != 0 else np.nan
                
                # 獲取完整數據集的 pnl 列作為計算
                pnl = df['pnl']  # 定義 pnl 變量
                
                # 計算 alpha, beta, r_squared 和 p-value
                alpha, beta, r_squared, alpha_p_value, beta_p_value = self.calculate_alpha_beta(df)
                
                # 計算訓練期和測試期的 alpha, beta, r_squared 和 p-value
                train_alpha, train_beta, train_r_squared, train_alpha_p_value, train_beta_p_value = self.calculate_alpha_beta(train_df)
                test_alpha, test_beta, test_r_squared, test_alpha_p_value, test_beta_p_value = self.calculate_alpha_beta(test_df)
                
                # 計算長短倉分量 (如果是長短倉策略)
                is_long_short = strategy_type.lower() == 'long short'
                
                if is_long_short:
                    # 創建長倉部分
                    df['long_pos'] = df['pos'].copy()
                    df.loc[df['long_pos'] < 0, 'long_pos'] = 0
                    df['long_pos'] = df['long_pos'] * LONG_LEVERAGE  # 應用長倉槓桿
                    df['long_pos_t-1'] = df['long_pos'].shift(shift_periods)
                    df['long_trade'] = abs(df['long_pos_t-1'] - df['long_pos'])
                    long_pnl = df['long_pos_t-1'] * df['percentage_change'] - df['long_trade'] * TRANSACTION_COST
                    df['long_pnl'] = long_pnl
                    df['long_cumu'] = long_pnl.cumsum()
                    
                    # 創建短倉部分
                    df['short_pos'] = df['pos'].copy()
                    df.loc[df['short_pos'] > 0, 'short_pos'] = 0
                    df['short_pos'] = df['short_pos'].abs() * SHORT_LEVERAGE  # 轉換為正值並應用槓桿
                    df['short_pos_t-1'] = df['short_pos'].shift(shift_periods)
                    df['short_trade'] = abs(df['short_pos_t-1'] - df['short_pos'])
                    short_pnl = df['short_pos_t-1'] * (-df['percentage_change']) - df['short_trade'] * TRANSACTION_COST
                    df['short_pnl'] = short_pnl
                    df['short_cumu'] = short_pnl.cumsum()
                    
                    # 更新總體 pnl 計算，結合長短倉（包含短倉槓桿）
                    df['pnl'] = long_pnl + short_pnl
                    df['cumu'] = df['pnl'].cumsum()
                
                # 計算性能指標
                sr = pnl.mean() / pnl.std() * np.sqrt(time_variable) if pnl.std() != 0 else np.nan
                ar = pnl.mean() * time_variable
                dd = df['cumu'].cummax() - df['cumu']
                mdd = dd.max()
                cr = ar / mdd if mdd != 0 else np.nan
                
                # 計算交易統計
                trades = df['trade'].sum()
                trades_per_year = trades / (len(df) / time_variable)
                
                performance_msg = f"策略 {strategy_name} 性能指標: SR={sr:.3f}, AR={ar:.3f}, MDD={mdd:.3f}, CR={cr:.3f}, 交易次數={trades:.0f}, Shift={shift_periods}"
                print(performance_msg) if self.use_multiprocessing else logger.info(performance_msg)
                
                # 將結果存入字典
                result = {
                    'strategy': strategy,
                    'sr': sr,
                    'ar': ar,
                    'mdd': mdd,
                    'cr': cr,
                    'trades': trades,
                    'trades_per_year': trades_per_year,
                    'shift_periods': shift_periods,
                    'resolution': resolution
                }
                
                return strategy_name, result, df
                
            except Exception as e:
                error_msg = f"運行策略 {strategy_name} 時出錯: {str(e)}"
                if self.use_multiprocessing:
                    print(error_msg)
                    import traceback
                    print(traceback.format_exc())
                else:
                    logger.error(error_msg)
                    logger.error(traceback.format_exc())
                return strategy_name, {'error': str(e)}, None

    def plot_portfolio(self, save_path=None):
        """
        繪製組合 equity curve，遵循 Portfolio_Sharpe.py 的邏輯
        並添加等權重計算作為參考
        
        參數:
            save_path (str): 保存路徑，如果為 None，使用默認路徑
        """
        if not self.strategy_dfs:
            logger.error("無可用策略數據，請先運行 run_all_strategies()")
            return None
            
        if save_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            weighting_method = self.portfolio_metrics['weighting_method'] if hasattr(self, 'portfolio_metrics') else "equal"
            save_path = os.path.join(self.output_dir, f"portfolio_{weighting_method}_{timestamp}.png")
        
        try:
            # 確保圖表開始前關閉所有現有圖形
            plt.close('all')
            
            # 設置字體和樣式，匹配 backtesting.py
            plt.rcParams.update({
                'font.family': 'DejaVu Sans',
                'font.size': 10,
                'lines.linewidth': 2,
                'axes.grid': True,
                'grid.alpha': 0.3,
                'axes.labelsize': 10
            })
            
            # === 遵循 Portfolio_Sharpe.py 的邏輯 ===
            # 根據時間框架分類策略
            hourly_dfs = []
            daily_dfs = []
            ten_min_dfs = []
            
            # 用於計算等權重數據
            equal_weighted_hourly_dfs = []
            equal_weighted_daily_dfs = []
            equal_weighted_ten_min_dfs = []
            
            # 分離不同時間框架的策略
            for name, result in self.results.items():
                if name in self.portfolio_weights:  # 只處理有權重的策略
                    df = self.strategy_dfs[name]
                    
                    # 獲取策略的時間框架
                    # 優先從result的strategy字典中獲取resolution
                    resolution = None
                    if 'strategy' in result and isinstance(result['strategy'], dict) and 'resolution' in result['strategy']:
                        resolution = result['strategy']['resolution']
                    else:
                        # 如果沒有，嘗試從results的頂層獲取
                        resolution = result.get('resolution', '24h')
                    
                    weight = self.portfolio_weights[name]
                    
                    # 確保 DataFrame 有正確的時間索引
                    if not isinstance(df.index, pd.DatetimeIndex):
                        try:
                            if 't' in df.columns:
                                df = df.set_index('t')
                            else:
                                df.index = pd.to_datetime(df.index)
                        except Exception as e:
                            logger.warning(f"無法將 {name} 的數據索引轉換為時間格式: {str(e)}")
                            continue
                    
                    # 複製 DataFrame 並添加權重標記
                    weighted_df = df.copy()
                    weighted_df['weight'] = weight
                    # 添加時間框架資訊到DataFrame
                    weighted_df['timeframe'] = resolution
                    
                    # 創建等權重的副本
                    equal_weighted_df = df.copy()
                    # 添加時間框架資訊到等權重DataFrame
                    equal_weighted_df['timeframe'] = resolution
                    
                    # 根據時間框架分類
                    if resolution == '1h':
                        hourly_dfs.append(weighted_df)
                        equal_weighted_hourly_dfs.append(equal_weighted_df)
                    elif resolution == '24h':
                        daily_dfs.append(weighted_df)
                        equal_weighted_daily_dfs.append(equal_weighted_df)
                    elif resolution == '10m':
                        ten_min_dfs.append(weighted_df)
                        equal_weighted_ten_min_dfs.append(equal_weighted_df)
                    else:
                        # 默認當作日線數據處理
                        logger.warning(f"策略 {name} 使用了未知的時間框架: {resolution}，將當作24h處理")
                        daily_dfs.append(weighted_df)
                        equal_weighted_daily_dfs.append(equal_weighted_df)
            
            # 計算等權重 - 所有策略權重相同
            n_strategies = len(hourly_dfs) + len(daily_dfs) + len(ten_min_dfs)
            equal_weight = 1.0 / n_strategies if n_strategies > 0 else 0
            
            # 輸出時間框架分類的結果確認
            logger.info(f"分類結果: 24h策略: {len(daily_dfs)}, 1h策略: {len(hourly_dfs)}, 10m策略: {len(ten_min_dfs)}")
            
            # 處理數據，收集所有 PnL 數據（按日重採樣）
            daily_pnl_list = []
            daily_bnh_pnl_list = []
            daily_pos_list = []
            
            # 等權重數據
            equal_daily_pnl_list = []
            equal_daily_pos_list = []
            
            all_dates = set()
            
            # 處理小時數據 - 原權重
            for df in hourly_dfs:
                # 乘以權重
                weight = df['weight'].iloc[0]
                df['pnl'] = df['pnl'] * weight
                
                # 按日重採樣
                daily_df = df['pnl'].resample('D').sum()
                if not daily_df.empty:
                    daily_pnl_list.append(daily_df)
                    all_dates.update(daily_df.index)
                
                # Buy & Hold 數據
                if 'percentage_change' in df.columns:
                    daily_bnh_df = df['percentage_change'].resample('D').sum()
                    if not daily_bnh_df.empty:
                        daily_bnh_pnl_list.append(daily_bnh_df)
                        all_dates.update(daily_bnh_df.index)
                
                # 倉位數據
                if 'pos' in df.columns:
                    daily_pos_df = df['pos'].resample('D').mean() * weight  # 加權倉位
                    if not daily_pos_df.empty:
                        daily_pos_list.append(daily_pos_df)
                        all_dates.update(daily_pos_df.index)
            
            # 處理小時數據 - 等權重
            for df in equal_weighted_hourly_dfs:
                # 使用等權重
                df['pnl'] = df['pnl'] * equal_weight
                
                # 按日重採樣
                daily_df = df['pnl'].resample('D').sum()
                if not daily_df.empty:
                    equal_daily_pnl_list.append(daily_df)
                    all_dates.update(daily_df.index)
                
                # 倉位數據
                if 'pos' in df.columns:
                    daily_pos_df = df['pos'].resample('D').mean() * equal_weight
                    if not daily_pos_df.empty:
                        equal_daily_pos_list.append(daily_pos_df)
                        all_dates.update(daily_pos_df.index)
            
            # 處理日線數據 - 原權重
            for df in daily_dfs:
                weight = df['weight'].iloc[0]
                df['pnl'] = df['pnl'] * weight
                
                daily_df = df['pnl']
                if not daily_df.empty:
                    daily_pnl_list.append(daily_df)
                    all_dates.update(daily_df.index)
                
                # Buy & Hold 數據
                if 'percentage_change' in df.columns:
                    daily_bnh_df = df['percentage_change']
                    if not daily_bnh_df.empty:
                        daily_bnh_pnl_list.append(daily_bnh_df)
                        all_dates.update(daily_bnh_df.index)
                
                # 倉位數據
                if 'pos' in df.columns:
                    daily_pos_df = df['pos'] * weight  # 加權倉位
                    if not daily_pos_df.empty:
                        daily_pos_list.append(daily_pos_df)
                        all_dates.update(daily_pos_df.index)
            
            # 處理日線數據 - 等權重
            for df in equal_weighted_daily_dfs:
                # 使用等權重
                df['pnl'] = df['pnl'] * equal_weight
                
                daily_df = df['pnl']
                if not daily_df.empty:
                    equal_daily_pnl_list.append(daily_df)
                    all_dates.update(daily_df.index)
                
                # 倉位數據
                if 'pos' in df.columns:
                    daily_pos_df = df['pos'] * equal_weight
                    if not daily_pos_df.empty:
                        equal_daily_pos_list.append(daily_pos_df)
                        all_dates.update(daily_pos_df.index)
            
            # 處理10分鐘數據 - 原權重
            for df in ten_min_dfs:
                weight = df['weight'].iloc[0]
                df['pnl'] = df['pnl'] * weight
                
                # 按日重採樣
                daily_df = df['pnl'].resample('D').sum()
                if not daily_df.empty:
                    daily_pnl_list.append(daily_df)
                    all_dates.update(daily_df.index)
                
                # Buy & Hold 數據
                if 'percentage_change' in df.columns:
                    daily_bnh_df = df['percentage_change'].resample('D').sum()
                    if not daily_bnh_df.empty:
                        daily_bnh_pnl_list.append(daily_bnh_df)
                        all_dates.update(daily_bnh_df.index)
                
                # 倉位數據
                if 'pos' in df.columns:
                    daily_pos_df = df['pos'].resample('D').mean() * weight  # 加權倉位
                    if not daily_pos_df.empty:
                        daily_pos_list.append(daily_pos_df)
                        all_dates.update(daily_pos_df.index)
                        
            # 處理10分鐘數據 - 等權重
            for df in equal_weighted_ten_min_dfs:
                # 使用等權重
                df['pnl'] = df['pnl'] * equal_weight
                
                # 按日重採樣
                daily_df = df['pnl'].resample('D').sum()
                if not daily_df.empty:
                    equal_daily_pnl_list.append(daily_df)
                    all_dates.update(daily_df.index)
                
                # 倉位數據
                if 'pos' in df.columns:
                    daily_pos_df = df['pos'].resample('D').mean() * equal_weight
                    if not daily_pos_df.empty:
                        equal_daily_pos_list.append(daily_pos_df)
                        all_dates.update(daily_pos_df.index)
            
            # 確保有數據可用
            if not daily_pnl_list:
                logger.error("無可用的每日 PnL 數據")
                return None
            
            # 確保所有數據都跨越相同的日期範圍
            if all_dates:
                all_dates = sorted(all_dates)
                date_range = pd.date_range(start=min(all_dates), end=max(all_dates), freq='D')
                
                # 重新索引所有數據
                for i in range(len(daily_pnl_list)):
                    daily_pnl_list[i] = daily_pnl_list[i].reindex(date_range).fillna(0)
                
                for i in range(len(daily_bnh_pnl_list)):
                    daily_bnh_pnl_list[i] = daily_bnh_pnl_list[i].reindex(date_range).fillna(0)
                
                for i in range(len(daily_pos_list)):
                    daily_pos_list[i] = daily_pos_list[i].reindex(date_range).fillna(0)
                
                # 等權重數據
                for i in range(len(equal_daily_pnl_list)):
                    equal_daily_pnl_list[i] = equal_daily_pnl_list[i].reindex(date_range).fillna(0)
                
                for i in range(len(equal_daily_pos_list)):
                    equal_daily_pos_list[i] = equal_daily_pos_list[i].reindex(date_range).fillna(0)
            
            # 原權重組合
            portfolio_daily = pd.concat(daily_pnl_list).groupby(level=0).sum().to_frame(name='pnl')
            portfolio_daily['cumu'] = portfolio_daily['pnl'].cumsum()
            portfolio_daily['dd'] = portfolio_daily['cumu'].cummax() - portfolio_daily['cumu']
            
            # 等權重組合
            if equal_daily_pnl_list:
                equal_portfolio_daily = pd.concat(equal_daily_pnl_list).groupby(level=0).sum().to_frame(name='pnl')
                equal_portfolio_daily['cumu'] = equal_portfolio_daily['pnl'].cumsum()
                equal_portfolio_daily['dd'] = equal_portfolio_daily['cumu'].cummax() - equal_portfolio_daily['cumu']
            else:
                equal_portfolio_daily = pd.DataFrame(index=portfolio_daily.index)
                equal_portfolio_daily['cumu'] = 0
            
            # 處理 Buy & Hold 數據
            if daily_bnh_pnl_list:
                portfolio_bnh_daily = pd.concat(daily_bnh_pnl_list).groupby(level=0).mean().to_frame(name='pnl')
                portfolio_bnh_daily['cumu'] = portfolio_bnh_daily['pnl'].cumsum()
                portfolio_bnh_daily['dd'] = portfolio_bnh_daily['cumu'].cummax() - portfolio_bnh_daily['cumu']
            else:
                # 如果沒有 Buy & Hold 數據，創建一個空的 DataFrame
                portfolio_bnh_daily = pd.DataFrame(index=portfolio_daily.index)
                portfolio_bnh_daily['cumu'] = 0
            
            # 處理倉位數據 - 原權重
            if daily_pos_list:
                portfolio_daily['pos'] = pd.concat(daily_pos_list).groupby(level=0).sum()
            else:
                portfolio_daily['pos'] = 0
            
            # 處理倉位數據 - 等權重
            if equal_daily_pos_list:
                equal_portfolio_daily['pos'] = pd.concat(equal_daily_pos_list).groupby(level=0).sum()
            else:
                equal_portfolio_daily['pos'] = 0
            
            # 計算性能指標
            time_variable = 365  # 年化因子
            
            # 原權重指標
            daily_return_mean = portfolio_daily['pnl'].mean()
            daily_return_std = portfolio_daily['pnl'].std()
            
            sr = daily_return_mean / daily_return_std * np.sqrt(time_variable) if daily_return_std != 0 else np.nan
            ar = daily_return_mean * time_variable
            mdd = portfolio_daily['dd'].max()
            cr = ar / mdd if mdd != 0 else np.nan
            
            # 等權重指標
            if 'pnl' in equal_portfolio_daily.columns and not equal_portfolio_daily['pnl'].empty:
                equal_daily_return_mean = equal_portfolio_daily['pnl'].mean()
                equal_daily_return_std = equal_portfolio_daily['pnl'].std()
                
                sr_equal = equal_daily_return_mean / equal_daily_return_std * np.sqrt(time_variable) if equal_daily_return_std != 0 else np.nan
                ar_equal = equal_daily_return_mean * time_variable
                mdd_equal = equal_portfolio_daily['dd'].max()
                cr_equal = ar_equal / mdd_equal if mdd_equal != 0 else np.nan
            else:
                sr_equal = np.nan
                ar_equal = np.nan
                mdd_equal = np.nan
                cr_equal = np.nan
            
            # Buy & Hold 指標
            if 'pnl' in portfolio_bnh_daily.columns and not portfolio_bnh_daily['pnl'].empty:
                daily_bnh_return_mean = portfolio_bnh_daily['pnl'].mean()
                daily_bnh_return_std = portfolio_bnh_daily['pnl'].std()
                
                sr_bnh = daily_bnh_return_mean / daily_bnh_return_std * np.sqrt(time_variable) if daily_bnh_return_std != 0 else np.nan
                ar_bnh = daily_bnh_return_mean * time_variable
                mdd_bnh = portfolio_bnh_daily['dd'].max() if 'dd' in portfolio_bnh_daily.columns else 0
                cr_bnh = ar_bnh / mdd_bnh if mdd_bnh != 0 else np.nan
            else:
                sr_bnh = np.nan
                ar_bnh = np.nan
                mdd_bnh = np.nan
                cr_bnh = np.nan
            
            # 創建 2x1 布局的圖表
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), 
                                        gridspec_kw={'height_ratios': [3, 1], 'hspace': 0.05})
            
            # 主圖 - 繪製累積收益曲線
            # 原權重策略
            ax1.plot(portfolio_daily.index, portfolio_daily['cumu'], 
                   label=f'Sharpe Weighting (SR={sr:.3f}, AR={ar:.3f}, MDD={mdd:.3f}, CR={cr:.3f})', 
                   color='blue', linewidth=2)
            
            # 等權重策略
            ax1.plot(equal_portfolio_daily.index, equal_portfolio_daily['cumu'], 
                   label=f'Equal Weighting (SR={sr_equal:.3f}, AR={ar_equal:.3f}, MDD={mdd_equal:.3f}, CR={cr_equal:.3f})', 
                   color='green', linewidth=2)
            
            # Buy & Hold
            if 'cumu' in portfolio_bnh_daily.columns:
                ax1.plot(portfolio_bnh_daily.index, portfolio_bnh_daily['cumu'], 
                       label=f'Buy & Hold (SR={sr_bnh:.3f}, AR={ar_bnh:.3f}, MDD={mdd_bnh:.3f}, CR={cr_bnh:.3f})', 
                       color='gray', linestyle='--', linewidth=1.5)
            
            # 設置主圖網格和標籤
            ax1.grid(True, alpha=0.3)
            ax1.set_ylabel('Cumulative Return', fontsize=10)
            
            # 調整 y 軸，確保顯示完整
            all_cumu_values = []
            if 'cumu' in portfolio_daily.columns:
                all_cumu_values.extend(portfolio_daily['cumu'].tolist())
            if 'cumu' in equal_portfolio_daily.columns:
                all_cumu_values.extend(equal_portfolio_daily['cumu'].tolist())
            if 'cumu' in portfolio_bnh_daily.columns:
                all_cumu_values.extend(portfolio_bnh_daily['cumu'].tolist())
            
            # 過濾 NaN 值
            all_cumu_values = [v for v in all_cumu_values if not pd.isna(v)]
            
            if all_cumu_values:
                y_min = min(min(all_cumu_values), 0)  # 確保 y 軸至少從 0 開始
                y_max = max(all_cumu_values)
                # 擴大範圍
                y_range = y_max - y_min
                y_min = y_min - y_range * 0.1
                y_max = y_max + y_range * 0.1
                ax1.set_ylim(y_min, y_max)
            
            # 底圖 - 繪製倉位
            # 原權重倉位
            if 'pos' in portfolio_daily.columns:
                ax2.plot(portfolio_daily.index, portfolio_daily['pos'], 
                       label='Sharpe Weighting Position', color='blue', linewidth=1.5)
            
            # 等權重倉位
            if 'pos' in equal_portfolio_daily.columns:
                ax2.plot(equal_portfolio_daily.index, equal_portfolio_daily['pos'], 
                       label='Equal Weighting Position', color='green', linewidth=1.5, linestyle='--')
            
            ax2.set_ylabel('Position', fontsize=10)
            ax2.set_xlabel('Date', fontsize=10)
            ax2.grid(True, alpha=0.3)
            
            # 設置倉位圖例
            ax2.legend(loc='upper left', fontsize=8)
            
            # 主圖添加圖例
            ax1.legend(loc='upper left', fontsize=9)
            
            # 隱藏主圖 x 軸標籤，只在底部顯示
            plt.setp(ax1.get_xticklabels(), visible=False)
            
            # 共享 x 軸
            ax2.sharex(ax1)
            
            # 設置 x 軸日期格式
            if len(portfolio_daily) > 365:  # 如果數據超過一年
                ax2.xaxis.set_major_locator(mdates.YearLocator())
                ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
                ax2.xaxis.set_minor_locator(mdates.MonthLocator((1, 4, 7, 10)))  # 每季度第一個月
            else:
                ax2.xaxis.set_major_locator(mdates.MonthLocator())
                ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            
            # 設置標題
            asset_symbol = self.asset_info.get('glassnode_symbol', 'Unknown')
            weighting_method = self.portfolio_metrics['weighting_method'].capitalize() if hasattr(self, 'portfolio_metrics') else "Equal"
            
            # 獲取策略總數 - 改為使用實際參與組合的策略數量，而不是總策略數量
            n_strategies = len(self.portfolio_weights)
            
            title = f"Portfolio Equity Curve - {asset_symbol} ({weighting_method} Weighting) - {n_strategies} Strategies"
            
            # 添加策略數量和時間框架統計
            hourly_count = len(hourly_dfs)
            daily_count = len(daily_dfs)
            ten_min_count = len(ten_min_dfs)
            
            tf_info = f"Strategies: {n_strategies} | Timeframes: 24h: {daily_count}, 1h: {hourly_count}, 10m: {ten_min_count}"
            title += f"\n{tf_info}"
            
            # 添加各時間框架的 shift 值
            daily_shift = backtesting.get_shift_periods('24h')
            hourly_shift = backtesting.get_shift_periods('1h') 
            ten_min_shift = backtesting.get_shift_periods('10m')
            
            shift_info = f"Shift periods: 24h = {daily_shift}, 1h = {hourly_shift}, 10m = {ten_min_shift}"
            title += f"\n{shift_info}"
            
            # 調整布局，確保沒有重疊
            fig.tight_layout()
            
            # 先調整 top 留出標題空間
            fig.subplots_adjust(top=0.85)  # 將 top 值從0.85降低到0.80，為標題預留更多空間
            
            # 設置標題，調整 y 位置配合 top 空間
            fig.suptitle(title, fontsize=12, y=0.92)  # 將 y 從0.92調整到0.90，與圖表1拉開更大距離
            
            # 自動格式化 x 軸日期標籤，避免重疊
            plt.gcf().autofmt_xdate()
            
            # 保存圖表
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            # 更新 portfolio_df 和 portfolio_metrics
            self.portfolio_df = portfolio_daily
            
            # 將等權重結果也保存到 portfolio_metrics
            self.portfolio_metrics.update({
                'sr': sr,
                'ar': ar,
                'mdd': mdd,
                'cr': cr,
                'sr_bnh': sr_bnh,
                'ar_bnh': ar_bnh,
                'mdd_bnh': mdd_bnh,
                'cr_bnh': cr_bnh,
                'sr_equal': sr_equal,
                'ar_equal': ar_equal,
                'mdd_equal': mdd_equal,
                'cr_equal': cr_equal
            })
            
            logger.info(f"已保存組合 equity curve 到 {save_path}")
            
            # 返回保存路徑
            return save_path
            
        except Exception as e:
            logger.error(f"繪製組合 equity curve 時出錯: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            plt.close('all')  # 確保發生錯誤時也關閉圖形
            return None

    def plot_weight_distribution(self, save_path=None):
        """
        繪製權重分佈餅圖

        參數:
            save_path (str): 保存路徑，如果為 None，使用默認路徑
        """
        if not hasattr(self, 'portfolio_weights') or not self.portfolio_weights:
            logger.error("無可用權重數據，請先運行 generate_portfolio()")
            return None # Return None instead of nothing

        if save_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            # Make sure portfolio_metrics exists
            weighting_method = self.portfolio_metrics.get('weighting_method', 'equal') if hasattr(self, 'portfolio_metrics') else 'equal'
            # Use CURRENT_REPORT_DIR if available
            base_dir = CURRENT_REPORT_DIR if CURRENT_REPORT_DIR else self.output_dir
            save_path = os.path.join(base_dir, f"weights_{weighting_method}_{timestamp}.png")

        # 只顯示權重大於 1% 的策略，其他合併為 'Others'
        weights_to_plot = {}
        others_weight = 0.0
        for name, weight in self.portfolio_weights.items():
            if weight >= 0.01:  # 1%
                weights_to_plot[name] = weight
            else:
                others_weight += weight

        if others_weight >= 0.01: # Only add 'Others' if its weight is significant
             weights_to_plot['Others'] = others_weight

        if not weights_to_plot: # Handle case where all weights are < 1%
             logger.warning("所有策略權重均小於1%，餅圖可能為空")
             # Optionally plot all weights if none are >= 1%
             if self.portfolio_weights:
                  weights_to_plot = self.portfolio_weights
             else:
                  return None # Cannot plot if original weights are also empty


        plt.figure(figsize=(10, 8))

        # 創建綠色系列調色板
        num_colors = len(weights_to_plot)
        greens = plt.cm.Greens(np.linspace(0.4, 0.9, num_colors))

        # 繪製餅圖
        wedges, texts, autotexts = plt.pie(
            weights_to_plot.values(),
            labels=weights_to_plot.keys(),
            autopct='%1.1f%%',
            startangle=90,
            colors=greens,
            wedgeprops={'edgecolor': 'w', 'linewidth': 1}
        )

        # 調整文字大小和顏色
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontsize(9)

        # 設置圖表
        weighting_method = self.portfolio_metrics.get('weighting_method', 'equal').capitalize() if hasattr(self, 'portfolio_metrics') else 'Equal'
        plt.title(f"Portfolio Weight Distribution - {weighting_method} Weighting")
        plt.axis('equal')  # 確保餅圖是圓形的

        # 保存圖表
        try:
             plt.savefig(save_path, dpi=300, bbox_inches='tight')
             plt.close()
             logger.info(f"已保存權重分佈圖到 {save_path}")
             return save_path # Return path on success
        except Exception as e:
             logger.error(f"保存權重分佈圖時出錯: {str(e)}")
             plt.close()
             return None # Return None on error

    def generate_strategy_heatmaps(self):
        """為每個策略生成熱力圖，保存到 heatmap 目錄"""
        if not self.generate_strategy_heatmaps:
            logger.info("策略熱力圖生成功能未開啟，跳過")
            return
            
        logger.info("開始生成策略熱力圖...")
        
        # 創建 heatmap 目錄
        heatmap_dir = os.path.join(self.output_dir, 'heatmap')
        os.makedirs(heatmap_dir, exist_ok=True)
        logger.info(f"熱力圖輸出目錄: {heatmap_dir}")
        
        # 使用 backtesting.SAVE_FIGURES 原始設置的備份
        original_save_figures = backtesting.SAVE_FIGURES
        
        # 修改 backtesting.SAVE_FIGURES 為 True，確保保存圖片
        backtesting.SAVE_FIGURES = True
        
        # 存儲原始的 backtesting 全局變量
        original_factor_name = getattr(backtesting, 'factor_name', 'Unknown Factor')
        original_metric_key = getattr(backtesting, 'metric_key', None)
        original_underlying = getattr(backtesting, 'underlying', 'Unknown')
        original_resolution = getattr(backtesting, 'resolution', '24h')
        
        # 刪除任何現有的 backtest_result 目錄，避免干擾
        backup_backtest_result = None
        if os.path.exists('backtest_result'):
            backup_time = int(time.time())
            backup_backtest_result = f'backtest_result_backup_{backup_time}'
            os.rename('backtest_result', backup_backtest_result)
            logger.info(f"備份現有的 backtest_result 目錄到 {backup_backtest_result}")
        
        # 創建新的 backtest_result 目錄
        os.makedirs('backtest_result', exist_ok=True)
        
        # 記錄成功生成的熱力圖數量
        success_count = 0
        
        # 為每個策略生成熱力圖
        for i, strategy_config in enumerate(self.strategies):
            strategy_name = strategy_config.get('name', f"Strategy_{i}")
            logger.info(f"處理策略 {strategy_name} ({i+1}/{len(self.strategies)})")
            
            # 檢查策略數據是否可用
            if not strategy_name in self.strategy_dfs:
                logger.warning(f"策略 {strategy_name} 缺少數據，跳過熱力圖生成")
                continue
                
            # 獲取策略參數
            model = strategy_config.get('model', 'Unknown')
            strategy_type = strategy_config.get('type', 'long_only').lower()
            style = strategy_config.get('style', 'momentum').lower()
            x_value = strategy_config.get('x', 0) 
            y_value = strategy_config.get('y', 0.0)
            api = strategy_config.get('api', '')
            metric_key = strategy_config.get('metric_key', '')
            resolution = strategy_config.get('resolution', '24h')
            asset = strategy_config.get('symbol', 'BTC')
            api_symbol = strategy_config.get('api_symbol', 'BTC')
            
            # 檢查必要的參數是否齊全
            if not (model and strategy_type and style and x_value and y_value and api):
                logger.warning(f"策略 {strategy_name} 缺少必要參數，跳過熱力圖生成")
                continue
            
            # 從 API 路徑提取因子名稱
            factor_name = api.split('/')[-1].replace('_', ' ').title() if api else 'Unknown Factor'
            
            # 設置 backtesting 模塊的全局變量以便正確生成熱力圖
            backtesting.factor_name = factor_name
            backtesting.metric_key = metric_key
            backtesting.underlying = api_symbol
            backtesting.resolution = resolution
            
            # 從 models.py 獲取基礎參數
            try:
                base_params = backtesting.get_base_params()
            except Exception as e:
                logger.error(f"無法獲取基礎參數: {str(e)}")
                continue
                
            strategy_func = None
            window_range = []
            threshold_range = []
            
            # 查找對應的策略函數和參數範圍
            for name, params in base_params.items():
                model_clean = model.lower().replace(' ', '_').replace('-', '_')
                name_clean = name.lower().replace(' ', '_').replace('-', '_')
                
                if model_clean == name_clean:
                    strategy_func = params['func']
                    
                    # 獲取窗口範圍
                    # 創建以當前策略 x 參數為中心的窗口範圍
                    window_center = int(x_value)
                    window_step = params['base_window'][2] if len(params['base_window']) > 2 else 1
                    if window_step <= 0:
                        window_step = 1
                    
                    # 創建窗口範圍 - 確保範圍足夠寬以在熱力圖上顯示有用的信息
                    # 默認在當前值的基礎上增減50%，最少5個點
                    window_min = max(1, int(window_center * 0.5))
                    window_max = int(window_center * 1.5)
                    num_points = max(5, (window_max - window_min) // window_step)
                    
                    window_range = np.linspace(window_min, window_max, num_points).astype(int)
                    
                    # 獲取閾值範圍
                    # 創建以當前策略 y 參數為中心的閾值範圍
                    threshold_center = float(y_value)
                    
                    if name == 'MA Cross':
                        # MA Cross 模型的閾值是基於 get_window_range 函數
                        threshold_base = params['base_threshold']
                        threshold_min = max(1, int(threshold_center * 0.5))
                        threshold_max = int(threshold_center * 1.5)
                        threshold_step = threshold_base[2] if len(threshold_base) > 2 else 1
                        num_points = max(5, (threshold_max - threshold_min) // threshold_step)
                        threshold_range = np.linspace(threshold_min, threshold_max, num_points).astype(int)
                    else:
                        # 對於其他模型，我們使用已定義的 threshold_range
                        threshold_range_orig = params['threshold_range']
                        
                        # 如果原始範圍太小，擴展它
                        if len(threshold_range_orig) < 3:
                            # 默認在當前值的基礎上增減0.5
                            threshold_min = max(0.0, threshold_center - 0.5)
                            threshold_max = threshold_center + 0.5
                            threshold_range = np.linspace(threshold_min, threshold_max, 10)
                        else:
                            threshold_range = threshold_range_orig
                    
                    break
            
            if not strategy_func:
                logger.warning(f"找不到策略 {strategy_name} 對應的模型函數 (model: {model})，跳過熱力圖生成")
                continue
                
            # 執行策略優化以獲取熱力圖數據
            logger.info(f"為策略 {strategy_name} 生成熱力圖 (模型: {model}, 類型: {strategy_type}, 風格: {style})")
            
            try:
                # 取得策略數據
                df = self.strategy_dfs[strategy_name].copy()
                
                # 確保 DataFrame 包含必要的欄位
                if 'value' not in df.columns or 'price' not in df.columns:
                    logger.warning(f"策略 {strategy_name} 數據缺少必要欄位，跳過熱力圖生成")
                    continue
                
                # 修改 backtesting.df 用於 backtesting.optimize_strategy
                backtesting.df = df
                backtesting.time_variable = backtesting.get_time_variable(resolution)
                backtesting.SHIFT_PERIODS = backtesting.get_shift_periods(resolution)
                
                # 標準化策略類型和風格
                strategy_type_mapping = {
                    'long only': 'long_only',
                    'long-only': 'long_only',
                    'short only': 'short_only',
                    'short-only': 'short_only',
                    'long short': 'long_short',
                    'long-short': 'long_short'
                }
                
                style_mapping = {
                    'mom': 'momentum',
                    'rev': 'reversion'
                }
                
                norm_strategy_type = strategy_type_mapping.get(strategy_type, strategy_type)
                norm_style = style_mapping.get(style, style)
                
                # 執行優化以生成熱力圖數據
                result_df = backtesting.optimize_strategy(
                    strategy_func,
                    window_range,
                    threshold_range,
                    norm_strategy_type,
                    norm_style
                ).sort_values('sr', ascending=False)
                
                if result_df.empty:
                    logger.warning(f"策略 {strategy_name} 生成的優化結果為空，跳過熱力圖生成")
                    continue
                
                # 創建結果字典適合 plot_strategy_heatmaps
                results = {model: result_df}
                
                # 設置熱力圖標題
                style_title = style.title()
                strategy_type_title = strategy_type.title().replace('_', ' ')
                title = f"{style_title} {strategy_type_title}"
                
                # 使用 backtesting.py 中的函數生成熱力圖
                backtesting.plot_strategy_heatmaps(results, title, print_summary=False)
                
                # 將生成的熱力圖文件從 backtest_result 移動到 heatmap 目錄
                factor_dir = f"backtest_result/{factor_name.replace(' ', '_')}"
                png_files_found = 0
                
                if os.path.exists(factor_dir):
                    for file in os.listdir(factor_dir):
                        if file.endswith('.png'):
                            png_files_found += 1
                            # 創建一個更有描述性的文件名
                            # 格式: 策略名稱_資產_解析度_模型_類型_風格_窗口_閾值.png
                            sanitized_strategy_name = strategy_name.replace('/', '_').replace('\\', '_')
                            metric_suffix = f"_{metric_key}" if metric_key and metric_key != "/" else ""
                            
                            new_filename = f"{sanitized_strategy_name}_{api_symbol}_{resolution}{metric_suffix}_{model}_{norm_strategy_type}_{norm_style}_w{x_value}_t{y_value}.png"
                            
                            src_path = os.path.join(factor_dir, file)
                            dst_path = os.path.join(heatmap_dir, new_filename)
                            
                            try:
                                shutil.copy2(src_path, dst_path)
                                logger.info(f"策略 {strategy_name} 熱力圖已保存: {dst_path}")
                                success_count += 1
                            except Exception as e:
                                logger.error(f"複製熱力圖文件時出錯: {str(e)}")
                                continue
                
                if png_files_found == 0:
                    logger.warning(f"未找到策略 {strategy_name} 的熱力圖文件，檢查 {factor_dir} 目錄")
                    
                # 清理本次生成的臨時文件
                if os.path.exists(factor_dir):
                    try:
                        shutil.rmtree(factor_dir)
                    except Exception as e:
                        logger.warning(f"清理臨時目錄 {factor_dir} 時出錯: {str(e)}")
                
            except Exception as e:
                logger.error(f"生成策略 {strategy_name} 熱力圖時出錯: {str(e)}")
                logger.error(traceback.format_exc())
                continue
        
        # 清理 backtest_result 目錄
        try:
            if os.path.exists('backtest_result'):
                shutil.rmtree('backtest_result')
                logger.info("已清理臨時目錄 backtest_result")
            
            # 恢復備份的 backtest_result 目錄
            if backup_backtest_result and os.path.exists(backup_backtest_result):
                os.rename(backup_backtest_result, 'backtest_result')
                logger.info(f"已恢復備份的 backtest_result 目錄")
        except Exception as e:
            logger.warning(f"清理或恢復 backtest_result 目錄時出錯: {str(e)}")
        
        # 恢復 backtesting 的原始設置
        backtesting.SAVE_FIGURES = original_save_figures
        backtesting.factor_name = original_factor_name
        backtesting.metric_key = original_metric_key
        backtesting.underlying = original_underlying
        backtesting.resolution = original_resolution
        
        logger.info(f"策略熱力圖生成完成，共成功生成 {success_count} 個熱力圖，嘗試處理 {len(self.strategies)} 個策略")
        return heatmap_dir

