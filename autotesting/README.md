# Glassnode Portfolio Backtester

A comprehensive backtest tool for cryptocurrency trading strategies based on Glassnode data.

## Overview

This tool allows you to:
- Backtest multiple Glassnode metric-based trading strategies
- Combine strategies into optimized portfolios
- Generate detailed performance reports with visualizations
- Filter strategies using rolling Sharpe ratio analysis
- Apply leverage to long and short positions

## Installation

### Prerequisites

- Python 3.7+
- Glassnode API key

### Setup

1. Clone the repository:
```bash
git clone <repository_url>
cd glassnode_backtest_all_v17
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set your Glassnode API key as an environment variable:
```bash
export GLASSNODE_API_KEY="your_api_key_here"
```

## Usage

The basic workflow is:

1. Create a configuration Excel file (`config_summary.xlsx`)
2. Run the backtest
3. Review the generated reports and visualizations

### Configuration File

Create an Excel file (`config_summary.xlsx`) with columns:

- `API`: Glassnode API endpoint
- `Metric Key`: Specific metric to retrieve
- `Resolution`: Data resolution (24h, 1h, 10m)
- `API Symbol`: Symbol for the API call
- `Strategy Name`: Unique name for the strategy
- `x`, `y`: Parameters for strategy calculation
- `Asset`: Trading asset (e.g., BTC)
- `Model`: Strategy model type
- `Style`: 'momentum' or 'mean_reversion'
- `Type`: 'long_only', 'short_only', or 'long_short'

### Running Backtests

Basic usage:
```bash
python main.py
```

With custom options:
```bash
python main.py --config config_summary.xlsx --weight-method sharpe --min-sr 0.5
```

### Command Line Options

- `--config`: Configuration file path (default: config_summary.xlsx)
- `--output-dir`: Output directory for reports
- `--weight-method`: Portfolio weighting method (equal, sharpe, calmar, sortino)
- `--min-sr`: Minimum Sharpe ratio threshold for strategies
- `--rolling-sharpe-filter`: Use rolling Sharpe ratio filtering
- `--min-start-date`: Minimum start date for strategy data (YYYY-MM-DD)
- `--long-leverage`: Leverage multiplier for long positions
- `--short-leverage`: Leverage multiplier for short positions
- `--generate-individual-curves`: Generate equity curves for individual strategies
- `--use-multiprocessing`: Enable multiprocessing
- `--max-workers`: Maximum number of worker processes
- `--threshold-avg-sr`: Threshold for average rolling Sharpe ratio
- `--threshold-recent-sr`: Threshold for recent period rolling Sharpe ratio
- `--recent-period-months`: Number of months for "recent" period definition

## Output

The tool generates a comprehensive report directory with:

- Performance metrics
- Equity curves
- Heatmaps for strategy optimization
- Weight distribution charts
- Detailed log files
- Strategy performance tables in Excel format

## Advanced Features

### Data Caching

The tool uses local caching for Glassnode data to avoid redundant API calls:
- Cache directory: `data/glassnode_cache`
- Cache validity: 7 days

### Strategy Filtering

Strategies can be filtered based on their rolling Sharpe ratio performance:
- Average rolling Sharpe ratio across the entire period
- Recent period rolling Sharpe ratio
- Configurable thresholds and recent period length

## Example Usage

1. Prepare your `config_summary.xlsx` with strategy definitions
2. Run:
```bash
python main.py --weight-method sharpe --min-sr 0.5 --rolling-sharpe-filter
```
3. Review the generated report in the `portfolio_results/report_TIMESTAMP` directory

## Notes

- Transaction cost is set to 0.05% by default
- Default train/test ratio is 0.7/0.3
- Log files are stored in the report directory for debugging 