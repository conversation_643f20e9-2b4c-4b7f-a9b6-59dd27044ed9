"""
Bybit API integration module for cryptocurrency backtesting and trading.
This module provides a class-based interface for interacting with Bybit's API.
"""

import time
import hmac
import hashlib
import urllib.parse
import os
import pandas as pd
import requests
import ccxt
from datetime import datetime

class BybitAPI:
    """Class for interacting with Bybit API."""
    
    def __init__(self, api_key=None, api_secret=None, vip_level='DEFAULT', is_testnet=False):
        """
        Initialize the Bybit API client.
        
        Args:
            api_key: Bybit API key
            api_secret: Bybit API secret
            vip_level: VIP level for rate limiting ('DEFAULT', 'VIP1', 'VIP2', etc.)
            is_testnet: Whether to use testnet (default: False)
        """
        self.api_key = api_key
        self.api_secret = api_secret
        self.vip_level = vip_level
        self.is_testnet = is_testnet
        self.exchange = None
        
        # Rate limit settings by VIP level (requests per minute)
        self.rate_limits = {
            'DEFAULT': 120,  # Regular users
            'VIP1': 240,     # VIP 1
            'VIP2': 300,     # VIP 2
            'VIP3': 360,     # VIP 3
            'VIP4': 420,     # VIP 4
            'VIP5': 480      # VIP 5
        }
        
        # Calculate appropriate rate limit pause based on VIP level (seconds between requests)
        rate_limit_per_minute = self.rate_limits.get(self.vip_level, self.rate_limits['DEFAULT'])
        self.rate_limit_pause = 60.0 / rate_limit_per_minute  # Convert to seconds between requests
        
        if api_key and api_secret:
            self.initialize_exchange()
            
    @classmethod
    def from_credentials(cls, credentials, vip_level='DEFAULT'):
        """
        Create a BybitAPI instance from a credentials dictionary.
        
        Args:
            credentials: Dictionary containing 'apiKey', 'secret', and 'live' keys
            vip_level: VIP level for rate limiting ('DEFAULT', 'VIP1', 'VIP2', etc.)
            
        Returns:
            BybitAPI: Configured BybitAPI instance, or None if credentials are invalid
        """
        if not credentials or not isinstance(credentials, dict):
            print("No valid credentials dictionary provided")
            return None
            
        api_key = credentials.get('apiKey')
        api_secret = credentials.get('secret')
        
        if not api_key or not api_secret:
            print("API key and secret are required")
            return None
            
        # Check if using testnet
        is_testnet = credentials.get('live', 'N') != 'Y'
        
        try:
            # Create BybitAPI instance
            bybit_api = cls(
                api_key=api_key,
                api_secret=api_secret,
                vip_level=vip_level,
                is_testnet=is_testnet
            )
            
            if is_testnet:
                print("Connecting to Bybit testnet with VIP account")
            else:
                print("Connecting to Bybit live VIP account")
                
            return bybit_api
        except Exception as e:
            print(f"Error initializing Bybit API: {str(e)}")
            print("Cannot proceed without valid Bybit VIP credentials")
            return None
    
    def initialize_exchange(self):
        """Initialize and configure the ccxt exchange object for Bybit."""
        try:
            self.exchange = ccxt.bybit({
                'apiKey': self.api_key,
                'secret': self.api_secret,
                'enableRateLimit': True,
                'options': {
                    'recvWindow': 10000,
                    'adjustForTimeDifference': True
                },
            })
            
            # Use testnet if specified
            if self.is_testnet:
                self.exchange.enable_demo_trading(True)
                print("Connecting to Bybit testnet")
            else:
                print("Connecting to Bybit live")
                
            return True
        except Exception as e:
            print(f"Error initializing Bybit exchange: {str(e)}")
            return False
    
    def fetch_ohlcv(self, symbol, resolution, start_time, end_time, max_retries=3, timeout=10):
        """
        Fetch OHLCV data from Bybit in chunks to handle API limits.
        Includes timeout and retry mechanisms.
        
        Args:
            symbol: Trading pair symbol (e.g. 'BTCUSDT')
            resolution: Time resolution (e.g. '15m', '1h', '1d')
            start_time: Start timestamp in milliseconds
            end_time: End timestamp in milliseconds
            max_retries: Maximum number of retries for failed chunks
            timeout: Request timeout in seconds
            
        Returns:
            DataFrame with OHLCV data or None if error
        """
        if not self.exchange:
            print("Exchange not initialized")
            return None
        
        # Initialize empty list to store all OHLCV data
        all_ohlcv = []
        
        # Fetch data in chunks to handle potential limits
        current_start = start_time
        print(f"Fetching OHLCV data for {symbol} with {resolution} resolution from Bybit...")
        
        # Set the request timeout
        self.exchange.timeout = timeout * 1000  # ccxt timeout is in milliseconds
        
        chunk_count = 0
        retry_count = 0
        
        while current_start < end_time:
            try:
                chunk = self.exchange.fetch_ohlcv(symbol, resolution, since=current_start, limit=1000)
                
                if not chunk or len(chunk) == 0:
                    # No more data, break the loop
                    break
                    
                chunk_count += 1
                all_ohlcv.extend(chunk)
                current_start = chunk[-1][0] + 1  # Start from next timestamp after last data point
                
                # Reset retry counter after a successful fetch
                retry_count = 0
                
                # Add a small delay to avoid rate limits
                time.sleep(self.rate_limit_pause)
                
            except ccxt.NetworkError as e:
                retry_count += 1
                if retry_count > max_retries:
                    print(f"Failed to fetch OHLCV chunk after {max_retries} retries. Last error: {str(e)}")
                    break
                    
                # Calculate exponential backoff wait time (1s, 2s, 4s, ...)
                wait_time = min(2 ** (retry_count - 1), 30)  # Cap at 30 seconds
                print(f"Network error: {str(e)}. Retrying in {wait_time} seconds (attempt {retry_count}/{max_retries})...")
                time.sleep(wait_time)
                
            except ccxt.ExchangeError as e:
                if 'rate limit' in str(e).lower():
                    # Rate limit reached, wait longer and retry
                    retry_count += 1
                    if retry_count > max_retries:
                        print(f"Rate limit exceeded after {max_retries} retries. Last error: {str(e)}")
                        break
                        
                    # More aggressive backoff for rate limit errors
                    wait_time = min(5 * retry_count, 60)  # Cap at 60 seconds
                    print(f"Rate limit error: {str(e)}. Retrying in {wait_time} seconds (attempt {retry_count}/{max_retries})...")
                    time.sleep(wait_time)
                else:
                    # Other exchange errors
                    print(f"Exchange error fetching OHLCV data chunk: {e}")
                    break
                    
            except Exception as e:
                print(f"Error fetching OHLCV data chunk: {e}")
                break
        
        if not all_ohlcv:
            print(f"Error fetching OHLCV data from Bybit")
            return None
        
        print(f"Successfully fetched {len(all_ohlcv)} OHLCV data points from Bybit in {chunk_count} chunks")
        
        # Create dataframe from OHLCV data
        df_raw = pd.DataFrame(all_ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df_raw['t'] = pd.to_datetime(df_raw['timestamp'], unit='ms')
        
        return df_raw
    
    def fetch_funding_rate(self, symbol, start_time, end_time, max_retries=3, timeout=10):
        """
        Fetch funding rate data from Bybit.
        Includes timeout and retry mechanisms.
        
        Args:
            symbol: Trading pair symbol (e.g. 'BTCUSDT')
            start_time: Start timestamp in milliseconds
            end_time: End timestamp in milliseconds
            max_retries: Maximum number of retries for failed chunks
            timeout: Request timeout in seconds
            
        Returns:
            DataFrame with funding rate data or None if error
        """
        try:
            if not self.exchange:
                print("Exchange not initialized")
                return None
                
            print(f"Fetching funding rate data for {symbol} from Bybit...")
            
            # Set the request timeout
            self.exchange.timeout = timeout * 1000  # ccxt timeout is in milliseconds
            
            # For ccxt, we need to use the fetchFundingRateHistory method
            # Some exchanges may have limits, so we might need to paginate
            all_funding_data = []
            current_start = start_time
            chunk_count = 0
            retry_count = 0
            
            while current_start < end_time:
                try:
                    funding_data = self.exchange.fetchFundingRateHistory(symbol, since=current_start, limit=1000)
                    
                    if not funding_data or len(funding_data) == 0:
                        # No more data, break the loop
                        break
                        
                    chunk_count += 1
                    all_funding_data.extend(funding_data)
                    
                    # Get the timestamp of the last item for pagination
                    last_timestamp = funding_data[-1]['timestamp']
                    current_start = last_timestamp + 1
                    
                    # Reset retry counter after successful fetch
                    retry_count = 0
                    
                    # Add a small delay to avoid rate limits
                    time.sleep(self.rate_limit_pause)
                    
                except ccxt.NetworkError as e:
                    retry_count += 1
                    if retry_count > max_retries:
                        print(f"Failed to fetch funding rate chunk after {max_retries} retries. Last error: {str(e)}")
                        break
                        
                    # Calculate exponential backoff wait time (1s, 2s, 4s, ...)
                    wait_time = min(2 ** (retry_count - 1), 30)  # Cap at 30 seconds
                    print(f"Network error: {str(e)}. Retrying in {wait_time} seconds (attempt {retry_count}/{max_retries})...")
                    time.sleep(wait_time)
                    
                except ccxt.ExchangeError as e:
                    if 'rate limit' in str(e).lower():
                        # Rate limit reached, wait longer and retry
                        retry_count += 1
                        if retry_count > max_retries:
                            print(f"Rate limit exceeded after {max_retries} retries. Last error: {str(e)}")
                            break
                            
                        # More aggressive backoff for rate limit errors
                        wait_time = min(5 * retry_count, 60)  # Cap at 60 seconds
                        print(f"Rate limit error: {str(e)}. Retrying in {wait_time} seconds (attempt {retry_count}/{max_retries})...")
                        time.sleep(wait_time)
                    else:
                        # Other exchange errors
                        print(f"Exchange error fetching funding rate chunk: {e}")
                        break
                        
                except Exception as e:
                    print(f"Error fetching funding rate chunk: {e}")
                    break
            
            if not all_funding_data:
                print(f"No funding rate data found for {symbol}")
                return None
                
            print(f"Successfully fetched {len(all_funding_data)} funding rate data points from Bybit in {chunk_count} chunks")
            
            # Convert to DataFrame
            df_funding = pd.DataFrame(all_funding_data)
            df_funding['t'] = pd.to_datetime(df_funding['timestamp'], unit='ms')
            df_funding.rename(columns={'rate': 'funding_rate'}, inplace=True)
            
            return df_funding
            
        except Exception as e:
            print(f"Error fetching funding rate data: {e}")
            return None
    
    def fetch_open_interest(self, symbol, start_time, end_time, max_retries=3, timeout=10):
        """
        Fetch open interest data from Bybit.
        
        Args:
            symbol: Trading pair symbol (e.g. 'BTCUSDT')
            start_time: Start timestamp in milliseconds
            end_time: End timestamp in milliseconds
            max_retries: Maximum number of retries for failed chunks
            timeout: Request timeout in seconds
            
        Returns:
            DataFrame with open interest data or None if error
        """
        try:
            if not self.exchange:
                print("Exchange not initialized")
                return None
                
            print(f"Fetching open interest data for {symbol} from Bybit...")
            
            # Set the request timeout
            self.exchange.timeout = timeout * 1000  # ccxt timeout is in milliseconds
            
            # For ccxt, some exchanges support fetchOpenInterestHistory
            if hasattr(self.exchange, 'fetchOpenInterestHistory') and callable(getattr(self.exchange, 'fetchOpenInterestHistory')):
                all_oi_data = []
                current_start = start_time
                chunk_count = 0
                retry_count = 0
                
                while current_start < end_time:
                    try:
                        oi_data = self.exchange.fetchOpenInterestHistory(symbol, since=current_start, limit=1000)
                        
                        if not oi_data or len(oi_data) == 0:
                            # No more data, break the loop
                            break
                            
                        chunk_count += 1
                        all_oi_data.extend(oi_data)
                        
                        # Get the timestamp of the last item for pagination
                        last_timestamp = oi_data[-1]['timestamp']
                        current_start = last_timestamp + 1
                        
                        # Reset retry counter after successful fetch
                        retry_count = 0
                        
                        # Add a small delay to avoid rate limits
                        time.sleep(self.rate_limit_pause)
                        
                    except ccxt.NetworkError as e:
                        retry_count += 1
                        if retry_count > max_retries:
                            print(f"Failed to fetch open interest chunk after {max_retries} retries. Last error: {str(e)}")
                            break
                            
                        # Calculate exponential backoff wait time (1s, 2s, 4s, ...)
                        wait_time = min(2 ** (retry_count - 1), 30)  # Cap at 30 seconds
                        print(f"Network error: {str(e)}. Retrying in {wait_time} seconds (attempt {retry_count}/{max_retries})...")
                        time.sleep(wait_time)
                        
                    except ccxt.ExchangeError as e:
                        if 'rate limit' in str(e).lower():
                            # Rate limit reached, wait longer and retry
                            retry_count += 1
                            if retry_count > max_retries:
                                print(f"Rate limit exceeded after {max_retries} retries. Last error: {str(e)}")
                                break
                                
                            # More aggressive backoff for rate limit errors
                            wait_time = min(5 * retry_count, 60)  # Cap at 60 seconds
                            print(f"Rate limit error: {str(e)}. Retrying in {wait_time} seconds (attempt {retry_count}/{max_retries})...")
                            time.sleep(wait_time)
                        else:
                            # Other exchange errors
                            print(f"Exchange error fetching open interest chunk: {e}")
                            break
                            
                    except Exception as e:
                        print(f"Error fetching open interest chunk: {e}")
                        break
                
                if not all_oi_data:
                    print(f"No open interest data found for {symbol}")
                    return None
                    
                print(f"Successfully fetched {len(all_oi_data)} open interest data points from Bybit")
                
                # Convert to DataFrame
                df_oi = pd.DataFrame(all_oi_data)
                df_oi['t'] = pd.to_datetime(df_oi['timestamp'], unit='ms')
                
                return df_oi
            else:
                # If ccxt doesn't support open interest for this exchange, try using direct API
                print("CCXT doesn't support open interest fetching for Bybit. Using direct API method.")
                return self.fetch_open_interest_direct(symbol, '1h', start_time, end_time, max_retries, timeout)
                
        except Exception as e:
            print(f"Error fetching open interest data: {e}")
            return None
    
    def fetch_open_interest_direct(self, symbol, interval, start_time, end_time, max_retries=3, timeout=10):
        """
        Fetch open interest data directly from Bybit REST API.
        
        Args:
            symbol: Trading pair symbol (e.g. 'BTCUSDT')
            interval: Time interval ('5min', '15min', '30min', '1h', '4h', '1d')
            start_time: Start timestamp in milliseconds
            end_time: End timestamp in milliseconds
            max_retries: Maximum number of retries for failed chunks
            timeout: Request timeout in seconds
            
        Returns:
            DataFrame with open interest data or None if error
        """
        try:
            if not self.api_key or not self.api_secret:
                print("API key and secret required for direct API access")
                return None
                
            print(f"Fetching open interest data for {symbol} directly from Bybit API...")
            
            # Convert CCXT intervals to Bybit intervals
            interval_mapping = {
                '1m': '5min',  # Bybit doesn't support 1m for OI, using 5min as minimum
                '5m': '5min',
                '15m': '15min',
                '30m': '30min',
                '1h': '1h',
                '4h': '4h',
                '1d': '1d',
                '24h': '1d'
            }
            
            bybit_interval = interval_mapping.get(interval, '1h')
            
            # Base URL for Bybit API v5
            base_url = "https://api.bybit.com"
            
            # Endpoint for open interest data
            endpoint = "/v5/market/open-interest"
            
            all_data = []
            # We need to loop due to API limits on date ranges
            # Bybit typically limits to data from the last 30 days
            # Break into smaller chunks if needed
            current_start = start_time
            page_size = 200  # Maximum page size for most Bybit endpoints
            chunk_count = 0
            
            while current_start < end_time:
                retry_count = 0
                success = False
                
                while not success and retry_count <= max_retries:
                    try:
                        # Calculate chunk end time (max 30 days from start)
                        chunk_end_time = min(current_start + (30 * 24 * 60 * 60 * 1000), end_time)
                        
                        # Parameters for the request
                        params = {
                            'category': 'linear',
                            'symbol': symbol,
                            'intervalTime': bybit_interval,
                            'startTime': current_start,
                            'endTime': chunk_end_time,
                            'limit': page_size
                        }
                        
                        # Sort parameters alphabetically as required by Bybit
                        sorted_params = sorted(params.items())
                        query_string = urllib.parse.urlencode(sorted_params)
                        
                        # Get current timestamp (required for authentication)
                        timestamp = int(time.time() * 1000)
                        
                        # Signature generation
                        signature_payload = f"{timestamp}{self.api_key}{page_size}{query_string}"
                        signature = hmac.new(
                            bytes(self.api_secret, 'utf-8'),
                            bytes(signature_payload, 'utf-8'),
                            hashlib.sha256
                        ).hexdigest()
                        
                        # Headers
                        headers = {
                            'X-BAPI-API-KEY': self.api_key,
                            'X-BAPI-SIGN': signature,
                            'X-BAPI-SIGN-TYPE': '2',
                            'X-BAPI-TIMESTAMP': str(timestamp),
                            'X-BAPI-RECV-WINDOW': '5000'
                        }
                        
                        # Make the request with timeout
                        url = f"{base_url}{endpoint}?{query_string}"
                        response = requests.get(url, headers=headers, timeout=timeout)
                        
                        if response.status_code == 200:
                            result = response.json()
                            if result['retCode'] == 0 and 'result' in result and 'list' in result['result']:
                                data = result['result']['list']
                                if data:
                                    chunk_count += 1
                                    all_data.extend(data)
                                    
                                    # Update current_start for next iteration
                                    last_timestamp = int(data[-1]['timestamp'])
                                    current_start = last_timestamp + 1
                                    success = True
                                else:
                                    # No data in this range, move forward
                                    current_start = chunk_end_time + 1
                                    success = True
                            else:
                                error_msg = f"Error in Bybit response: {result}"
                                if 'rate limit' in str(result).lower():
                                    # Rate limit error, retry with backoff
                                    retry_count += 1
                                    if retry_count <= max_retries:
                                        wait_time = min(5 * retry_count, 60)
                                        print(f"Rate limit error. Retrying in {wait_time} seconds (attempt {retry_count}/{max_retries})...")
                                        time.sleep(wait_time)
                                    else:
                                        print(error_msg)
                                        break
                                else:
                                    # Other API error
                                    print(error_msg)
                                    break
                        else:
                            error_msg = f"Error fetching open interest: HTTP {response.status_code}: {response.text}"
                            if response.status_code in [429, 403]:  # Rate limit or permission error
                                retry_count += 1
                                if retry_count <= max_retries:
                                    wait_time = min(5 * retry_count, 60)
                                    print(f"HTTP error {response.status_code}. Retrying in {wait_time} seconds (attempt {retry_count}/{max_retries})...")
                                    time.sleep(wait_time)
                                else:
                                    print(error_msg)
                                    break
                            else:
                                # Other HTTP error
                                print(error_msg)
                                break
                    except requests.Timeout:
                        retry_count += 1
                        if retry_count <= max_retries:
                            wait_time = min(2 ** (retry_count - 1), 30)
                            print(f"Request timeout. Retrying in {wait_time} seconds (attempt {retry_count}/{max_retries})...")
                            time.sleep(wait_time)
                        else:
                            print(f"Request timeout after {max_retries} retries.")
                            break
                            
                    except requests.ConnectionError:
                        retry_count += 1
                        if retry_count <= max_retries:
                            wait_time = min(2 ** (retry_count - 1), 30)
                            print(f"Connection error. Retrying in {wait_time} seconds (attempt {retry_count}/{max_retries})...")
                            time.sleep(wait_time)
                        else:
                            print(f"Connection error after {max_retries} retries.")
                            break
                            
                    except Exception as e:
                        retry_count += 1
                        if retry_count <= max_retries:
                            wait_time = min(2 ** (retry_count - 1), 30)
                            print(f"Error: {str(e)}. Retrying in {wait_time} seconds (attempt {retry_count}/{max_retries})...")
                            time.sleep(wait_time)
                        else:
                            print(f"Error after {max_retries} retries: {str(e)}")
                            break
                
                # If all retries failed for this chunk, break the outer loop
                if not success:
                    break
                    
                # Add a delay between chunks to avoid rate limits
                time.sleep(self.rate_limit_pause)
            
            if not all_data:
                print("No open interest data found")
                return None
                
            print(f"Successfully fetched {len(all_data)} open interest data points from Bybit API in {chunk_count} chunks")
            
            # Convert to DataFrame
            df_oi = pd.DataFrame(all_data)
            
            # Format the data
            df_oi['t'] = pd.to_datetime(df_oi['timestamp'], unit='ms')
            df_oi['openInterest'] = df_oi['openInterest'].astype(float)
            
            return df_oi
            
        except Exception as e:
            print(f"Error fetching open interest data from Bybit API: {e}")
            return None
    
    def fetch_funding_rate_direct(self, symbol, start_time, end_time, max_retries=3, timeout=10):
        """
        Fetch funding rate data directly from Bybit REST API.
        Includes timeout and retry mechanisms.
        
        Args:
            symbol: Trading pair symbol (e.g. 'BTCUSDT')
            start_time: Start timestamp in milliseconds
            end_time: End timestamp in milliseconds
            max_retries: Maximum number of retries for failed chunks
            timeout: Request timeout in seconds
            
        Returns:
            DataFrame with funding rate data or None if error
        """
        try:
            if not self.api_key or not self.api_secret:
                print("API key and secret required for direct API access")
                return None
                
            print(f"Fetching funding rate data for {symbol} directly from Bybit API...")
            
            # Base URL for Bybit API v5
            base_url = "https://api.bybit.com"
            
            # Endpoint for funding rate data
            endpoint = "/v5/market/funding/history"
            
            all_data = []
            # We need to loop due to API limits on date ranges
            current_start = start_time
            page_size = 200  # Maximum page size for most Bybit endpoints
            chunk_count = 0
            
            while current_start < end_time:
                retry_count = 0
                success = False
                
                while not success and retry_count <= max_retries:
                    try:
                        # Parameters for the request
                        params = {
                            'category': 'linear',
                            'symbol': symbol,
                            'startTime': current_start,
                            'endTime': min(current_start + (30 * 24 * 60 * 60 * 1000), end_time),
                            'limit': page_size
                        }
                        
                        # Sort parameters alphabetically as required by Bybit
                        sorted_params = sorted(params.items())
                        query_string = urllib.parse.urlencode(sorted_params)
                        
                        # Get current timestamp (required for authentication)
                        timestamp = int(time.time() * 1000)
                        
                        # Signature generation
                        signature_payload = f"{timestamp}{self.api_key}{page_size}{query_string}"
                        signature = hmac.new(
                            bytes(self.api_secret, 'utf-8'),
                            bytes(signature_payload, 'utf-8'),
                            hashlib.sha256
                        ).hexdigest()
                        
                        # Headers
                        headers = {
                            'X-BAPI-API-KEY': self.api_key,
                            'X-BAPI-SIGN': signature,
                            'X-BAPI-SIGN-TYPE': '2',
                            'X-BAPI-TIMESTAMP': str(timestamp),
                            'X-BAPI-RECV-WINDOW': '5000'
                        }
                        
                        # Make the request with timeout
                        url = f"{base_url}{endpoint}?{query_string}"
                        response = requests.get(url, headers=headers, timeout=timeout)
                        
                        if response.status_code == 200:
                            result = response.json()
                            if result['retCode'] == 0 and 'result' in result and 'list' in result['result']:
                                data = result['result']['list']
                                if data:
                                    chunk_count += 1
                                    all_data.extend(data)
                                    
                                    # Update current_start for next iteration
                                    last_timestamp = 0
                                    for item in data:
                                        if 'fundingRateTimestamp' in item:
                                            ts = int(item['fundingRateTimestamp'])
                                            if ts > last_timestamp:
                                                last_timestamp = ts
                                    
                                    if last_timestamp > 0:
                                        current_start = last_timestamp + 1
                                    else:
                                        # Fallback if we can't find timestamps
                                        current_start += (7 * 24 * 60 * 60 * 1000)  # Move forward 7 days
                                else:
                                    # No data in this range, move forward
                                    current_start += (7 * 24 * 60 * 60 * 1000)  # Move forward 7 days
                                    
                                success = True
                            else:
                                error_msg = f"Error in Bybit response: {result}"
                                if 'rate limit' in str(result).lower():
                                    # Rate limit error, retry with backoff
                                    retry_count += 1
                                    if retry_count <= max_retries:
                                        wait_time = min(5 * retry_count, 60)
                                        print(f"Rate limit error. Retrying in {wait_time} seconds (attempt {retry_count}/{max_retries})...")
                                        time.sleep(wait_time)
                                    else:
                                        print(error_msg)
                                        break
                                else:
                                    # Other API error
                                    print(error_msg)
                                    break
                        else:
                            error_msg = f"Error fetching funding rate: HTTP {response.status_code}: {response.text}"
                            if response.status_code in [429, 403]:  # Rate limit or permission error
                                retry_count += 1
                                if retry_count <= max_retries:
                                    wait_time = min(5 * retry_count, 60)
                                    print(f"HTTP error {response.status_code}. Retrying in {wait_time} seconds (attempt {retry_count}/{max_retries})...")
                                    time.sleep(wait_time)
                                else:
                                    print(error_msg)
                                    break
                            else:
                                # Other HTTP error
                                print(error_msg)
                                break
                    
                    except requests.Timeout:
                        retry_count += 1
                        if retry_count <= max_retries:
                            wait_time = min(2 ** (retry_count - 1), 30)
                            print(f"Request timeout. Retrying in {wait_time} seconds (attempt {retry_count}/{max_retries})...")
                            time.sleep(wait_time)
                        else:
                            print(f"Request timeout after {max_retries} retries.")
                            break
                            
                    except requests.ConnectionError:
                        retry_count += 1
                        if retry_count <= max_retries:
                            wait_time = min(2 ** (retry_count - 1), 30)
                            print(f"Connection error. Retrying in {wait_time} seconds (attempt {retry_count}/{max_retries})...")
                            time.sleep(wait_time)
                        else:
                            print(f"Connection error after {max_retries} retries.")
                            break
                            
                    except Exception as e:
                        retry_count += 1
                        if retry_count <= max_retries:
                            wait_time = min(2 ** (retry_count - 1), 30)
                            print(f"Error: {str(e)}. Retrying in {wait_time} seconds (attempt {retry_count}/{max_retries})...")
                            time.sleep(wait_time)
                        else:
                            print(f"Error after {max_retries} retries: {str(e)}")
                            break
                
                # If all retries failed for this chunk, break the outer loop
                if not success:
                    break
                    
                # Add a delay between successful chunks to avoid rate limits
                time.sleep(self.rate_limit_pause)
            
            if not all_data:
                print("No funding rate data found")
                return None
                
            print(f"Successfully fetched {len(all_data)} funding rate data points from Bybit API in {chunk_count} chunks")
            
            # Convert to DataFrame
            df_funding = pd.DataFrame(all_data)
            
            # Format the data
            if 'fundingRateTimestamp' in df_funding.columns:
                df_funding['t'] = pd.to_datetime(df_funding['fundingRateTimestamp'], unit='ms')
            elif 'timestamp' in df_funding.columns:
                df_funding['t'] = pd.to_datetime(df_funding['timestamp'], unit='ms')
            else:
                raise ValueError("No timestamp column found in funding rate data")
                
            # Find the funding rate column
            funding_rate_col = next((col for col in df_funding.columns if 'fundingRate' in col), None)
            if not funding_rate_col:
                print(f"Available columns in funding rate data: {list(df_funding.columns)}")
                raise ValueError("Could not determine funding rate column in data")
                
            df_funding['funding_rate'] = df_funding[funding_rate_col].astype(float)
            
            return df_funding
            
        except Exception as e:
            print(f"Error fetching funding rate data from Bybit API: {e}")
            return None

    def save_data_to_csv(self, df, data_type, symbol, resolution, output_dir="cex_data"):
        """
        Save dataframe to CSV file.
        
        Args:
            df: Dataframe to save
            data_type: Type of data (ohlcv, funding_rate, open_interest)
            symbol: Trading pair symbol
            resolution: Time resolution (e.g. '15m', '1h', '1d')
            output_dir: Output directory
        
        Returns:
            Path to the saved file
        """
        if df is None or df.empty:
            print(f"Cannot save {data_type} data: DataFrame is empty or None")
            return None
            
        # Create main directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Create symbol subdirectory
        symbol_dir = os.path.join(output_dir, symbol.lower())
        os.makedirs(symbol_dir, exist_ok=True)
        
        # Create data type subdirectory
        data_dir = os.path.join(symbol_dir, data_type)
        os.makedirs(data_dir, exist_ok=True)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{data_dir}/{symbol.lower()}_{data_type}_{resolution}_{timestamp}.csv"
        
        # Save data to CSV
        df.to_csv(filename, index=False)
        print(f"Saved {data_type} data to {filename}")
        
        return filename
