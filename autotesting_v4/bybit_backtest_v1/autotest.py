import os
import pandas as pd
import sys
import time
import requests
import shutil
import builtins
import multiprocessing
import uuid
import random
from typing import Dict, Any, Optional, List, Tuple, Callable
from datetime import datetime
from concurrent.futures import ProcessPoolExecutor
from functools import wraps

# Import backtesting as module level variable but don't access it directly yet
import importlib
import os.path

# Use a delayed import approach
backtesting = None

def retry_with_exponential_backoff(
    max_retries: int = 10,
    initial_delay_seconds: float = 1.0,
    max_delay_seconds: float = 60.0,
    backoff_factor: float = 2.0,
    retryable_exceptions: Tuple = (
        requests.exceptions.RequestException,
        requests.exceptions.HTTPError,
        requests.exceptions.ConnectionError,
        requests.exceptions.Timeout,
        requests.exceptions.J<PERSON><PERSON><PERSON><PERSON><PERSON>rror,
        ValueError,  # Handle empty response issues
    ),
):
    """
    Retry decorator with exponential backoff for handling API rate limits and temporary failures.
    
    Args:
        max_retries: Maximum number of retries before giving up
        initial_delay_seconds: Initial delay between retries in seconds
        max_delay_seconds: Maximum delay between retries in seconds
        backoff_factor: Factor by which the delay increases with each retry
        retryable_exceptions: Tuple of exceptions that trigger a retry
        
    Returns:
        A decorator that can be applied to functions making API calls
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            delay = initial_delay_seconds
            last_exception = None
            
            for retry_count in range(max_retries + 1):  # +1 for the initial attempt
                try:
                    if retry_count > 0:
                        print(f"Retry attempt {retry_count}/{max_retries} (delay: {delay:.2f}s)...")
                    
                    # Call the function and get the result
                    result = func(*args, **kwargs)
                    
                    # For API requests that return empty results (but don't raise exceptions)
                    # We'll treat empty results as a retryable condition
                    if result is None or (isinstance(result, (list, dict)) and len(result) == 0):
                        if retry_count < max_retries:
                            print(f"Empty result received. Retrying...")
                            time.sleep(delay)
                            delay = min(delay * backoff_factor, max_delay_seconds)
                            continue
                        else:
                            print(f"Empty result received after {max_retries} retries.")
                            return result
                    
                    # If we got here, the request was successful
                    return result
                    
                except requests.exceptions.HTTPError as e:
                    # Check for rate limiting (429) or server errors (5xx)
                    if hasattr(e, 'response') and e.response is not None:
                        status_code = e.response.status_code
                        
                        # Handle rate limiting
                        if status_code == 429:
                            print(f"\n===== RATE LIMIT ERROR =====")
                            print(f"Glassnode API rate limit hit (429 Too Many Requests)")
                            
                            # Try to get retry-after header, default to current delay if not present
                            retry_after = e.response.headers.get('Retry-After')
                            if retry_after and retry_after.isdigit():
                                delay = float(retry_after)
                                print(f"API suggests waiting {delay}s before retrying")
                            
                            if retry_count < max_retries:
                                # Add jitter to avoid thundering herd problem
                                jitter = random.uniform(0, 0.1 * delay)
                                wait_time = delay + jitter
                                print(f"Waiting {wait_time:.2f}s before retry {retry_count + 1}/{max_retries}...")
                                time.sleep(wait_time)
                                # Increase delay for next time
                                delay = min(delay * backoff_factor, max_delay_seconds)
                                continue
                        
                        # Handle server errors (always retry)
                        elif 500 <= status_code < 600:
                            print(f"Server error: HTTP {status_code}. Retrying...")
                            if retry_count < max_retries:
                                # Add jitter to avoid thundering herd problem
                                jitter = random.uniform(0, 0.1 * delay)
                                wait_time = delay + jitter
                                time.sleep(wait_time)
                                delay = min(delay * backoff_factor, max_delay_seconds)
                                continue
                        
                        # Handle authentication errors (don't retry)
                        elif status_code == 401 or status_code == 403:
                            print(f"\n===== AUTHENTICATION ERROR =====")
                            print(f"API Key may be invalid (HTTP {status_code})")
                            print("This error indicates an authentication problem with your API key.")
                            print("Check that your API key is valid and properly set.")
                            # Don't retry authentication errors
                            raise e
                            
                    last_exception = e
                    print(f"HTTP Error: {e}")
                    if retry_count < max_retries:
                        # Add jitter to avoid thundering herd problem
                        jitter = random.uniform(0, 0.1 * delay)
                        wait_time = delay + jitter
                        time.sleep(wait_time)
                        delay = min(delay * backoff_factor, max_delay_seconds)
                    else:
                        # Max retries reached
                        raise
                        
                except requests.exceptions.JSONDecodeError as e:
                    last_exception = e
                    
                    print(f"JSON decode error: {str(e)}")
                    if retry_count >= max_retries:
                        print(f"Maximum retry attempts ({max_retries}) reached.")
                        raise
                    
                    # Add longer delay for JSON decode errors - these are often due to rate limits
                    delay = min(delay * backoff_factor * 1.5, max_delay_seconds)
                    jitter = random.uniform(0, 0.1 * delay)
                    wait_time = delay + jitter
                    
                    print(f"Waiting {wait_time:.2f}s before retry {retry_count + 1}/{max_retries}...")
                    time.sleep(wait_time)
                        
                except retryable_exceptions as e:
                    last_exception = e
                    
                    # Check if we should retry
                    if retry_count >= max_retries:
                        print(f"Maximum retry attempts ({max_retries}) reached.")
                        raise
                    
                    # Add jitter to avoid thundering herd problem
                    jitter = random.uniform(0, 0.1 * delay)
                    wait_time = delay + jitter
                    
                    print(f"Error during API call: {str(e)}")
                    print(f"Waiting {wait_time:.2f}s before retry {retry_count + 1}/{max_retries}...")
                    time.sleep(wait_time)
                    
                    # Increase delay for next time with exponential backoff
                    delay = min(delay * backoff_factor, max_delay_seconds)
                
                except Exception as e:
                    # Non-retryable exception, raise immediately
                    print(f"Non-retryable error: {str(e)}")
                    raise
            
            # If we exit the loop due to max retries, raise the last exception
            if last_exception:
                raise last_exception
                
        return wrapper
    return decorator

def import_backtesting():
    """
    Import the backtesting module safely.
    This prevents the execution of code in the module level when importing.
    Handle common errors during import and provide user-friendly error messages.
    """
    global backtesting
    if backtesting is None:
        try:
            # Get the current directory to add to sys.path if needed
            current_dir = os.path.dirname(os.path.abspath(__file__))
            if current_dir not in sys.path:
                sys.path.insert(0, current_dir)
            
            # Try to import the module using importlib
            import importlib.util
            
            try:
                backtesting = importlib.import_module('backtesting')
            except ImportError:
                # If that fails, try with absolute path
                module_path = os.path.join(current_dir, 'backtesting.py')
                if os.path.exists(module_path):
                    module_name = os.path.basename(module_path)[:-3]  # Remove .py extension
                    spec = importlib.util.spec_from_file_location(module_name, module_path)
                    backtesting = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(backtesting)
                else:
                    raise ImportError(f"Could not find backtesting.py in {current_dir}")
                    
        except requests.exceptions.JSONDecodeError:
            print("\n===== API ERROR =====")
            print("Error: Failed to decode JSON response from the Glassnode API.")
            print("This usually happens when:")
            print("1. Your API key is invalid or has expired")
            print("2. The Glassnode API is experiencing downtime")
            print("3. Your network connection to Glassnode servers is unstable")
            print("\nWhat to do next:")
            print("- Check your API key in the backtesting.py file")
            print("- Verify your internet connection")
            print("- Wait a few minutes and try again")
            print("- Check Glassnode status page for any reported outages")
            sys.exit(1)
        except ImportError as e:
            print(f"\n===== IMPORT ERROR =====")
            print(f"Error: Could not import the backtesting module: {str(e)}")
            print("Make sure the backtesting.py file exists in the same directory as autotest.py")
            sys.exit(1)
        except Exception as e:
            print(f"\n===== UNEXPECTED ERROR =====")
            print(f"Error initializing backtesting module: {str(e)}")
            print("\nTraceback:")
            import traceback
            traceback.print_exc()
            print("\nWhat to do next:")
            print("- Try running again in a few minutes")
            print("- Check your API credentials")
            print("- Verify all required Python packages are installed")
            sys.exit(1)
    return backtesting

@retry_with_exponential_backoff()
def make_glassnode_api_request(url, params, timeout=30):
    """
    Make a request to the Glassnode API with retry logic.
    
    Args:
        url: The API endpoint URL
        params: Dictionary of query parameters
        timeout: Request timeout in seconds
        
    Returns:
        Parsed JSON response data
    """
    try:
        # Make sure we have an API key
        if 'api_key' not in params or not params['api_key']:
            raise ValueError("Missing API key. Check backtesting.py API_KEY setting.")
            
        # Ensure we have required parameters
        required_params = ['a', 's', 'u', 'i']
        for param in required_params:
            if param not in params:
                raise ValueError(f"Missing required parameter: {param}")
        
        # Print additional debug info
        api_symbol = params.get('a', 'unknown')
        since = params.get('s', 'unknown')
        until = params.get('u', 'unknown')
        resolution = params.get('i', 'unknown')
        print(f"API Request: {url.split('/')[-1]} - Symbol: {api_symbol}, "
              f"Range: {datetime.fromtimestamp(int(since)).strftime('%Y-%m-%d')} to "
              f"{datetime.fromtimestamp(int(until)).strftime('%Y-%m-%d')}, Resolution: {resolution}")
              
        # Make the request with additional error handling
        response = requests.get(url, params=params, timeout=timeout)
        
        # Raise an HTTPError for bad responses (4XX and 5XX)
        response.raise_for_status()
        
        # Check for empty response
        if not response.text or response.text.isspace():
            raise ValueError("API returned empty response body")
        
        # Parse JSON response
        try:
            data = response.json()
        except ValueError as e:
            # Log the actual response content to help debug
            print(f"JSON decode error. Response content (first 200 chars): {response.text[:200]}...")
            raise requests.exceptions.JSONDecodeError(
                f"Failed to parse JSON: {str(e)}", response.text, 0
            )
        
        # Check for empty data
        if not data:
            raise ValueError("API returned empty data array")
            
        # Check for specific error patterns in the response
        if isinstance(data, dict) and 'error' in data:
            error_message = data.get('error', 'Unknown API error')
            print(f"API returned error message: {error_message}")
            if 'rate limit' in error_message.lower() or 'too many requests' in error_message.lower():
                # Create a response-like object to raise as HTTPError with status code 429
                error_response = requests.Response()
                error_response.status_code = 429
                raise requests.exceptions.HTTPError(
                    f"Rate limit error: {error_message}",
                    response=error_response
                )
            # Raise as a generic exception
            raise ValueError(f"API error: {error_message}")
            
        # Log success with data count
        if isinstance(data, list):
            print(f"Received {len(data)} data points from API")
        
        return data
        
    except requests.exceptions.RequestException as e:
        # Add more context to the exception
        print(f"Request failed: {url}")
        print(f"Parameters: {', '.join([f'{k}={v}' for k, v in params.items() if k != 'api_key'])}")
        raise

def create_output_directory(strategy_name: str, mode: str = None, run_id: str = None) -> str:
    """
    Create output directory for the strategy results.
    
    Args:
        strategy_name: Name of the strategy
        mode: Mode of the backtest (optimize, walkforward, backtest)
        run_id: Unique identifier for this run to avoid directory conflicts
        
    Returns:
        Path to the output directory
    """
    # Get the script directory path
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Create base directory - relative to the script directory
    base_dir = os.path.join(script_dir, "backtest_result")
    os.makedirs(base_dir, exist_ok=True)
    
    # Create strategy-specific directory
    strategy_dir = os.path.join(base_dir, strategy_name)
    os.makedirs(strategy_dir, exist_ok=True)
    
    # If run_id provided, create a unique subdirectory to avoid conflicts
    if run_id:
        # Add mode to the directory name if provided
        if mode:
            unique_dir = os.path.join(strategy_dir, f"{mode}_{run_id}")
        else:
            unique_dir = os.path.join(strategy_dir, run_id)
        os.makedirs(unique_dir, exist_ok=True)
        return unique_dir
    
    return strategy_dir

def redirect_path_to_strategy_dir(original_path: str, strategy_name: str, mode: str = None, run_id: str = None) -> str:
    """
    Redirect a file path to save in the strategy-specific directory.
    
    Args:
        original_path: Original file path
        strategy_name: Name of the strategy
        mode: Mode of the backtest (optimize, walkforward, backtest)
        run_id: Unique identifier for this run to avoid directory conflicts
        
    Returns:
        New path in the strategy directory
    """
    # Get the script directory path
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Create base strategy directory - relative to the script directory
    base_dir = os.path.join(script_dir, "backtest_result")
    os.makedirs(base_dir, exist_ok=True)
    
    # Create strategy-specific directory
    strategy_dir = os.path.join(base_dir, strategy_name)
    os.makedirs(strategy_dir, exist_ok=True)
    
    # Extract the base filename
    basename = os.path.basename(original_path)
    
    # If run_id provided, create a unique subdirectory to avoid conflicts
    if run_id:
        # Add mode to the directory name if provided
        if mode:
            unique_dir = os.path.join(strategy_dir, f"{mode}_{run_id}")
        else:
            unique_dir = os.path.join(strategy_dir, run_id)
        os.makedirs(unique_dir, exist_ok=True)
        return os.path.join(unique_dir, basename)
    
    # Return the new path (directly in the strategy directory)
    return os.path.join(strategy_dir, basename)

def generate_csv_from_excel(excel_file: str, csv_file: str, sheet_name: str = "Strategies") -> bool:
    """
    Generate a CSV file from an Excel workbook's specified sheet.
    
    Args:
        excel_file: Path to the Excel file
        csv_file: Path to output CSV file
        sheet_name: Name of the worksheet to convert (default: "Strategies")
        
    Returns:
        True if successful, False otherwise
    """
    try:
        if not os.path.exists(excel_file):
            print(f"Excel file not found: {excel_file}")
            # If Excel doesn't exist but CSV does, consider that a success
            if os.path.exists(csv_file):
                print(f"CSV file already exists: {csv_file} - using existing file")
                return True
            return False
            
        # Check if the "Excel" file is actually a CSV (special case)
        import subprocess
        try:
            file_type_cmd = ["file", excel_file]
            file_type_output = subprocess.check_output(file_type_cmd, universal_newlines=True)
            
            if "CSV text" in file_type_output:
                print(f"The file {excel_file} is actually a CSV file with .xlsx extension")
                # Simply copy it to the CSV file location
                import shutil
                shutil.copy2(excel_file, csv_file)
                print(f"Copied CSV file from {excel_file} to {csv_file}")
                return True
        except Exception as e:
            print(f"Error checking file type: {str(e)}")
            
        # Try reading with different engines
        for engine in ['openpyxl', 'xlrd']:
            try:
                print(f"Trying to read Excel with engine: {engine}")
                df = pd.read_excel(excel_file, sheet_name=sheet_name, engine=engine)
                
                # Create the directory for CSV if it doesn't exist
                os.makedirs(os.path.dirname(csv_file), exist_ok=True)
                
                # Save as CSV
                df.to_csv(csv_file, index=False)
                print(f"Generated {csv_file} from {excel_file}, sheet: {sheet_name} using {engine} engine")
                return True
            except Exception as engine_error:
                print(f"Error with engine {engine}: {str(engine_error)}")
                continue
                
        # If we get here, both engines failed
        print(f"Failed to read Excel file with any available engine. Is it a valid Excel file?")
        
        # If Excel reading failed but CSV already exists, use that
        if os.path.exists(csv_file):
            print(f"Using existing CSV file: {csv_file}")
            return True
        
        return False
    except Exception as e:
        print(f"Error generating CSV from Excel: {str(e)}")
        
        # If we encounter an error but CSV already exists, use that
        if os.path.exists(csv_file):
            print(f"Using existing CSV file: {csv_file}")
            return True
            
        return False

def load_config_from_csv(csv_file: str, strategy_name: str = None) -> Dict[str, Any]:
    """
    Load configuration from a CSV file.
    Always regenerate CSV from the corresponding Excel file when available.
    
    Args:
        csv_file: Path to the CSV configuration file
        strategy_name: Optional strategy name to filter by
        
    Returns:
        Dictionary containing configuration parameters
    """
    # Try to generate from Excel if the CSV doesn't exist
    if not os.path.exists(csv_file):
        excel_file = csv_file.replace('.csv', '.xlsx')
        if os.path.exists(excel_file):
            print(f"Regenerating {csv_file} from {excel_file}")
            generate_csv_from_excel(excel_file, csv_file)
    
    # Check if CSV exists (either pre-existing or just generated)
    if not os.path.exists(csv_file):
        raise FileNotFoundError(f"Neither CSV ({csv_file}) nor Excel ({csv_file.replace('.csv', '.xlsx')}) files found")
        
    df = pd.read_csv(csv_file)
    
    # If strategy_name is provided, filter by that strategy
    if strategy_name:
        df = df[df['Strategy Name'] == strategy_name]
        if len(df) == 0:
            raise ValueError(f"Strategy '{strategy_name}' not found in config file")
    
    # Get the first row (or the filtered row if strategy_name was provided)
    config_row = df.iloc[0]
    
    # Map CSV columns to backtesting variables based on the specified mapping
    config = {
        'api_url': config_row['API'],
        'metric_key': str(config_row['Metric Key']) if pd.notna(config_row['Metric Key']) and str(config_row['Metric Key']).strip() else '/',
        'resolution': config_row['Resolution'],
        'api_symbol': config_row['API Symbol'],
        'underlying': config_row['Asset'],
        'strategy_name': config_row['Strategy Name'],  # Keep this for directory naming
        'model': config_row['Model'],  # This is the actual strategy model to use
        'style': config_row['Style'],
        'strategy_type': config_row['Type'],
    }
    
    # Normalize the API URL for CEX data sources
    # If API URL is 'bybit', 'cex', or similar, standardize it to 'cex'
    if config['api_url'].lower() in ['bybit', 'cex', 'bybit.com', 'exchange', 'cex_data']:
        config['api_url'] = 'cex'
        print(f"Normalized API URL to 'cex' for CEX data source.")
        # Ensure cex_symbol and cex_underlying are set
        config['cex_symbol'] = config['api_symbol']
        config['cex_underlying'] = config['underlying']
        
        # Print details for debugging
        print(f"CEX config: symbol={config['cex_symbol']}, underlying={config['cex_underlying']}")
    else:
        # For Glassnode, ensure we have the right parameters set
        # These will be used differently in the data retrieval process
        print(f"Using Glassnode API: {config['api_url']}")
    
    # Add window and threshold if they exist in the CSV (for backtest mode)
    if 'x' in config_row:
        try:
            # Try to convert to numeric value, handling potential spaces
            config['window'] = int(float(str(config_row['x']).strip()))
        except (ValueError, TypeError):
            print(f"Warning: Could not convert window value '{config_row['x']}' to integer. Using default.")
    
    if 'y' in config_row:
        try:
            # Try to convert to numeric value, handling potential spaces
            config['threshold'] = float(str(config_row['y']).strip())
        except (ValueError, TypeError):
            print(f"Warning: Could not convert threshold value '{config_row['y']}' to float. Using default.")
    
    # Add ratio if it exists
    if 'Ratio' in config_row:
        config['ratio'] = config_row['Ratio']
    
    return config

def monkey_patch_save_figure(run_id=None, mode=None):
    """
    Monkey patch the backtesting module's figure saving functionality 
    to save directly to strategy-specific directories
    
    Args:
        run_id: Unique identifier for this run to avoid directory conflicts
        mode: Mode of the backtest (optimize, walkforward, backtest)
    """
    # Get the backtesting module
    backtesting = import_backtesting()
    
    # Save the original method
    original_savefig = backtesting.plt.savefig
    
    # Get the script directory path
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Define our new method that redirects the output path
    def custom_savefig(filename, *args, **kwargs):
        # If we have a strategy name, redirect the path
        if hasattr(backtesting, 'current_strategy_name'):
            strategy_name = backtesting.current_strategy_name
            
            # Create output path in the strategy directory - ensure it's relative to script directory
            base_dir = os.path.join(script_dir, "backtest_result")
            os.makedirs(base_dir, exist_ok=True)
            
            # Create strategy-specific directory
            strategy_dir = os.path.join(base_dir, strategy_name)
            os.makedirs(strategy_dir, exist_ok=True)
            
            # If run_id is provided, create a unique subdirectory
            if run_id:
                # Add mode to the directory name if provided
                if mode:
                    unique_dir = os.path.join(strategy_dir, f"{mode}_{run_id}")
                else:
                    unique_dir = os.path.join(strategy_dir, run_id)
                os.makedirs(unique_dir, exist_ok=True)
                strategy_dir = unique_dir
            
            # Get the basename of the file
            basename = os.path.basename(filename)
            
            # If the basename includes model name instead of strategy name,
            # replace it with the strategy name or modify as needed
            if hasattr(backtesting, 'current_model'):
                model_name = backtesting.current_model
                # If the filename starts with the model name, replace it with strategy name
                if basename.startswith(model_name):
                    basename = basename.replace(model_name, strategy_name, 1)
                
            # Create the new filename
            new_filename = os.path.join(strategy_dir, basename)
            
            # Print message showing we're redirecting
            if os.path.dirname(filename) != os.path.dirname(new_filename):
                print(f"Redirecting output: {filename} → {new_filename}")
                
            # Save to the new location
            original_savefig(new_filename, *args, **kwargs)
        else:
            # No strategy name, use original behavior
            original_savefig(filename, *args, **kwargs)
        
    # Replace the method
    backtesting.plt.savefig = custom_savefig
    
    # Store the original method for cleanup later
    backtesting._original_savefig = original_savefig

def monkey_patch_print(run_id=None, mode=None):
    """
    Monkey patch the built-in print function to intercept and modify file saving messages
    
    Args:
        run_id: Unique identifier for this run to avoid directory conflicts
        mode: Mode of the backtest (optimize, walkforward, backtest)
    """
    # Get the backtesting module
    backtesting = import_backtesting()
    
    # Get the script directory path
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    original_print = builtins.print
    
    def custom_print(*args, **kwargs):
        # Check if this is a file saving message
        if len(args) > 0 and isinstance(args[0], str):
            original_path = None
            
            # Handle regular heatmap messages
            if args[0].startswith("儲存熱力圖為 "):
                original_path = args[0].replace("儲存熱力圖為 ", "").strip()
                message_prefix = "儲存熱力圖為 "
            # Handle walkforward heatmap messages
            elif args[0].startswith("儲存 Walk Forward 熱力圖為 "):
                original_path = args[0].replace("儲存 Walk Forward 熱力圖為 ", "").strip()
                message_prefix = "儲存 Walk Forward 熱力圖為 "
            # Handle backtest equity curve messages
            elif args[0].startswith("已保存圖表到："):
                original_path = args[0].replace("已保存圖表到：", "").strip()
                message_prefix = "已保存圖表到："
            
            # If we found a path and have a strategy name, redirect
            if original_path and hasattr(backtesting, 'current_strategy_name'):
                strategy_name = backtesting.current_strategy_name
                
                # Create base strategy directory - ensure it's relative to script directory
                base_dir = os.path.join(script_dir, "backtest_result")
                os.makedirs(base_dir, exist_ok=True)
                
                # Create strategy-specific directory
                strategy_dir = os.path.join(base_dir, strategy_name)
                os.makedirs(strategy_dir, exist_ok=True)
                
                # If run_id is provided, create a unique subdirectory
                if run_id:
                    # Add mode to the directory name if provided
                    if mode:
                        unique_dir = os.path.join(strategy_dir, f"{mode}_{run_id}")
                    else:
                        unique_dir = os.path.join(strategy_dir, run_id)
                    os.makedirs(unique_dir, exist_ok=True)
                    strategy_dir = unique_dir
                
                # For equity curve charts, we need to handle entire paths differently
                if args[0].startswith("已保存圖表到："):
                    # Get just the filename without any subdirectories
                    basename = os.path.basename(original_path)
                    # If the basename includes model name, replace it with strategy name
                    if hasattr(backtesting, 'current_model'):
                        model_name = backtesting.current_model
                        if basename.startswith(model_name):
                            # Replace model name with strategy name
                            basename = basename.replace(model_name, strategy_name, 1)
                    new_path = os.path.join(strategy_dir, basename)
                else:
                    # For heatmaps, keep the existing behavior
                    basename = os.path.basename(original_path)
                    new_path = os.path.join(strategy_dir, basename)
                
                # Replace the message
                args = (f"{message_prefix}{new_path}",) + args[1:]
                
        # Call original print with possibly modified args
        return original_print(*args, **kwargs)
    
    # Replace built-in print
    builtins.print = custom_print
    
    # Store original for cleanup
    global _original_print
    _original_print = original_print

def restore_monkey_patches():
    """Restore all monkey-patched functions"""
    # Get the backtesting module
    backtesting = import_backtesting()
    
    # Restore savefig
    if hasattr(backtesting, '_original_savefig'):
        backtesting.plt.savefig = backtesting._original_savefig
        delattr(backtesting, '_original_savefig')
    
    # Restore print
    if '_original_print' in globals():
        builtins.print = globals()['_original_print']

def run_backtest(config: Dict[str, Any], mode: str = 'optimize', window: Optional[int] = None, 
                threshold: Optional[float] = None, train_ratio: float = 0.7, run_id: Optional[str] = None) -> List[str]:
    """
    Run a backtest with the specified configuration.
    
    Args:
        config: Dictionary containing configuration parameters
        mode: Mode to run ('optimize', 'walkforward', or 'backtest')
        window: Window size for backtest mode (optional if provided in config)
        threshold: Threshold for backtest mode (optional if provided in config)
        train_ratio: Ratio of training data (default: 0.7)
        run_id: Unique identifier for this run to avoid directory conflicts
        
    Returns:
        List of temporary directory paths that should be cleaned up
    """
    # Required for monkey-patching print
    import builtins
    import shutil
    
    # Get the backtesting module
    backtesting = import_backtesting()
    
    # List to collect directories that should be cleaned up later
    dirs_to_cleanup = []
    
    try:
        # Set up common parameters in backtesting module
        backtesting.resolution = config['resolution']
        
        # Handle data source specific parameters
        is_cex_data = config['api_url'].lower() in ['cex', 'bybit']
        
        # Always set metric_key - this is crucial for correct factor naming
        backtesting.metric_key = config['metric_key']
        
        # Reset factor_name to empty so backtesting.py can set it properly during data fetching
        backtesting.factor_name = ""
        
        if is_cex_data:
            # CEX-specific parameters
            backtesting.api_url = config['api_url']
            backtesting.cex_symbol = config['cex_symbol']
            backtesting.cex_underlying = config['cex_underlying']
            # Also set api_symbol and underlying to ensure compatibility
            backtesting.api_symbol = config['cex_symbol']
            backtesting.underlying = config['cex_underlying']
        else:
            # Glassnode-specific parameters
            backtesting.api_url = config['api_url']
            backtesting.api_symbol = config['api_symbol']
            backtesting.underlying = config['underlying']
        
        # IMPORTANT: Use the strategy name from our config, not the model name
        # This ensures files are saved to the strategy directory (e.g., "Cody4")
        strategy_name = config['strategy_name']
        backtesting.current_strategy_name = strategy_name
        
        # Set the model but don't use it for file paths
        model_name = config['model']
        backtesting.current_model = model_name
        backtesting.current_strategy_style = config['style'].lower()
        backtesting.current_strategy_type = config['strategy_type'].lower()
        
        # The factor name will be set correctly by backtesting.py now
        # But we still need to determine factor name for directory cleanup purposes
        if is_cex_data:
            factor_name = f"CEX {backtesting.cex_symbol} {backtesting.resolution} ({config['metric_key']})"
            original_factor_name = f"CEX_{backtesting.cex_symbol}_{backtesting.resolution}_{config['metric_key']}"
        else:
            # For Glassnode data, extract factor name from API URL like backtesting.py does
            api_url = config.get('api_url', '')
            if api_url and '/' in api_url:
                url_part = api_url.split('/')[-1]
                # Handle special formatting for options data (same as backtesting.py)
                if 'options' in url_part and 'delta' in url_part:
                    # Special handling for options delta skew metrics
                    # Convert options_25delta_skew_6_months to Options 25Delta Skew 6 months
                    formatted_name = url_part.replace('_', ' ')
                    # Capitalize first letter of each word but keep "delta" as "Delta"
                    words = formatted_name.split()
                    formatted_words = []
                    for word in words:
                        if 'delta' in word.lower():
                            # Keep the number prefix and capitalize Delta
                            formatted_words.append(word.replace('delta', 'Delta'))
                        elif word.lower() in ['months', 'days', 'weeks', 'years']:
                            # Keep time units lowercase
                            formatted_words.append(word.lower())
                        else:
                            # Capitalize first letter for other words
                            formatted_words.append(word.capitalize())
                    factor_name = ' '.join(formatted_words)
                else:
                    # Default formatting for other metrics
                    factor_name = url_part.replace('_', ' ').title()
                
                # Add metric key information if it's "/" (indicating default metric)
                if config['metric_key'] == "/":
                    factor_name += " (nan)"
            else:
                factor_name = str(config['metric_key']) if config['metric_key'] else 'close'
            original_factor_name = factor_name.replace(' ', '_')  # Store for cleanup later
        
        # For backtest mode, store window and threshold values
        if mode == 'backtest':
            backtest_window = config.get('window', window)
            backtest_threshold = config.get('threshold', threshold)
            if backtest_window is not None:
                backtesting.current_window = backtest_window
            if backtest_threshold is not None:
                backtesting.current_threshold = backtest_threshold
                
        # Create strategy-specific output directory with run_id if provided
        output_dir = create_output_directory(strategy_name, mode, run_id)
        print(f"Results will be saved to: {output_dir}")
        
        # Monkey patch figure saving and print function with run_id
        monkey_patch_save_figure(run_id=run_id, mode=mode)
        monkey_patch_print(run_id=run_id, mode=mode)
        
        # Save original SAVE_FIGURES setting
        original_save_figures = backtesting.SAVE_FIGURES
        
        # Force saving figures
        backtesting.SAVE_FIGURES = True
        
        # Also patch the ensure_output_directory function if it exists
        if hasattr(backtesting, 'ensure_output_directory'):
            original_ensure_output = backtesting.ensure_output_directory
            
            def custom_ensure_output(*args, **kwargs):
                # Just return our strategy directory
                return output_dir
                
            backtesting.ensure_output_directory = custom_ensure_output
            
        # Store original plot_backtest_results function for backtest mode
        if mode == 'backtest':
            original_plot_backtest = backtesting.plot_backtest_results
            
            # Create a custom plot_backtest_results function that saves files to the strategy directory
            def custom_plot_backtest(strategy_name, window, threshold, strategy_type, style, train_ratio=0.7):
                """
                Enhanced version of plot_backtest_results that ensures files are saved to the correct directory
                """
                # The factor name will be set correctly by backtesting.py data processing
                # Don't override it here - let backtesting.py handle it properly
                
                # Save the original SAVE_FIGURES setting
                original_save_figures = backtesting.SAVE_FIGURES
                backtesting.SAVE_FIGURES = True
                
                # Set the filename format to include strategy name
                original_basename = None
                if hasattr(backtesting, 'current_strategy_name'):
                    # Store the current value
                    original_strategy_name = backtesting.current_strategy_name
                    # Set it to our strategy name
                    backtesting.current_strategy_name = config['strategy_name']
                
                try:
                    # Call the original function
                    result = original_plot_backtest(strategy_name, window, threshold, strategy_type, style, train_ratio)
                    return result
                finally:
                    # Restore original settings
                    backtesting.SAVE_FIGURES = original_save_figures
                    if original_basename:
                        backtesting.current_basename = original_basename
                    if hasattr(backtesting, 'current_strategy_name') and 'original_strategy_name' in locals():
                        backtesting.current_strategy_name = original_strategy_name
            
            # Replace the function
            backtesting.plot_backtest_results = custom_plot_backtest
            
        # Prepare data
        since = backtesting.since
        until = backtesting.until
        
        # Fetch data using the centralized function in backtesting.py
        print(f"Fetching data for {config['strategy_name']}...")
        df_value, df_price = backtesting.fetch_and_process_data()
        
        # Process data
        backtesting.df = backtesting.prepare_data(df_value, df_price)
        backtesting.time_variable = backtesting.get_time_variable(backtesting.resolution)
        
        # Get the factor name set by backtesting.py data processing
        # This ensures we use the correct factor name with proper metric_key
        factor_name = getattr(backtesting, 'factor_name', factor_name)
        
        # Set strategy parameters based on style
        style = config['style'].lower()
        strategy_type = config['strategy_type'].lower()
        
        # Set shift periods based on resolution
        backtesting.SHIFT_PERIODS = backtesting.get_shift_periods(backtesting.resolution)
        
        # Print strategy details
        print("\n===== Backtest Configuration =====")
        print(f"Strategy Name: {config['strategy_name']}")
        
        if is_cex_data:
            print(f"Data Source: CEX (Bybit)")
            print(f"Factor: {factor_name}")
            print(f"Symbol: {backtesting.cex_symbol}")
            print(f"Underlying: {backtesting.cex_underlying}")
        else:
            print(f"Data Source: Glassnode API")
            print(f"Factor: {factor_name}")
            print(f"Metric Key: {backtesting.metric_key}")
            print(f"API Symbol: {backtesting.api_symbol}")
            print(f"Asset: {backtesting.underlying}")
        
        print(f"Resolution: {backtesting.resolution}")
        print(f"Model: {config['model']}")
        print(f"Style: {style}")
        print(f"Type: {strategy_type}")
        
        # Print time range
        start_date = pd.to_datetime(since, unit='s').strftime('%Y-%m-%d')
        end_date = pd.to_datetime(until, unit='s').strftime('%Y-%m-%d')
        print(f"Time Range: {start_date} to {end_date}")
        print(f"Training Ratio: {train_ratio * 100:.0f}%")
        print("=" * 40)
        
        if mode == 'optimize':
            print(f"\n=== Running optimization for {config['strategy_name']} ===")
            backtesting.run_strategy(mode='optimize')
        
        elif mode == 'walkforward':
            print(f"\n=== Running walk-forward analysis for {config['strategy_name']} ===")
            backtesting.run_strategy(mode='walkforward', train_ratio=train_ratio)
        
        elif mode == 'backtest':
            # For backtest mode, use window/threshold from config if available, otherwise use passed values
            backtest_window = config.get('window', window)
            backtest_threshold = config.get('threshold', threshold)
            
            if backtest_window is None or backtest_threshold is None:
                raise ValueError("Window and threshold must be provided either in the config or as arguments for backtest mode")
            
            print(f"\n=== Running backtest for {config['strategy_name']} ===")
            print(f"Window: {backtest_window}, Threshold: {backtest_threshold}")
            
            # Find the strategy model name from config
            model_name = config['model']
            
            # Check if the model exists in base_params
            if model_name not in backtesting.base_params:
                closest_match = find_closest_model(model_name, list(backtesting.base_params.keys()))
                print(f"Model '{model_name}' not found. Using closest match: '{closest_match}'")
                model_name = closest_match
                
            # Before running the backtest, save current time to identify new files
            before_time = time.time()
            
            backtesting.run_strategy(
                mode='backtest', 
                strategy_name=model_name,
                window=backtest_window, 
                threshold=backtest_threshold, 
                strategy_type=strategy_type, 
                style=style,
                train_ratio=train_ratio
            )
            
            # After running backtest, find and copy all newly created files to our output directory
            # Get the script directory path for consistent paths
            script_dir = os.path.dirname(os.path.abspath(__file__))
            
            # Create a list of possible directories where backtest results might be saved
            search_directories = [
                # Check model directories (with and without spaces)
                os.path.join(script_dir, "backtest_result", model_name.replace(' ', '_')),
                os.path.join(script_dir, "backtest_result", model_name),
                # Check factor directories
                os.path.join(script_dir, "backtest_result", original_factor_name),
                os.path.join(script_dir, "backtest_result", factor_name),
                # Check strategy directories
                os.path.join(script_dir, "backtest_result", strategy_name),
                # Check all subdirectories of backtest_result (for any other places)
                os.path.join(script_dir, "backtest_result")
            ]
            
            # Track if we found and copied any files
            found_any_files = False
            
            # First, process direct directories we know about
            for search_dir in search_directories:
                if os.path.exists(search_dir) and os.path.isdir(search_dir):
                    # If this is the base backtest_result directory, we need to search subdirectories
                    if search_dir == os.path.join(script_dir, "backtest_result"):
                        # Look through all subdirectories
                        for dir_name in os.listdir(search_dir):
                            subdir_path = os.path.join(search_dir, dir_name)
                            if os.path.isdir(subdir_path) and dir_name != strategy_name:
                                if subdir_path.endswith(strategy_name):
                                    # Skip directories with the strategy name to avoid duplicates
                                    continue
                                # Process files in this subdirectory
                                for filename in os.listdir(subdir_path):
                                    src = os.path.join(subdir_path, filename)
                                    # Check if this is a PNG file created after we started the backtest
                                    if (os.path.isfile(src) and
                                        filename.endswith('.png') and
                                        os.path.getmtime(src) >= before_time):
                                        
                                        # FIX: Only copy files that match the current model name
                                        # This prevents copying files from other strategies/models
                                        if model_name in filename:
                                            # Copy the file to our output directory
                                            dst = os.path.join(output_dir, filename)
                                            if not os.path.exists(dst) or os.path.getmtime(src) > os.path.getmtime(dst):
                                                shutil.copy2(src, dst)
                                                print(f"Copied backtest result: {src} → {dst}")
                                        found_any_files = True
                    else:
                        # Process files directly in this directory
                        for filename in os.listdir(search_dir):
                            src = os.path.join(search_dir, filename)
                            # Check if this is a PNG file created after we started the backtest
                            if (os.path.isfile(src) and
                                filename.endswith('.png') and
                                os.path.getmtime(src) >= before_time):
                                # FIX: Only copy files that match the current model name
                                # This prevents copying files from other strategies/models
                                if model_name in filename:
                                    # Copy the file to our output directory
                                    dst = os.path.join(output_dir, filename)
                                    if not os.path.exists(dst) or os.path.getmtime(src) > os.path.getmtime(dst):
                                        shutil.copy2(src, dst)
                                        print(f"Copied backtest result: {src} → {dst}")
                                        found_any_files = True
            # If we didn't find any files, search the entire backtest_result directory for PNG files
            if not found_any_files:
                print("No backtest result files found in expected locations. Searching all directories...")
                
                backtest_result_dir = os.path.join(script_dir, "backtest_result")
                for root, dirs, files in os.walk(backtest_result_dir):
                    # Skip our output directory to avoid copying between strategy subdirectories
                    if root == output_dir:
                        continue
                        
                    for filename in files:
                        if filename.endswith('.png'):
                            src = os.path.join(root, filename)
                            # Check if the file was created after we started the backtest
                            if os.path.getmtime(src) >= before_time:
                                # FIX: Only copy files that match the current model name
                                # This prevents copying files from other strategies/models
                                if model_name in filename:
                                    dst = os.path.join(output_dir, filename)
                                    if not os.path.exists(dst) or os.path.getmtime(src) > os.path.getmtime(dst):
                                        shutil.copy2(src, dst)
                                        print(f"Copied backtest result: {src} → {dst}")
                                        found_any_files = True
            if not found_any_files:
                print("Warning: No backtest result files found to copy. Check if the backtest generated any output.")
                print("Warning: No backtest result files found to copy. Check if the backtest generated any output.")
    
    finally:
        # Restore original plot_backtest_results if we modified it
        if mode == 'backtest' and 'original_plot_backtest' in locals():
            backtesting.plot_backtest_results = original_plot_backtest
        
        # Restore all monkey patches
        restore_monkey_patches()
        
        # Restore original SAVE_FIGURES setting
        if 'original_save_figures' in locals():
            backtesting.SAVE_FIGURES = original_save_figures
            
        # Restore ensure_output_directory if we patched it
        if hasattr(backtesting, 'ensure_output_directory') and 'original_ensure_output' in locals():
            backtesting.ensure_output_directory = original_ensure_output
        
        # Identify directories that should be cleaned up later
        try:
            # Determine the factor name based on data source
            is_cex_data = config['api_url'].lower() in ['cex', 'bybit']
            
            if is_cex_data:
                # For CEX data
                if hasattr(backtesting, 'cex_symbol'):
                    cex_symbol = backtesting.cex_symbol
                    factor_name = f"CEX {cex_symbol} {backtesting.resolution} ({config['metric_key']})"
                    original_factor_name = f"CEX_{cex_symbol}_{backtesting.resolution}_{config['metric_key']}"
                else:
                    # Fallback using config values
                    cex_symbol = config.get('cex_symbol', config.get('api_symbol'))
                    factor_name = f"CEX {cex_symbol} {config['resolution']} ({config['metric_key']})"
                    original_factor_name = f"CEX_{cex_symbol}_{config['resolution']}_{config['metric_key']}"
            else:
                # For Glassnode data
                factor_name = config['metric_key']
                original_factor_name = factor_name.replace(' ', '_')
            
            # Get the script directory path for consistent paths
            script_dir = os.path.dirname(os.path.abspath(__file__))
            
            # Add the API URL factor directory to cleanup list
            factor_dir = os.path.join(script_dir, "backtest_result", original_factor_name)
            if os.path.exists(factor_dir) and os.path.basename(factor_dir) != strategy_name:
                dirs_to_cleanup.append(factor_dir)
            
            # Add any variation of the factor name (with different capitalizations)
            variations = [
                original_factor_name,
                original_factor_name.lower(),
                original_factor_name.upper(),
                original_factor_name.capitalize(),
                original_factor_name.replace('_', '')
            ]
            
            for var in variations:
                var_dir = os.path.join(script_dir, "backtest_result", var)
                if os.path.exists(var_dir) and var_dir not in dirs_to_cleanup and os.path.basename(var_dir) != strategy_name:
                    dirs_to_cleanup.append(var_dir)
            
            # Add the model directory to the cleanup list if it exists
            model_name = config['model']
            model_dir = os.path.join(script_dir, "backtest_result", model_name.replace(' ', '_'))
            if os.path.exists(model_dir) and os.path.basename(model_dir) != strategy_name:
                dirs_to_cleanup.append(model_dir)
            
            # Also check for the model directory with spaces
            model_dir_with_spaces = os.path.join(script_dir, "backtest_result", model_name)
            if os.path.exists(model_dir_with_spaces) and os.path.basename(model_dir_with_spaces) != strategy_name:
                dirs_to_cleanup.append(model_dir_with_spaces)
                
            # Check for combined strategy_factor directories
            combined_dir = os.path.join(script_dir, "backtest_result", f"{strategy_name}_{original_factor_name}")
            if os.path.exists(combined_dir) and os.path.basename(combined_dir) != strategy_name:
                dirs_to_cleanup.append(combined_dir)
                
            # List all directories in backtest_result
            base_dir = os.path.join(script_dir, "backtest_result")
            if os.path.exists(base_dir):
                for dir_name in os.listdir(base_dir):
                    dir_path = os.path.join(base_dir, dir_name)
                    
                    # Skip the strategy-specific directories
                    if dir_name == strategy_name:
                        continue
                    
                    # Include directories that match additional patterns:
                    # 1. Any directory with underscores (likely a factor name)
                    # 2. Any directory starting with known metric prefixes
                    # 3. Any directory that matches the model name or factor name pattern
                    if os.path.isdir(dir_path) and (
                        "_" in dir_name or
                        dir_name.startswith(("Mvrv", "Options", "Derivatives", "Metrics", "CEX", "Bybit")) or
                        dir_name.startswith(strategy_name + "_") or
                        dir_name.startswith(model_name) or
                        dir_name == original_factor_name or
                        dir_name == model_name.replace(' ', '_')
                    ):
                        dirs_to_cleanup.append(dir_path)
                
        except Exception as e:
            print(f"Warning: Failed to identify temporary directories: {e}")
    
    return dirs_to_cleanup

def find_closest_model(model_name: str, available_models: List[str]) -> str:
    """Find the closest matching model name from available models"""
    import difflib
    return difflib.get_close_matches(model_name, available_models, n=1, cutoff=0.1)[0]

def display_available_strategies(csv_file: str) -> None:
    """
    Display all available strategies in the CSV file.
    Always regenerate CSV from the corresponding Excel file when available.
    
    Args:
        csv_file: Path to the CSV configuration file
    """
    # Always try to generate from Excel first
    excel_file = csv_file.replace('.csv', '.xlsx')
    if os.path.exists(excel_file):
        print(f"Regenerating {csv_file} from {excel_file}")
        generate_csv_from_excel(excel_file, csv_file)
    
    # Check if CSV exists (either pre-existing or just generated)
    if not os.path.exists(csv_file):
        raise FileNotFoundError(f"Neither CSV ({csv_file}) nor Excel ({excel_file}) files found")
    
    try:
        # Read the CSV file
        df = pd.read_csv(csv_file)
        strategies = df['Strategy Name'].unique().tolist()
        
        print("\n===== CONFIGURATION FILE INFORMATION =====")
        print(f"File: {csv_file}")
        print(f"Found {len(strategies)} strategies:")
        
        for i, strategy in enumerate(strategies, 1):
            strategy_df = df[df['Strategy Name'] == strategy]
            model = strategy_df['Model'].iloc[0]
            style = strategy_df['Style'].iloc[0]
            strategy_type = strategy_df['Type'].iloc[0]
            print(f"  {i}. {strategy} - Model: {model}, Style: {style}, Type: {strategy_type}")
        
        print("=" * 40)
        return strategies
        
    except Exception as e:
        print(f"Error reading configuration file: {e}")
        return []

def extract_best_parameters(strategy_dir: str, strategy_type: str, style: str) -> Tuple[Optional[int], Optional[float]]:
    """
    Extract the best parameters (window, threshold) from optimization results.
    
    Args:
        strategy_dir: Path to the strategy directory
        strategy_type: Strategy type (long_only, short_only, long_short)
        style: Strategy style (momentum, reversion)
        
    Returns:
        Tuple of (window, threshold) or (None, None) if not found
    """
    # Convert style and type to short forms used in filenames
    style_short = 'M' if style.lower() == 'momentum' else 'R'
    
    if strategy_type.lower() == 'long_only':
        type_short = 'L'
    elif strategy_type.lower() == 'short_only':
        type_short = 'S'
    else:
        type_short = 'LS'
    
    # Look for heatmap files with good factors
    best_window, best_threshold = None, None
    
    # Try to find CSV files with optimization results
    for filename in os.listdir(strategy_dir):
        if filename.endswith('.csv') and 'good_factors' in filename:
            try:
                # Load the CSV file
                csv_path = os.path.join(strategy_dir, filename)
                results_df = pd.read_csv(csv_path)
                
                # Check if this contains results for our specific style and type
                style_type_match = False
                for _, row in results_df.iterrows():
                    if 'Strategy' in row:
                        strategy_desc = str(row['Strategy'])
                        if (style.lower() in strategy_desc.lower() and 
                            strategy_type.lower().replace('_', ' ') in strategy_desc.lower()):
                            style_type_match = True
                            break
                
                if not style_type_match:
                    continue
                    
                # If we found a match, sort by Sharpe ratio and take the best one
                if 'sr' in results_df.columns and 'Window' in results_df.columns and 'Threshold' in results_df.columns:
                    best_row = results_df.sort_values('sr', ascending=False).iloc[0]
                    best_window = int(best_row['Window'])
                    best_threshold = float(best_row['Threshold'])
                    print(f"Found best parameters from {filename}: Window={best_window}, Threshold={best_threshold}")
                    return best_window, best_threshold
            except Exception as e:
                print(f"Error reading {filename}: {e}")
                continue
    
    # If no CSV files found, try to extract from image filenames
    for filename in os.listdir(strategy_dir):
        # Look for heatmap images with the right style and type
        if (filename.endswith('.png') and 
            f"_{style_short}_{type_short}_" in filename and
            'good_factors' in filename):
            try:
                # Try to extract window and threshold from filename
                # Format might be: heatmap_BTC_24h_above_100k_M_L_1_good_factors_timestamp.png
                parts = filename.split('_')
                for i, part in enumerate(parts):
                    if part == style_short and i < len(parts) - 1 and parts[i+1] == type_short:
                        # Found style and type markers
                        if i + 2 < len(parts) and parts[i+2].isdigit():
                            # This might be the window
                            best_window = int(parts[i+2])
                            # And if there's another number after this, it might be the threshold
                            if i + 3 < len(parts) and parts[i+3].replace('.', '', 1).isdigit():
                                best_threshold = float(parts[i+3])
                                print(f"Extracted parameters from filename {filename}: Window={best_window}, Threshold={best_threshold}")
                                return best_window, best_threshold
            except Exception as e:
                print(f"Error extracting parameters from {filename}: {e}")
                continue
    
    # If we can't find anything, return None, None
    return None, None

def run_strategy_with_mode(strategy_name: str, config_file: str, mode: str, window: Optional[int] = None,
                          threshold: Optional[float] = None, train_ratio: float = 0.7, 
                          run_id: Optional[str] = None) -> Tuple[List[str], bool]:
    """
    Run a single strategy with a specific mode.
    This function is designed to be used as a target for multiprocessing.
    
    Args:
        strategy_name: Name of the strategy to run
        config_file: Path to the CSV configuration file
        mode: Mode to run ('optimize', 'walkforward', or 'backtest')
        window: Window size for backtest mode
        threshold: Threshold for backtest mode
        train_ratio: Ratio of training data
        run_id: Unique identifier for this run to avoid directory conflicts
        
    Returns:
        Tuple of (temporary directory paths, success flag)
    """
    success = False
    try:
        # Create a unique run ID for this process if not provided
        if run_id is None:
            run_id = f"{mode}_{uuid.uuid4().hex[:8]}"
        
        # Load configuration for this strategy
        try:
            # Use os.path.abspath to resolve the path before loading the config
            config_file_path = os.path.abspath(config_file)
            config = load_config_from_csv(config_file_path, strategy_name)
        except FileNotFoundError as e:
            print(f"\n===== CONFIGURATION ERROR =====")
            print(f"Could not find configuration file: {str(e)}")
            print("\nWhat to do next:")
            print("- Check the file path and make sure the CSV or Excel file exists")
            print("- Ensure the file has the correct format with required columns")
            return [], success
        except ValueError as e:
            print(f"\n===== STRATEGY ERROR =====")
            print(f"Error with strategy configuration: {str(e)}")
            print("\nWhat to do next:")
            print("- Verify the strategy name exists in the configuration file")
            print("- Check that all required parameters are defined for this strategy")
            return [], success
        except Exception as e:
            print(f"\n===== CONFIGURATION LOADING ERROR =====")
            print(f"Failed to load configuration: {str(e)}")
            print("\nWhat to do next:")
            print("- Check the format of your configuration file")
            print("- Ensure all required columns are present")
            import traceback
            traceback.print_exc()
            return [], success
        
        # Build arguments for run_backtest
        kwargs = {
            'config': config,
            'mode': mode,
            'train_ratio': train_ratio,
            'run_id': run_id
        }
        
        # Add window and threshold if provided
        if window is not None:
            kwargs['window'] = window
        if threshold is not None:
            kwargs['threshold'] = threshold
            
        # For backtest mode, we need to ensure we have window/threshold parameters
        if mode == 'backtest':
            # First check if parameters are in config
            if 'window' in config and 'window' not in kwargs:
                kwargs['window'] = config['window']
            if 'threshold' in config and 'threshold' not in kwargs:
                kwargs['threshold'] = config['threshold']
            
            # If still missing parameters, try to extract from optimization results
            if 'window' not in kwargs or 'threshold' not in kwargs:
                # Get the script directory path
                script_dir = os.path.dirname(os.path.abspath(__file__))
                strategy_dir = os.path.join(script_dir, "backtest_result", strategy_name)
                
                if os.path.exists(strategy_dir):
                    best_window, best_threshold = extract_best_parameters(
                        strategy_dir, config['strategy_type'], config['style']
                    )
                    
                    if best_window is not None and 'window' not in kwargs:
                        kwargs['window'] = best_window
                        print(f"Using best window={best_window} from optimization results for {strategy_name}")
                    
                    if best_threshold is not None and 'threshold' not in kwargs:
                        kwargs['threshold'] = best_threshold
                        print(f"Using best threshold={best_threshold} from optimization results for {strategy_name}")
            
            # If we still don't have window/threshold, use defaults
            if 'window' not in kwargs:
                # Use a reasonable default window based on resolution
                resolution = config.get('resolution', '24h')
                if resolution == '1h':
                    kwargs['window'] = 24  # 1 day for hourly data
                elif resolution == '10m':
                    kwargs['window'] = 144  # 1 day for 10-min data
                else:
                    kwargs['window'] = 60  # Default for daily data
                print(f"Warning: No window parameter found. Using default window={kwargs['window']} for {strategy_name}")
            
            if 'threshold' not in kwargs:
                kwargs['threshold'] = 1.0  # Default threshold
                print(f"Warning: No threshold parameter found. Using default threshold={kwargs['threshold']} for {strategy_name}")
        
        # Run the backtest with the appropriate mode
        print(f"Starting {mode} mode for strategy {strategy_name} (Process ID: {os.getpid()}, Run ID: {run_id})")
        
        # Determine data source type for better logging
        is_cex_data = config['api_url'].lower() in ['cex', 'bybit']
        data_source = "CEX (Bybit)" if is_cex_data else "Glassnode API"
        print(f"Data Source: {data_source}")
        
        if is_cex_data:
            print(f"Symbol: {config.get('cex_symbol', config.get('api_symbol'))}")
        else:
            print(f"API URL: {config['api_url']}")
            print(f"API Symbol: {config['api_symbol']}")
            print(f"Metric Key: {config['metric_key']}")
        
        # Run the backtest with the appropriate mode
        dirs_to_cleanup = run_backtest(**kwargs)
        
        print(f"Completed {mode} mode for strategy {strategy_name}")
        success = True
        return dirs_to_cleanup, success
    
    except requests.exceptions.JSONDecodeError as e:
        print(f"\n===== API RESPONSE ERROR for {strategy_name} =====")
        print(f"Failed to decode JSON from API response: {str(e)}")
        print("\nWhat to do next:")
        print("- Check your API key in the backtesting.py file")
        print("- Verify your internet connection")
        print("- Wait a few minutes and try again")
        print("- Check if you've exceeded API rate limits")
        import traceback
        traceback.print_exc()
        return [], success
        
    except requests.exceptions.RequestException as e:
        print(f"\n===== API CONNECTION ERROR for {strategy_name} =====")
        print(f"Failed to connect to API: {str(e)}")
        print("\nWhat to do next:")
        print("- Check your internet connection")
        print("- Verify the API endpoints are correct")
        print("- Wait a few minutes and try again")
        import traceback
        traceback.print_exc()
        return [], success
        
    except ImportError as e:
        print(f"\n===== MODULE ERROR for {strategy_name} =====")
        print(f"Failed to import required module: {str(e)}")
        print("\nWhat to do next:")
        print("- Ensure all dependencies are installed")
        print("- Check that backtesting.py exists in the correct location")
        import traceback
        traceback.print_exc()
        return [], success
        
    except Exception as e:
        print(f"\n===== ERROR running {mode} mode for strategy {strategy_name} =====")
        print(f"Error: {str(e)}")
        print("\nWhat to do next:")
        print("- Check the error message and traceback for specific issues")
        print("- Verify your configuration settings")
        print("- Try running again with a different strategy or mode")
        import traceback
        traceback.print_exc()
        return [], success

def cleanup_temp_directories(dirs_to_cleanup: List[str], config_file: str = 'fund/fund_bybit.csv') -> None:
    """
    Instead of cleaning up temporary directories, create a timestamped backup of results.
    Only creates an archive if there are valid results to archive.
    Always regenerate CSV from the corresponding Excel file when available.
    
    Args:
        dirs_to_cleanup: List of directory paths (not used, kept for compatibility)
        config_file: Path to the CSV configuration file (to identify valid strategy names)
    """
    # Get the script directory path
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Get list of valid strategy names from CSV file
    valid_strategies = []
    try:
        # Resolve the path to the CSV file using absolute paths
        config_file_path = os.path.abspath(config_file)
        
        # Always try to generate from Excel first
        excel_path = config_file_path.replace('.csv', '.xlsx')
        if os.path.exists(excel_path):
            print(f"Regenerating {config_file_path} from {excel_path}")
            generate_csv_from_excel(excel_path, config_file_path)
        
        # Check if CSV exists (either pre-existing or just generated)
        if not os.path.exists(config_file_path):
            print(f"Warning: Neither CSV ({config_file_path}) nor Excel ({excel_path}) found. Cannot determine valid strategy names.")
            valid_strategies = []
        else:
            # CSV exists, read it directly
            df = pd.read_csv(config_file_path)
            valid_strategies = df['Strategy Name'].unique().tolist()
            print(f"Valid strategy names from CSV: {', '.join(valid_strategies)}")
    except Exception as e:
        print(f"Warning: Failed to read strategy names from CSV or Excel: {e}")
    
    # Check if backtest_result directory exists - relative to script directory
    base_dir = os.path.join(script_dir, "backtest_result")
    if not os.path.exists(base_dir):
        print("No backtest results to archive.")
        return
    
    # First check if we have any valid strategy folders to archive
    strategies_to_archive = []
    for strategy_name in valid_strategies:
        source_dir = os.path.join(base_dir, strategy_name)
        if os.path.exists(source_dir) and os.path.isdir(source_dir):
            strategies_to_archive.append(strategy_name)
    
    if not strategies_to_archive:
        print("\nNo strategy folders found to archive.")
        return
    
    # Create a timestamped directory only if we have strategies to archive
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    archive_dir = os.path.join(script_dir, f"backtest_result_{timestamp}")
    
    # Create the archive directory
    os.makedirs(archive_dir, exist_ok=True)
    print(f"\nCreating archive directory: {archive_dir}")
    
    # Copy strategy folders to the archive
    copied_count = 0
    for strategy_name in strategies_to_archive:
        source_dir = os.path.join(base_dir, strategy_name)
        target_dir = os.path.join(archive_dir, strategy_name)
        try:
            print(f"Archiving: {source_dir} → {target_dir}")
            shutil.copytree(source_dir, target_dir)
            copied_count += 1
        except Exception as e:
            print(f"Failed to archive {source_dir}: {e}")
    
    if copied_count > 0:
        print(f"\nArchiving completed. {copied_count} strategy folders archived to: {archive_dir}")
    else:
        # This should not happen, but just in case
        print("\nNo strategy folders were successfully archived.")
        try:
            os.rmdir(archive_dir)
            print(f"Removed empty archive directory: {archive_dir}")
        except:
            pass

def run_all_strategies(config_file: str, modes: List[str] = None, parallel: bool = False, 
                      max_workers: int = None, delay_between_tasks: float = 1.0) -> Dict[str, Dict[str, bool]]:
    """
    Run all strategies in the config file with specified modes.
    
    Args:
        config_file: Path to the CSV configuration file
        modes: List of modes to run (default: all three modes)
        parallel: Whether to run in parallel using multiprocessing
        max_workers: Maximum number of worker processes to use (default: CPU count)
        delay_between_tasks: Delay in seconds between starting tasks (to avoid API rate limits)
        
    Returns:
        Dictionary with results of each strategy and mode
    """
    # Default to all modes if none specified
    if modes is None:
        modes = ['optimize', 'walkforward', 'backtest']
    
    # Validate modes
    valid_modes = ['optimize', 'walkforward', 'backtest']
    for mode in modes:
        if mode not in valid_modes:
            raise ValueError(f"Invalid mode: {mode}. Must be one of {valid_modes}")
    
    # Resolve the path to the CSV file
    script_dir = os.path.dirname(os.path.abspath(__file__))
    csv_file = os.path.join(script_dir, config_file)
    
    # Check if CSV exists, generate from Excel if needed
    if not os.path.exists(csv_file):
        excel_file = csv_file.replace('.csv', '.xlsx')
        if not generate_csv_from_excel(excel_file, csv_file):
            raise FileNotFoundError(f"Neither CSV ({csv_file}) nor Excel ({excel_file}) files found")
    
    # Load all strategies from the CSV file
    df = pd.read_csv(csv_file)
    strategies = df['Strategy Name'].unique().tolist()
    
    if not strategies:
        print("No strategies found in the config file.")
        return {}
    
    # Group strategies by data source for better logging and rate limiting
    cex_strategies = []
    glassnode_strategies = []
    
    # Identify which strategies use which data source
    for strategy_name in strategies:
        strategy_df = df[df['Strategy Name'] == strategy_name]
        api_url = strategy_df['API'].iloc[0]
        if str(api_url).lower() in ['cex', 'bybit', 'bybit.com', 'exchange', 'cex_data']:
            cex_strategies.append(strategy_name)
        else:
            glassnode_strategies.append(strategy_name)
    
    print(f"Found {len(strategies)} strategies: {', '.join(strategies)}")
    print(f"CEX strategies: {len(cex_strategies)}")
    print(f"Glassnode strategies: {len(glassnode_strategies)}")
    print(f"Will run the following modes: {', '.join(modes)}")
    
    # Generate all the tasks to run (strategy + mode combinations)
    tasks = []
    for strategy_name in strategies:
        for mode in modes:
            # Generate a unique run ID for each strategy and mode combination
            run_id = f"{strategy_name}_{mode}_{uuid.uuid4().hex[:6]}"
            tasks.append((strategy_name, csv_file, mode, run_id))
    
    print(f"Total tasks to run: {len(tasks)}")
    
    # Dictionary to track results
    results = {strategy: {mode: False for mode in modes} for strategy in strategies}
    
    # List to collect all temporary directories that need to be cleaned up
    all_dirs_to_cleanup = []
    
    if parallel and len(tasks) > 1:
        # Determine number of workers
        if max_workers is None:
            # Limit the maximum workers to avoid overwhelming the API
            default_workers = min(multiprocessing.cpu_count(), 3)
            max_workers = default_workers
        max_workers = min(max_workers, len(tasks))
        
        print(f"Running in parallel with {max_workers} workers")
        print(f"Using {delay_between_tasks}s delay between task submissions to avoid API rate limits")
        
        # Run tasks in parallel with controlled submission rate
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            futures = []
            
            # Submit CEX tasks first (often less rate-limited)
            cex_tasks = [(s, c, m, r) for s, c, m, r in tasks if s in cex_strategies]
            glassnode_tasks = [(s, c, m, r) for s, c, m, r in tasks if s in glassnode_strategies]
            
            # Sort tasks logically - first by strategy, then by mode (optimize -> walkforward -> backtest)
            cex_tasks.sort(key=lambda x: (x[0], {'optimize': 0, 'walkforward': 1, 'backtest': 2}[x[2]]))
            glassnode_tasks.sort(key=lambda x: (x[0], {'optimize': 0, 'walkforward': 1, 'backtest': 2}[x[2]]))
            
            ordered_tasks = cex_tasks + glassnode_tasks
            
            # Submit tasks in the ordered sequence
            for i, (strategy_name, csv_file, mode, run_id) in enumerate(ordered_tasks):
                # Submit the task
                future = executor.submit(run_strategy_with_mode, strategy_name, csv_file, mode, run_id=run_id)
                futures.append((future, strategy_name, mode))
                
                # Add a delay between submissions to avoid overwhelming the API
                if i < len(ordered_tasks) - 1:  # No need to delay after the last task
                    # Add a small random jitter to avoid synchronized requests
                    jitter = random.uniform(0, 0.5)
                    delay = delay_between_tasks + jitter
                    
                    # Add extra delay for Glassnode API which has stricter rate limits
                    next_strategy = ordered_tasks[i+1][0]
                    if next_strategy in glassnode_strategies and strategy_name in glassnode_strategies:
                        delay *= 1.5  # 50% longer delay between Glassnode tasks
                    
                    # Add longer delay between different symbols for CEX to avoid rate limits 
                    elif next_strategy in cex_strategies and strategy_name in cex_strategies:
                        # Load configs to check if symbols are different
                        current_config = load_config_from_csv(csv_file, strategy_name)
                        next_config = load_config_from_csv(csv_file, next_strategy)
                        
                        if (current_config.get('cex_symbol') != next_config.get('cex_symbol') or 
                            current_config.get('cex_underlying') != next_config.get('cex_underlying')):
                            # If different symbols, add more delay
                            delay *= 1.2
                            print(f"Adding extra delay between different CEX symbols")
                        
                    print(f"Waiting {delay:.1f}s before submitting next task...")
                    time.sleep(delay)
            
            # Wait for all tasks to complete and collect dirs to cleanup
            for future, strategy_name, mode in futures:
                try:
                    dirs_to_cleanup, success = future.result()
                    all_dirs_to_cleanup.extend(dirs_to_cleanup)
                    results[strategy_name][mode] = success
                except Exception as e:
                    print(f"Task for {strategy_name} with {mode} mode failed: {str(e)}")
                    results[strategy_name][mode] = False
    else:
        print("Running sequentially")
        
        # Sort tasks logically - first by strategy, then by mode (optimize -> walkforward -> backtest)
        sorted_tasks = sorted(tasks, key=lambda x: (x[0], {'optimize': 0, 'walkforward': 1, 'backtest': 2}[x[2]]))
        
        # Run tasks sequentially
        for i, (strategy_name, csv_file, mode, run_id) in enumerate(sorted_tasks):
            print(f"Running task {i+1}/{len(sorted_tasks)}: {strategy_name} with {mode} mode")
            dirs_to_cleanup, success = run_strategy_with_mode(strategy_name, csv_file, mode, run_id=run_id)
            all_dirs_to_cleanup.extend(dirs_to_cleanup)
            results[strategy_name][mode] = success
            
            # Add a delay between tasks to avoid API rate limits
            if i < len(sorted_tasks) - 1:  # No need to delay after the last task
                # Add a small random jitter to avoid synchronized requests
                jitter = random.uniform(0, 0.5)
                delay = delay_between_tasks + jitter
                
                # Add extra delay for Glassnode API which has stricter rate limits
                next_strategy = sorted_tasks[i+1][0]
                if next_strategy in glassnode_strategies and strategy_name in glassnode_strategies:
                    delay *= 1.5  # 50% longer delay between Glassnode tasks
                    
                print(f"Waiting {delay:.1f}s before starting next task...")
                time.sleep(delay)
    
    # After all tasks are completed, archive strategy results
    cleanup_temp_directories(all_dirs_to_cleanup, config_file=config_file)
    
    return results

def display_results_summary(results: Dict[str, Dict[str, bool]]) -> None:
    """
    Display a summary of successful and failed strategies.
    
    Args:
        results: Dictionary with results of each strategy and mode
    """
    if not results:
        print("No results to display.")
        return
    
    # Try to identify data sources for the strategies
    cex_strategies = []
    glassnode_strategies = []
    strategy_symbols = {}  # Store symbols for each strategy
    
    # Try to determine data source from the strategy name by looking at the backtest_result directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    base_dir = os.path.join(script_dir, "backtest_result")
    
    # Find the CSV file to read symbol information
    csv_file = os.path.join(script_dir, "fund", "fund_bybit.csv")
    if not os.path.exists(csv_file):
        # Try alternative locations
        alternative_paths = [
            os.path.join(os.path.dirname(script_dir), "fund", "fund_bybit.csv"),
            os.path.join(script_dir, "..", "fund", "fund_bybit.csv")
        ]
        for alt_path in alternative_paths:
            if os.path.exists(alt_path):
                csv_file = alt_path
                break
    
    # If CSV exists, read symbol information
    if os.path.exists(csv_file):
        try:
            df = pd.read_csv(csv_file)
            for strategy in results.keys():
                if strategy in df['Strategy Name'].values:
                    row = df[df['Strategy Name'] == strategy].iloc[0]
                    
                    # Check if it's CEX or Glassnode based on API column
                    if str(row['API']).lower() in ['bybit', 'cex', 'bybit.com', 'exchange', 'cex_data']:
                        cex_strategies.append(strategy)
                        symbol = row['API Symbol']
                        underlying = row['Asset']
                        strategy_symbols[strategy] = f"{symbol}/{underlying}"
                    else:
                        glassnode_strategies.append(strategy)
                        symbol = row['API Symbol']
                        strategy_symbols[strategy] = symbol
        except Exception as e:
            print(f"Error reading CSV file: {e}")
    
    # If we couldn't determine from CSV, check for files in results directory
    for strategy in results.keys():
        if strategy not in cex_strategies and strategy not in glassnode_strategies:
            strategy_dir = os.path.join(base_dir, strategy)
            
            if os.path.exists(strategy_dir):
                # Look for files that might indicate the data source
                is_cex = False
                for root, dirs, files in os.walk(strategy_dir):
                    for file in files:
                        if "CEX" in file or "Bybit" in file:
                            is_cex = True
                            break
                    if is_cex:
                        break
                
                if is_cex:
                    cex_strategies.append(strategy)
                else:
                    glassnode_strategies.append(strategy)
    
    print("\n===== RESULTS SUMMARY =====")
    print(f"{'Strategy':<15} | {'Source':<12} | {'Symbol':<12} | {'Optimize':<10} | {'Walkforward':<12} | {'Backtest':<10}")
    print("-" * 75)
    
    # Count successful and failed runs
    total_success = 0
    total_runs = 0
    
    # Sort strategies by data source type first, then by name
    sorted_strategies = sorted(results.keys(), key=lambda s: (s not in cex_strategies, s))
    
    for strategy in sorted_strategies:
        modes = results[strategy]
        
        # Determine data source
        source = "CEX" if strategy in cex_strategies else "Glassnode" if strategy in glassnode_strategies else "Unknown"
        
        # Get symbol info
        symbol = strategy_symbols.get(strategy, "")
        
        optimize = "✓ Success" if modes.get('optimize', False) else "✗ Failed" if 'optimize' in modes else "- Skipped"
        walkforward = "✓ Success" if modes.get('walkforward', False) else "✗ Failed" if 'walkforward' in modes else "- Skipped"
        backtest = "✓ Success" if modes.get('backtest', False) else "✗ Failed" if 'backtest' in modes else "- Skipped"
        
        print(f"{strategy:<15} | {source:<12} | {symbol:<12} | {optimize:<10} | {walkforward:<12} | {backtest:<10}")
        
        # Update counters
        for mode, success in modes.items():
            total_runs += 1
            if success:
                total_success += 1
    
    print("-" * 75)
    success_rate = (total_success / total_runs * 100) if total_runs > 0 else 0
    print(f"Overall: {total_success}/{total_runs} tasks completed successfully ({success_rate:.1f}%)")
    
    # Show summary by data source
    if cex_strategies and glassnode_strategies:
        print("\n===== SUMMARY BY DATA SOURCE =====")
        
        # Count CEX successes
        cex_success = 0
        cex_total = 0
        for strategy in cex_strategies:
            for mode, success in results[strategy].items():
                cex_total += 1
                if success:
                    cex_success += 1
        
        # Count Glassnode successes
        glassnode_success = 0
        glassnode_total = 0
        for strategy in glassnode_strategies:
            for mode, success in results[strategy].items():
                glassnode_total += 1
                if success:
                    glassnode_success += 1
        
        cex_rate = (cex_success / cex_total * 100) if cex_total > 0 else 0
        glassnode_rate = (glassnode_success / glassnode_total * 100) if glassnode_total > 0 else 0
        
        print(f"CEX: {cex_success}/{cex_total} tasks completed successfully ({cex_rate:.1f}%)")
        print(f"Glassnode: {glassnode_success}/{glassnode_total} tasks completed successfully ({glassnode_rate:.1f}%)")
    
    print("=" * 75)

def display_menu() -> str:
    """
    Display a user-friendly menu and get the user's selection.
    
    Returns:
        Selected mode as a string
    """
    print("\n===== AUTOTEST MENU =====")
    print("1. Optimize (Find best parameters)")
    print("2. Walkforward (Test parameter stability)")
    print("3. Backtest (Run with specific parameters)")
    print("4. Full (Run all three modes for one strategy)")
    print("5. All (Run all modes for all strategies)")
    print("6. List Strategies")
    print("0. Exit")
    
    choice = input("\nEnter your choice (0-6): ")
    
    mode_map = {
        "1": "optimize",
        "2": "walkforward",
        "3": "backtest",
        "4": "full",
        "5": "all",
        "6": "list",
        "0": "exit"
    }
    
    return mode_map.get(choice, "invalid")

def display_file_info(csv_file: str) -> List[str]:
    """
    Display information about the file being processed and the available strategies.
    Always regenerate CSV from the corresponding Excel file when available.
    
    Args:
        csv_file: Path to the CSV configuration file
        
    Returns:
        List of strategy names in the CSV file
    """
    strategies = []
    
    # Always try to generate from Excel first
    excel_file = csv_file.replace('.csv', '.xlsx')
    if os.path.exists(excel_file):
        print(f"Regenerating {csv_file} from {excel_file}")
        generate_csv_from_excel(excel_file, csv_file)
    
    # Check if CSV exists (either pre-existing or just generated)
    if not os.path.exists(csv_file):
        print(f"Neither CSV ({csv_file}) nor Excel ({excel_file}) files found")
        return []
    
    try:
        # Read the CSV file
        df = pd.read_csv(csv_file)
        strategies = df['Strategy Name'].unique().tolist()
        
        print("\n===== CONFIGURATION FILE INFORMATION =====")
        print(f"File: {csv_file}")
        print(f"Found {len(strategies)} strategies:")
        
        for i, strategy in enumerate(strategies, 1):
            strategy_df = df[df['Strategy Name'] == strategy]
            model = strategy_df['Model'].iloc[0]
            style = strategy_df['Style'].iloc[0]
            strategy_type = strategy_df['Type'].iloc[0]
            print(f"  {i}. {strategy} - Model: {model}, Style: {style}, Type: {strategy_type}")
        
        print("=" * 40)
        return strategies
        
    except Exception as e:
        print(f"Error reading configuration file: {e}")
        return []

def check_existing_results() -> None:
    """
    Check if backtest_result directory exists and ask user if they want to archive it.
    This helps avoid confusion with old results and ensures a clean testing environment.
    """
    if os.path.exists("backtest_result"):
        print("\n===== WARNING =====")
        print("Existing backtest results found in 'backtest_result' directory.")
        print("Keeping these may cause confusion when analyzing new results.")
        choice = input("Would you like to archive existing results before proceeding? (y/n): ")
        
        if choice.lower() in ("y", "yes"):
            try:
                # Create a timestamped archive
                timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                archive_dir = f"backtest_result_{timestamp}"
                
                # Copy the entire directory
                shutil.copytree("backtest_result", archive_dir)
                print(f"Archived existing results to '{archive_dir}'")
                
                # Remove the original
                shutil.rmtree("backtest_result")
                print("Removed original backtest_result directory.")
            except Exception as e:
                print(f"Error archiving directory: {str(e)}")
                print("Please manually archive or delete the directory if needed.")
        else:
            print("Keeping existing results. New results will be merged with existing ones.")
    # If directory doesn't exist, no action needed

def main():
    """Main function to run the autotest script"""
    import argparse
    import sys
    from datetime import datetime, timedelta
    
    # Track start time
    start_time = time.time()
    
    try:
        # Get the backtesting module
        backtesting = import_backtesting()
    except Exception as e:
        # The import_backtesting function now handles all exceptions internally
        # and provides detailed error messages, so we don't need to do anything here
        return
    
    parser = argparse.ArgumentParser(description='Automated backtest system')
    parser.add_argument('--config', type=str, default='fund/fund_bybit.csv',
                        help='Path to the configuration CSV file')
    parser.add_argument('--strategy', type=str, help='Strategy name to run (omit to run all strategies)')
    parser.add_argument('--mode', type=str, default=None,
                        choices=['optimize', 'walkforward', 'backtest', 'full', 'all'],
                        help='Mode to run (full runs all three modes for one strategy, all runs all modes for all strategies)')
    parser.add_argument('--window', type=int, help='Window size for backtest mode (overrides CSV value)')
    parser.add_argument('--threshold', type=float, help='Threshold for backtest mode (overrides CSV value)')
    parser.add_argument('--train_ratio', type=float, default=0.7,
                        help='Ratio of training data (default: 0.7)')
    parser.add_argument('--list', action='store_true', 
                        help='List all available strategies in the config file')
    parser.add_argument('--since', type=str, help='Start date in YYYY-MM-DD format')
    parser.add_argument('--until', type=str, help='End date in YYYY-MM-DD format (default: today)')
    parser.add_argument('--parallel', action='store_true',
                        help='Run strategies in parallel using multiprocessing')
    parser.add_argument('--max_workers', type=int, default=None,
                        help='Maximum number of worker processes (default: number of CPU cores)')
    parser.add_argument('--delay', type=float, default=2.0,
                        help='Delay in seconds between task submissions to avoid API rate limits (default: 2.0)')
    parser.add_argument('--menu', action='store_true',
                        help='Use interactive menu interface')
    parser.add_argument('--source', type=str, choices=['cex', 'glassnode', 'all'], default='all',
                        help='Data source to use: cex, glassnode, or all (default: all)')
    
    args = parser.parse_args()
    
    # Check if script is run without arguments (only the script name in sys.argv) or with menu flag
    if len(sys.argv) == 1 or args.menu:
        # Set to menu mode
        use_menu = True
    else:
        use_menu = False
        # Default behavior for no arguments: run all strategies with all modes in parallel
        if len(sys.argv) == 1:
            args.mode = 'all'
            args.parallel = True
            print("Running in default mode: all strategies with all modes in parallel")
    
    # Resolve the path to the CSV file
    # Use os.path.join with the current directory to ensure paths are correct
    if not os.path.isabs(args.config):
        csv_file = os.path.join(os.getcwd(), args.config)
    else:
        csv_file = args.config
    
    # Always try to generate from Excel first if it exists
    excel_file = csv_file.replace('.csv', '.xlsx')
    if os.path.exists(excel_file):
        print(f"Regenerating {csv_file} from {excel_file}")
        if not generate_csv_from_excel(excel_file, csv_file):
            print(f"Failed to generate CSV from Excel: {excel_file}")
    
    # If CSV still doesn't exist, try with script_dir path as fallback
    if not os.path.exists(csv_file):
        script_dir = os.path.dirname(os.path.abspath(__file__))
        alt_csv_file = os.path.join(script_dir, args.config)
        alt_excel_file = alt_csv_file.replace('.csv', '.xlsx')
        
        if os.path.exists(alt_excel_file):
            print(f"Trying alternative path. Regenerating {alt_csv_file} from {alt_excel_file}")
            if generate_csv_from_excel(alt_excel_file, alt_csv_file):
                csv_file = alt_csv_file  # Use this path if successful
            else:
                print(f"Failed to generate CSV from Excel: {alt_excel_file}")
        
        if not os.path.exists(csv_file) and os.path.exists(alt_csv_file):
            csv_file = alt_csv_file
    
    # Final check if CSV exists
    if not os.path.exists(csv_file):
        print(f"Error: Configuration file not found: {csv_file}")
        print(f"Please make sure the Excel file {excel_file} exists and is readable.")
        return
    
    # Read the CSV file to get strategy information
    df = pd.read_csv(csv_file)
    
    # Group strategies by data source
    cex_strategies = []
    glassnode_strategies = []
    
    # Identify which strategies use which data source
    for _, row in df.iterrows():
        strategy_name = row['Strategy Name']
        api_url = row['API']
        
        # Skip duplicates
        if strategy_name in cex_strategies or strategy_name in glassnode_strategies:
            continue
            
        if str(api_url).lower() in ['cex', 'bybit', 'bybit.com', 'exchange', 'cex_data']:
            cex_strategies.append(strategy_name)
        else:
            glassnode_strategies.append(strategy_name)
    
    # Apply data source filter if specified
    all_strategies = cex_strategies + glassnode_strategies
    filtered_strategies = []
    
    if args.source == 'cex':
        filtered_strategies = cex_strategies
        print(f"Filtered to only CEX strategies: {len(filtered_strategies)} found")
    elif args.source == 'glassnode':
        filtered_strategies = glassnode_strategies
        print(f"Filtered to only Glassnode strategies: {len(filtered_strategies)} found")
    else:
        filtered_strategies = all_strategies
        print(f"Using all strategies: {len(cex_strategies)} CEX and {len(glassnode_strategies)} Glassnode")
    
    # Variable to keep track of whether user selected 'all' for strategies
    strategy_choice = None
    
    # Ask about multiprocessing at the beginning for menu mode
    if use_menu:
        # Check for existing results before proceeding
        check_existing_results()
        
        # Display file information and available strategies
        strategies = display_file_info(csv_file)
        if not strategies:
            print("No strategies found. Exiting.")
            return
        
        # Show data source information
        print("\n===== DATA SOURCE INFORMATION =====")
        print(f"CEX Strategies ({len(cex_strategies)}): {', '.join(cex_strategies) if cex_strategies else 'None'}")
        print(f"Glassnode Strategies ({len(glassnode_strategies)}): {', '.join(glassnode_strategies) if glassnode_strategies else 'None'}")
        
        # Force parallel processing with all available CPU cores
        args.parallel = True
        args.max_workers = multiprocessing.cpu_count()
        print(f"\nParallel processing enabled with {args.max_workers} CPU cores for maximum performance.")
        
        # Automatically select all strategies instead of asking user
        args.source = 'all'
        filtered_strategies = all_strategies
        print(f"\nUsing all available strategies: {len(filtered_strategies)} total ({len(cex_strategies)} CEX + {len(glassnode_strategies)} Glassnode)")
        
        # Show menu and get user selection
        selected_mode = display_menu()
        
        if selected_mode == "exit":
            print("Exiting program.")
            return
        
        if selected_mode == "list":
            # Display the menu again without duplicating the file info display
            # that was already shown at the beginning
            print("\nReturning to menu...")
            return main()
            
        # Set mode based on user selection
        args.mode = selected_mode
        
        # For modes other than 'all', ask which strategy to run
        if selected_mode != "all":
            print("\nSelect a strategy to run:")
            for i, strategy in enumerate(filtered_strategies, 1):
                # Indicate data source type
                source_type = "CEX" if strategy in cex_strategies else "Glassnode"
                print(f"{i}. {strategy} ({source_type})")
            
            strategy_choice = input(f"\nEnter strategy number (1-{len(filtered_strategies)}) or 'all' for all strategies: ")
            
            if strategy_choice.lower() == 'all':
                # Run all strategies with the selected mode
                args.strategy = None
                # args.mode remains as selected_mode (optimize, walkforward, or backtest)
            else:
                try:
                    strategy_idx = int(strategy_choice) - 1
                    if 0 <= strategy_idx < len(filtered_strategies):
                        args.strategy = filtered_strategies[strategy_idx]
                    else:
                        print(f"Invalid selection. Using the first strategy: {filtered_strategies[0]}")
                        args.strategy = filtered_strategies[0]
                except ValueError:
                    print(f"Invalid selection. Using the first strategy: {filtered_strategies[0]}")
                    args.strategy = filtered_strategies[0]
            
            # For backtest mode, ask for window and threshold if not in CSV
            if selected_mode == "backtest":
                # Check if window/threshold are in CSV for this strategy
                config = load_config_from_csv(csv_file, args.strategy)
                
                if 'window' not in config:
                    window_input = input("\nEnter window size (or press Enter for default 60): ")
                    if window_input.strip():
                        try:
                            args.window = int(window_input)
                        except ValueError:
                            print("Invalid window value. Using default 60.")
                            args.window = 60
                    else:
                        args.window = 60
                
                if 'threshold' not in config:
                    threshold_input = input("\nEnter threshold value (or press Enter for default 1.0): ")
                    if threshold_input.strip():
                        try:
                            args.threshold = float(threshold_input)
                        except ValueError:
                            print("Invalid threshold value. Using default 1.0.")
                            args.threshold = 1.0
                    else:
                        args.threshold = 1.0
    
    # List available strategies if requested
    if args.list:
        display_available_strategies(csv_file)
        return
    
    # Set custom date range if provided
    if args.since:
        since_date = pd.to_datetime(args.since)
        backtesting.since = int(since_date.timestamp())
    
    if args.until:
        until_date = pd.to_datetime(args.until)
        backtesting.until = int(until_date.timestamp())
    else:
        backtesting.until = int(time.time())  # Default to current time
    
    # Apply strategy filter if a specific strategy was requested
    if args.strategy:
        # Check if the strategy exists and is in the filtered list
        if args.strategy not in filtered_strategies:
            if args.strategy in all_strategies:
                # Strategy exists but was filtered out by data source
                data_source = "CEX" if args.strategy in cex_strategies else "Glassnode"
                print(f"Warning: {args.strategy} is a {data_source} strategy but you selected {args.source} source.")
                print(f"Adding {args.strategy} to the list of strategies to run.")
                filtered_strategies.append(args.strategy)
            else:
                print(f"Error: Strategy '{args.strategy}' not found in the configuration file.")
                return
    
    print("\n===== STARTING BACKTESTING =====")
    print(f"Mode: {args.mode}")
    if args.strategy:
        # Determine data source for this strategy
        data_source = "CEX" if args.strategy in cex_strategies else "Glassnode"
        print(f"Strategy: {args.strategy} ({data_source})")
    else:
        print(f"Running {len(filtered_strategies)} strategies")
        print(f"Data Source: {args.source.upper()}")
        
    print(f"Parallel processing: {'Enabled with {0} cores'.format(args.max_workers if args.max_workers else multiprocessing.cpu_count()) if args.parallel else 'Disabled'}")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 40 + "\n")
    
    # Store results to display summary
    results = {}
    
    # Process the specific mode selected
    if args.mode == 'all':
        # Check if we need to run all strategies or just one strategy with all modes
        if strategy_choice and strategy_choice.lower() == 'all':
            # Run a single mode (optimize, walkforward, or backtest) for all strategies
            results = run_all_strategies(
                config_file=csv_file,  # Use the resolved csv_file path
                modes=[selected_mode],  # Just the selected mode
                parallel=args.parallel,
                max_workers=args.max_workers,
                delay_between_tasks=args.delay
            )
        else:
            # Run all strategies with all modes
            if args.strategy:
                # If a specific strategy is requested, filter the run to just that strategy
                # Get the strategy configuration to check data source
                try:
                    config = load_config_from_csv(csv_file, args.strategy)
                    is_cex = config['api_url'].lower() in ['cex', 'bybit', 'bybit.com', 'exchange', 'cex_data']
                    source_type = "CEX" if is_cex else "Glassnode"
                    print(f"Running all modes for {args.strategy} ({source_type})")
                    
                    # Create a unique run ID for this full run
                    full_run_id = f"full_{uuid.uuid4().hex[:8]}"
                    
                    # Track results for this strategy
                    results = {args.strategy: {}}
                    
                    # Run optimize
                    print("\n======= OPTIMIZE MODE =======")
                    dirs_to_cleanup = []
                    optimize_dirs, success = run_strategy_with_mode(
                        args.strategy, 
                        csv_file,  # Use the resolved csv_file path
                        'optimize', 
                        train_ratio=args.train_ratio,
                        run_id=f"{full_run_id}_optimize"
                    )
                    dirs_to_cleanup.extend(optimize_dirs)
                    results[args.strategy]['optimize'] = success
                    
                    # Run walkforward
                    print("\n======= WALKFORWARD MODE =======")
                    walkforward_dirs, success = run_strategy_with_mode(
                        args.strategy, 
                        csv_file,  # Use the resolved csv_file path
                        'walkforward', 
                        train_ratio=args.train_ratio,
                        run_id=f"{full_run_id}_walkforward"
                    )
                    dirs_to_cleanup.extend(walkforward_dirs)
                    results[args.strategy]['walkforward'] = success
                    
                    # Run backtest
                    print("\n======= BACKTEST MODE =======")
                    backtest_dirs, success = run_strategy_with_mode(
                        args.strategy, 
                        csv_file,  # Use the resolved csv_file path
                        'backtest', 
                        window=args.window,
                        threshold=args.threshold,
                        train_ratio=args.train_ratio,
                        run_id=f"{full_run_id}_backtest"
                    )
                    dirs_to_cleanup.extend(backtest_dirs)
                    results[args.strategy]['backtest'] = success
                    
                    # Clean up temporary directories
                    cleanup_temp_directories(dirs_to_cleanup, config_file=csv_file)
                    
                except Exception as e:
                    print(f"Error running all modes for {args.strategy}: {e}")
                    import traceback
                    traceback.print_exc()
            else:
                # Apply data source filter if specified
                to_run = []
                if args.source == 'cex':
                    to_run = cex_strategies
                elif args.source == 'glassnode':
                    to_run = glassnode_strategies
                else:
                    to_run = filtered_strategies
                
                # Run all three modes for all filtered strategies
                results = run_all_strategies(
                    config_file=csv_file,  # Use the resolved csv_file path
                    modes=['optimize', 'walkforward', 'backtest'],
                    parallel=args.parallel,
                    max_workers=args.max_workers,
                    delay_between_tasks=args.delay
                )
    elif args.mode == 'full':
        # Run all three modes in sequence for one strategy
        if not args.strategy:
            # If no strategy specified, use the first one from filtered strategies
            if filtered_strategies:
                args.strategy = filtered_strategies[0]
                print(f"No strategy specified. Using the first one: {args.strategy}")
            else:
                print("Error: No strategies available to run.")
                return
        
        # Get the strategy configuration to check data source
        try:
            config = load_config_from_csv(csv_file, args.strategy)
            is_cex = config['api_url'].lower() in ['cex', 'bybit', 'bybit.com', 'exchange', 'cex_data']
            source_type = "CEX" if is_cex else "Glassnode"
            print(f"\n======= RUNNING FULL MODE FOR {args.strategy} ({source_type}) =======")
            
            # Track results for this strategy
            results = {args.strategy: {}}
            
            # Create a unique run ID for this full run
            full_run_id = f"full_{uuid.uuid4().hex[:8]}"
            
            # Run optimize
            print("\n======= OPTIMIZE MODE =======")
            dirs_to_cleanup = []
            optimize_dirs, success = run_strategy_with_mode(
                args.strategy, 
                csv_file,  # Use the resolved csv_file path
                'optimize', 
                train_ratio=args.train_ratio,
                run_id=f"{full_run_id}_optimize"
            )
            dirs_to_cleanup.extend(optimize_dirs)
            results[args.strategy]['optimize'] = success
            
            # Run walkforward
            print("\n======= WALKFORWARD MODE =======")
            walkforward_dirs, success = run_strategy_with_mode(
                args.strategy, 
                csv_file,  # Use the resolved csv_file path
                'walkforward', 
                train_ratio=args.train_ratio,
                run_id=f"{full_run_id}_walkforward"
            )
            dirs_to_cleanup.extend(walkforward_dirs)
            results[args.strategy]['walkforward'] = success
            
            # Run backtest
            print("\n======= BACKTEST MODE =======")
            backtest_dirs, success = run_strategy_with_mode(
                args.strategy, 
                csv_file,  # Use the resolved csv_file path
                'backtest', 
                window=args.window,
                threshold=args.threshold,
                train_ratio=args.train_ratio,
                run_id=f"{full_run_id}_backtest"
            )
            dirs_to_cleanup.extend(backtest_dirs)
            results[args.strategy]['backtest'] = success
            
            print("\n======= FULL MODE COMPLETED =======")
            
            # Clean up temporary directories
            cleanup_temp_directories(dirs_to_cleanup, config_file=csv_file)
        except Exception as e:
            print(f"Error running full mode for {args.strategy}: {e}")
            import traceback
            traceback.print_exc()
    else:
        # For single mode (optimize, walkforward, or backtest)
        if not args.strategy:
            # If no strategy specified, use the first one from filtered strategies
            if filtered_strategies:
                args.strategy = filtered_strategies[0]
                print(f"No strategy specified. Using the first one: {args.strategy}")
            else:
                print("Error: No strategies available to run.")
                return
        
        # If user selected a specific mode through the menu,
        # run just that mode for a single strategy or all strategies
        if use_menu and strategy_choice and strategy_choice.lower() == 'all':
            # User selected to run a single mode for all strategies
            print(f"\n======= RUNNING {args.mode.upper()} MODE FOR ALL STRATEGIES =======")
            results = run_all_strategies(
                config_file=csv_file,  # Use the resolved csv_file path
                modes=[args.mode],  # Just the selected mode
                parallel=args.parallel,
                max_workers=args.max_workers,
                delay_between_tasks=args.delay
            )
        else:
            # Get the strategy configuration to check data source
            try:
                config = load_config_from_csv(csv_file, args.strategy)
                is_cex = config['api_url'].lower() in ['cex', 'bybit', 'bybit.com', 'exchange', 'cex_data']
                source_type = "CEX" if is_cex else "Glassnode"
                print(f"\n======= RUNNING {args.mode.upper()} MODE FOR {args.strategy} ({source_type}) =======")
                
                # Create a unique run ID for this single run
                single_run_id = f"{args.mode}_{uuid.uuid4().hex[:8]}"
                
                dirs_to_cleanup, success = run_strategy_with_mode(
                    args.strategy,
                    csv_file,  # Use the resolved csv_file path
                    args.mode,
                    window=args.window,
                    threshold=args.threshold,
                    train_ratio=args.train_ratio,
                    run_id=single_run_id
                )
                
                # Store results
                results = {args.strategy: {args.mode: success}}
                
                # Clean up temporary directories
                cleanup_temp_directories(dirs_to_cleanup, config_file=csv_file)
            except Exception as e:
                print(f"Error running {args.mode} mode for {args.strategy}: {e}")
                import traceback
                traceback.print_exc()
    
    # Calculate execution time
    end_time = time.time()
    execution_time = end_time - start_time
    hours, remainder = divmod(execution_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print("\n===== BACKTESTING COMPLETED =====")
    print(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Total execution time: {int(hours)}h {int(minutes)}m {int(seconds)}s")
    print("=" * 40 + "\n")
    
    # Display results summary
    display_results_summary(results)
    
    # Exit the program
    sys.exit(0)

if __name__ == "__main__":
    main()