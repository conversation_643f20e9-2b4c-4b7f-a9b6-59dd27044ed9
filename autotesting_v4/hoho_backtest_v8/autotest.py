import os
import pandas as pd
import sys
import time
import requests
import shutil
import builtins
import multiprocessing
import uuid
import random
from typing import Dict, Any, Optional, List, Tuple, Callable
from datetime import datetime
from concurrent.futures import ProcessPoolExecutor
from functools import wraps

# Import backtesting as module level variable but don't access it directly yet
import importlib

# Use a delayed import approach
backtesting = None


def retry_with_exponential_backoff(
        max_retries: int = 10,
        initial_delay_seconds: float = 1.0,
        max_delay_seconds: float = 60.0,
        backoff_factor: float = 2.0,
        retryable_exceptions: Tuple = (
                requests.exceptions.RequestException,
                requests.exceptions.HTTPError,
                requests.exceptions.ConnectionError,
                requests.exceptions.Timeout,
                requests.exceptions.J<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
                ValueError,  # Handle empty response issues
        ),
):
    """
    Retry decorator with exponential backoff for handling API rate limits and temporary failures.

    Args:
        max_retries: Maximum number of retries before giving up
        initial_delay_seconds: Initial delay between retries in seconds
        max_delay_seconds: Maximum delay between retries in seconds
        backoff_factor: Factor by which the delay increases with each retry
        retryable_exceptions: Tuple of exceptions that trigger a retry

    Returns:
        A decorator that can be applied to functions making API calls
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            delay = initial_delay_seconds
            last_exception = None

            for retry_count in range(max_retries + 1):  # +1 for the initial attempt
                try:
                    if retry_count > 0:
                        print(f"Retry attempt {retry_count}/{max_retries} (delay: {delay:.2f}s)...")

                    # Call the function and get the result
                    result = func(*args, **kwargs)

                    # For API requests that return empty results (but don't raise exceptions)
                    # We'll treat empty results as a retryable condition
                    if result is None or (isinstance(result, (list, dict)) and len(result) == 0):
                        if retry_count < max_retries:
                            print(f"Empty result received. Retrying...")
                            time.sleep(delay)
                            delay = min(delay * backoff_factor, max_delay_seconds)
                            continue
                        else:
                            print(f"Empty result received after {max_retries} retries.")
                            return result

                    # If we got here, the request was successful
                    return result

                except requests.exceptions.HTTPError as e:
                    # Check for rate limiting (429) or server errors (5xx)
                    if hasattr(e, 'response') and e.response is not None:
                        status_code = e.response.status_code

                        # Handle rate limiting
                        if status_code == 429:
                            print(f"\n===== RATE LIMIT ERROR =====")
                            print(f"Glassnode API rate limit hit (429 Too Many Requests)")

                            # Try to get retry-after header, default to current delay if not present
                            retry_after = e.response.headers.get('Retry-After')
                            if retry_after and retry_after.isdigit():
                                delay = float(retry_after)
                                print(f"API suggests waiting {delay}s before retrying")

                            if retry_count < max_retries:
                                # Add jitter to avoid thundering herd problem
                                jitter = random.uniform(0, 0.1 * delay)
                                wait_time = delay + jitter
                                print(f"Waiting {wait_time:.2f}s before retry {retry_count + 1}/{max_retries}...")
                                time.sleep(wait_time)
                                # Increase delay for next time
                                delay = min(delay * backoff_factor, max_delay_seconds)
                                continue

                        # Handle server errors (always retry)
                        elif 500 <= status_code < 600:
                            print(f"Server error: HTTP {status_code}. Retrying...")
                            if retry_count < max_retries:
                                # Add jitter to avoid thundering herd problem
                                jitter = random.uniform(0, 0.1 * delay)
                                wait_time = delay + jitter
                                time.sleep(wait_time)
                                delay = min(delay * backoff_factor, max_delay_seconds)
                                continue

                        # Handle authentication errors (don't retry)
                        elif status_code == 401 or status_code == 403:
                            print(f"\n===== AUTHENTICATION ERROR =====")
                            print(f"API Key may be invalid (HTTP {status_code})")
                            print("This error indicates an authentication problem with your API key.")
                            print("Check that your API key is valid and properly set.")
                            # Don't retry authentication errors
                            raise e

                    last_exception = e
                    print(f"HTTP Error: {e}")
                    if retry_count < max_retries:
                        # Add jitter to avoid thundering herd problem
                        jitter = random.uniform(0, 0.1 * delay)
                        wait_time = delay + jitter
                        time.sleep(wait_time)
                        delay = min(delay * backoff_factor, max_delay_seconds)
                    else:
                        # Max retries reached
                        raise

                except requests.exceptions.JSONDecodeError as e:
                    last_exception = e

                    print(f"JSON decode error: {str(e)}")
                    if retry_count >= max_retries:
                        print(f"Maximum retry attempts ({max_retries}) reached.")
                        raise

                    # Add longer delay for JSON decode errors - these are often due to rate limits
                    delay = min(delay * backoff_factor * 1.5, max_delay_seconds)
                    jitter = random.uniform(0, 0.1 * delay)
                    wait_time = delay + jitter

                    print(f"Waiting {wait_time:.2f}s before retry {retry_count + 1}/{max_retries}...")
                    time.sleep(wait_time)

                except retryable_exceptions as e:
                    last_exception = e

                    # Check if we should retry
                    if retry_count >= max_retries:
                        print(f"Maximum retry attempts ({max_retries}) reached.")
                        raise

                    # Add jitter to avoid thundering herd problem
                    jitter = random.uniform(0, 0.1 * delay)
                    wait_time = delay + jitter

                    print(f"Error during API call: {str(e)}")
                    print(f"Waiting {wait_time:.2f}s before retry {retry_count + 1}/{max_retries}...")
                    time.sleep(wait_time)

                    # Increase delay for next time with exponential backoff
                    delay = min(delay * backoff_factor, max_delay_seconds)

                except Exception as e:
                    # Non-retryable exception, raise immediately
                    print(f"Non-retryable error: {str(e)}")
                    raise

            # If we exit the loop due to max retries, raise the last exception
            if last_exception:
                raise last_exception

        return wrapper

    return decorator


def import_backtesting():
    """
    Import the backtesting module safely.
    This prevents the execution of code in the module level when importing.
    Handle common errors during import and provide user-friendly error messages.
    """
    global backtesting
    if backtesting is None:
        try:
            backtesting = importlib.import_module('backtesting')
        except requests.exceptions.JSONDecodeError:
            print("\n===== API ERROR =====")
            print("Error: Failed to decode JSON response from the Glassnode API.")
            print("This usually happens when:")
            print("1. Your API key is invalid or has expired")
            print("2. The Glassnode API is experiencing downtime")
            print("3. Your network connection to Glassnode servers is unstable")
            print("\nWhat to do next:")
            print("- Check your API key in the backtesting.py file")
            print("- Verify your internet connection")
            print("- Wait a few minutes and try again")
            print("- Check Glassnode status page for any reported outages")
            sys.exit(1)
        except ImportError as e:
            print(f"\n===== IMPORT ERROR =====")
            print(f"Error: Could not import the backtesting module: {str(e)}")
            print("Make sure the backtesting.py file exists in the same directory as autotest.py")
            sys.exit(1)
        except Exception as e:
            print(f"\n===== UNEXPECTED ERROR =====")
            print(f"Error initializing backtesting module: {str(e)}")
            print("\nTraceback:")
            import traceback
            traceback.print_exc()
            print("\nWhat to do next:")
            print("- Try running again in a few minutes")
            print("- Check your API credentials")
            print("- Verify all required Python packages are installed")
            sys.exit(1)
    return backtesting


@retry_with_exponential_backoff()
def make_glassnode_api_request(url, params, timeout=30):
    """
    Make a request to the Glassnode API with retry logic.

    Args:
        url: The API endpoint URL
        params: Dictionary of query parameters
        timeout: Request timeout in seconds

    Returns:
        Parsed JSON response data
    """
    try:
        # Make sure we have an API key
        if 'api_key' not in params or not params['api_key']:
            raise ValueError("Missing API key. Check backtesting.py API_KEY setting.")

        # Ensure we have required parameters
        required_params = ['a', 's', 'u', 'i']
        for param in required_params:
            if param not in params:
                raise ValueError(f"Missing required parameter: {param}")

        # Print additional debug info
        api_symbol = params.get('a', 'unknown')
        since = params.get('s', 'unknown')
        until = params.get('u', 'unknown')
        resolution = params.get('i', 'unknown')
        print(f"API Request: {url.split('/')[-1]} - Symbol: {api_symbol}, "
              f"Range: {datetime.fromtimestamp(int(since)).strftime('%Y-%m-%d')} to "
              f"{datetime.fromtimestamp(int(until)).strftime('%Y-%m-%d')}, Resolution: {resolution}")

        # Make the request with additional error handling
        response = requests.get(url, params=params, timeout=timeout)

        # Raise an HTTPError for bad responses (4XX and 5XX)
        response.raise_for_status()

        # Check for empty response
        if not response.text or response.text.isspace():
            raise ValueError("API returned empty response body")

        # Parse JSON response
        try:
            data = response.json()
        except ValueError as e:
            # Log the actual response content to help debug
            print(f"JSON decode error. Response content (first 200 chars): {response.text[:200]}...")
            raise requests.exceptions.JSONDecodeError(
                f"Failed to parse JSON: {str(e)}", response.text, 0
            )

        # Check for empty data
        if not data:
            raise ValueError("API returned empty data array")

        # Check for specific error patterns in the response
        if isinstance(data, dict) and 'error' in data:
            error_message = data.get('error', 'Unknown API error')
            print(f"API returned error message: {error_message}")
            if 'rate limit' in error_message.lower() or 'too many requests' in error_message.lower():
                # Create a response-like object to raise as HTTPError with status code 429
                error_response = requests.Response()
                error_response.status_code = 429
                raise requests.exceptions.HTTPError(
                    f"Rate limit error: {error_message}",
                    response=error_response
                )
            # Raise as a generic exception
            raise ValueError(f"API error: {error_message}")

        # Log success with data count
        if isinstance(data, list):
            print(f"Received {len(data)} data points from API")

        return data

    except requests.exceptions.RequestException as e:
        # Add more context to the exception
        print(f"Request failed: {url}")
        print(f"Parameters: {', '.join([f'{k}={v}' for k, v in params.items() if k != 'api_key'])}")
        raise


def create_output_directory(strategy_name: str, mode: str = None, run_id: str = None) -> str:
    """
    Create output directory for the strategy results.

    Args:
        strategy_name: Name of the strategy
        mode: Mode of the backtest (optimize, walkforward, backtest)
        run_id: Unique identifier for this run to avoid directory conflicts

    Returns:
        Path to the output directory
    """
    # Create base directory - ensure it's relative to current working directory
    base_dir = os.path.join(os.getcwd(), "backtest_result")
    os.makedirs(base_dir, exist_ok=True)

    # Create strategy-specific directory
    strategy_dir = os.path.join(base_dir, strategy_name)
    os.makedirs(strategy_dir, exist_ok=True)

    # If run_id provided, create a unique subdirectory to avoid conflicts
    if run_id:
        # Add mode to the directory name if provided
        if mode:
            unique_dir = os.path.join(strategy_dir, f"{mode}_{run_id}")
        else:
            unique_dir = os.path.join(strategy_dir, run_id)
        os.makedirs(unique_dir, exist_ok=True)
        return unique_dir

    return strategy_dir


def redirect_path_to_strategy_dir(original_path: str, strategy_name: str, mode: str = None, run_id: str = None) -> str:
    """
    Redirect a file path to save in the strategy-specific directory.

    Args:
        original_path: Original file path
        strategy_name: Name of the strategy
        mode: Mode of the backtest (optimize, walkforward, backtest)
        run_id: Unique identifier for this run to avoid directory conflicts

    Returns:
        New path in the strategy directory
    """
    # Create base strategy directory - ensure it's relative to current working directory
    base_dir = os.path.join(os.getcwd(), "backtest_result")
    os.makedirs(base_dir, exist_ok=True)

    # Create strategy-specific directory
    strategy_dir = os.path.join(base_dir, strategy_name)
    os.makedirs(strategy_dir, exist_ok=True)

    # Extract the base filename
    basename = os.path.basename(original_path)

    # If run_id provided, create a unique subdirectory to avoid conflicts
    if run_id:
        # Add mode to the directory name if provided
        if mode:
            unique_dir = os.path.join(strategy_dir, f"{mode}_{run_id}")
        else:
            unique_dir = os.path.join(strategy_dir, run_id)
        os.makedirs(unique_dir, exist_ok=True)
        return os.path.join(unique_dir, basename)

    # Return the new path (directly in the strategy directory)
    return os.path.join(strategy_dir, basename)


def generate_csv_from_excel(excel_file: str, csv_file: str, sheet_name: str = "Strategies") -> bool:
    """
    Generate a CSV file from an Excel workbook's specified sheet.

    Args:
        excel_file: Path to the Excel file
        csv_file: Path to output CSV file
        sheet_name: Name of the worksheet to convert (default: "Strategies")

    Returns:
        True if successful, False otherwise
    """
    try:
        if not os.path.exists(excel_file):
            print(f"Excel file not found: {excel_file}")
            return False

        # Read the specified worksheet
        df = pd.read_excel(excel_file, sheet_name=sheet_name)

        # Create the directory for CSV if it doesn't exist
        os.makedirs(os.path.dirname(csv_file), exist_ok=True)

        # Save as CSV
        df.to_csv(csv_file, index=False)
        print(f"Generated {csv_file} from {excel_file}, sheet: {sheet_name}")
        return True
    except Exception as e:
        print(f"Error generating CSV from Excel: {str(e)}")
        return False


def load_config_from_csv(csv_file: str, strategy_name: str = None) -> Dict[str, Any]:
    """
    Load configuration from a CSV file.
    Always regenerate CSV from the corresponding Excel file when available.

    Args:
        csv_file: Path to the CSV configuration file
        strategy_name: Optional strategy name to filter by

    Returns:
        Dictionary containing configuration parameters
    """
    # Always try to generate from Excel first
    excel_file = csv_file.replace('.csv', '.xlsx')
    if os.path.exists(excel_file):
        print(f"Regenerating {csv_file} from {excel_file}")
        generate_csv_from_excel(excel_file, csv_file)

    # Check if CSV exists (either pre-existing or just generated)
    if not os.path.exists(csv_file):
        raise FileNotFoundError(f"Neither CSV ({csv_file}) nor Excel ({excel_file}) files found")

    df = pd.read_csv(csv_file)

    # If strategy_name is provided, filter by that strategy
    if strategy_name:
        df = df[df['Strategy Name'] == strategy_name]
        if len(df) == 0:
            raise ValueError(f"Strategy '{strategy_name}' not found in config file")

    # Get the first row (or the filtered row if strategy_name was provided)
    config_row = df.iloc[0]

    # Map CSV columns to backtesting variables based on the specified mapping
    config = {
        'api_url': config_row['API'],
        'metric_key': config_row['Metric Key'],
        'resolution': config_row['Resolution'],
        'api_symbol': config_row['API Symbol'],
        'underlying': config_row['Asset'],
        'strategy_name': config_row['Strategy Name'],  # Keep this for directory naming
        'model': config_row['Model'],  # This is the actual strategy model to use
        'style': config_row['Style'],
        'strategy_type': config_row['Type'],
    }

    # Add window and threshold if they exist in the CSV (for backtest mode)
    if 'x' in config_row:
        try:
            # Try to convert to numeric value, handling potential spaces
            config['window'] = int(float(str(config_row['x']).strip()))
        except (ValueError, TypeError):
            print(f"Warning: Could not convert window value '{config_row['x']}' to integer. Using default.")

    if 'y' in config_row:
        try:
            # Try to convert to numeric value, handling potential spaces
            config['threshold'] = float(str(config_row['y']).strip())
        except (ValueError, TypeError):
            print(f"Warning: Could not convert threshold value '{config_row['y']}' to float. Using default.")

    # Add ratio if it exists
    if 'Ratio' in config_row:
        config['ratio'] = config_row['Ratio']

    return config


def monkey_patch_save_figure(run_id=None, mode=None):
    """
    Monkey patch the backtesting module's figure saving functionality
    to save directly to strategy-specific directories

    Args:
        run_id: Unique identifier for this run to avoid directory conflicts
        mode: Mode of the backtest (optimize, walkforward, backtest)
    """
    # Get the backtesting module
    backtesting = import_backtesting()

    # Save the original method
    original_savefig = backtesting.plt.savefig

    # Define our new method that redirects the output path
    def custom_savefig(filename, *args, **kwargs):
        # If we have a strategy name, redirect the path
        if hasattr(backtesting, 'current_strategy_name'):
            strategy_name = backtesting.current_strategy_name

            # Create output path in the strategy directory - ensure it's relative to current working directory
            base_dir = os.path.join(os.getcwd(), "backtest_result")
            os.makedirs(base_dir, exist_ok=True)

            # Create strategy-specific directory
            strategy_dir = os.path.join(base_dir, strategy_name)
            os.makedirs(strategy_dir, exist_ok=True)

            # If run_id is provided, create a unique subdirectory
            if run_id:
                # Add mode to the directory name if provided
                if mode:
                    unique_dir = os.path.join(strategy_dir, f"{mode}_{run_id}")
                else:
                    unique_dir = os.path.join(strategy_dir, run_id)
                os.makedirs(unique_dir, exist_ok=True)
                strategy_dir = unique_dir

            # Get the basename of the file
            basename = os.path.basename(filename)

            # If the basename includes model name instead of strategy name,
            # replace it with the strategy name or modify as needed
            if hasattr(backtesting, 'current_model'):
                model_name = backtesting.current_model
                # If the filename starts with the model name, replace it with strategy name
                if basename.startswith(model_name):
                    basename = basename.replace(model_name, strategy_name, 1)

            # Create the new filename
            new_filename = os.path.join(strategy_dir, basename)

            # Print message showing we're redirecting
            if os.path.dirname(filename) != os.path.dirname(new_filename):
                print(f"Redirecting output: {filename} → {new_filename}")

            # Save to the new location
            original_savefig(new_filename, *args, **kwargs)
        else:
            # No strategy name, use original behavior
            original_savefig(filename, *args, **kwargs)

    # Replace the method
    backtesting.plt.savefig = custom_savefig

    # Store the original method for cleanup later
    backtesting._original_savefig = original_savefig


def monkey_patch_print(run_id=None, mode=None):
    """
    Monkey patch the built-in print function to intercept and modify file saving messages

    Args:
        run_id: Unique identifier for this run to avoid directory conflicts
        mode: Mode of the backtest (optimize, walkforward, backtest)
    """
    # Get the backtesting module
    backtesting = import_backtesting()

    original_print = builtins.print

    def custom_print(*args, **kwargs):
        # Check if this is a file saving message
        if len(args) > 0 and isinstance(args[0], str):
            original_path = None

            # Handle regular heatmap messages
            if args[0].startswith("儲存熱力圖為 "):
                original_path = args[0].replace("儲存熱力圖為 ", "").strip()
                message_prefix = "儲存熱力圖為 "
            # Handle walkforward heatmap messages
            elif args[0].startswith("儲存 Walk Forward 熱力圖為 "):
                original_path = args[0].replace("儲存 Walk Forward 熱力圖為 ", "").strip()
                message_prefix = "儲存 Walk Forward 熱力圖為 "
            # Handle backtest equity curve messages
            elif args[0].startswith("已保存圖表到："):
                original_path = args[0].replace("已保存圖表到：", "").strip()
                message_prefix = "已保存圖表到："

            # If we found a path and have a strategy name, redirect
            if original_path and hasattr(backtesting, 'current_strategy_name'):
                strategy_name = backtesting.current_strategy_name

                # Create base strategy directory - ensure it's relative to current working directory
                base_dir = os.path.join(os.getcwd(), "backtest_result")
                os.makedirs(base_dir, exist_ok=True)

                # Create strategy-specific directory
                strategy_dir = os.path.join(base_dir, strategy_name)
                os.makedirs(strategy_dir, exist_ok=True)

                # If run_id is provided, create a unique subdirectory
                if run_id:
                    # Add mode to the directory name if provided
                    if mode:
                        unique_dir = os.path.join(strategy_dir, f"{mode}_{run_id}")
                    else:
                        unique_dir = os.path.join(strategy_dir, run_id)
                    os.makedirs(unique_dir, exist_ok=True)
                    strategy_dir = unique_dir

                # For equity curve charts, we need to handle entire paths differently
                if args[0].startswith("已保存圖表到："):
                    # Get just the filename without any subdirectories
                    basename = os.path.basename(original_path)
                    # If the basename includes model name, replace it with strategy name
                    if hasattr(backtesting, 'current_model'):
                        model_name = backtesting.current_model
                        if basename.startswith(model_name):
                            # Replace model name with strategy name
                            basename = basename.replace(model_name, strategy_name, 1)
                    new_path = os.path.join(strategy_dir, basename)
                else:
                    # For heatmaps, keep the existing behavior
                    basename = os.path.basename(original_path)
                    new_path = os.path.join(strategy_dir, basename)

                # Replace the message
                args = (f"{message_prefix}{new_path}",) + args[1:]

        # Call original print with possibly modified args
        return original_print(*args, **kwargs)

    # Replace built-in print
    builtins.print = custom_print

    # Store original for cleanup
    global _original_print
    _original_print = original_print


def restore_monkey_patches():
    """Restore all monkey-patched functions"""
    # Get the backtesting module
    backtesting = import_backtesting()

    # Restore savefig
    if hasattr(backtesting, '_original_savefig'):
        backtesting.plt.savefig = backtesting._original_savefig
        delattr(backtesting, '_original_savefig')

    # Restore print
    if '_original_print' in globals():
        builtins.print = globals()['_original_print']


def run_backtest(config: Dict[str, Any], mode: str = 'optimize', window: Optional[int] = None,
                 threshold: Optional[float] = None, train_ratio: float = 0.7, run_id: Optional[str] = None) -> List[
    str]:
    """
    Run a backtest with the specified configuration.

    Args:
        config: Dictionary containing configuration parameters
        mode: Mode to run ('optimize', 'walkforward', or 'backtest')
        window: Window size for backtest mode (optional if provided in config)
        threshold: Threshold for backtest mode (optional if provided in config)
        train_ratio: Ratio of training data (default: 0.7)
        run_id: Unique identifier for this run to avoid directory conflicts

    Returns:
        List of temporary directory paths that should be cleaned up
    """
    # Required for monkey-patching print
    import builtins
    import shutil

    # Get the backtesting module
    backtesting = import_backtesting()

    # List to collect directories that should be cleaned up later
    dirs_to_cleanup = []

    try:
        # Set up parameters in backtesting module
        backtesting.api_url = config['api_url']
        backtesting.metric_key = config['metric_key']
        backtesting.resolution = config['resolution']
        backtesting.api_symbol = config['api_symbol']
        backtesting.underlying = config['underlying']

        # IMPORTANT: Use the strategy name from our config, not the model name
        # This ensures files are saved to the strategy directory (e.g., "Cody4")
        strategy_name = config['strategy_name']
        backtesting.current_strategy_name = strategy_name

        # Set the model but don't use it for file paths
        model_name = config['model']
        backtesting.current_model = model_name
        backtesting.current_strategy_style = config['style'].lower()
        backtesting.current_strategy_type = config['strategy_type'].lower()

        # Extract factor name from API URL using the improved function
        factor_name = extract_factor_name_from_api_url(backtesting.api_url)
        # Also set it in the backtesting module for use in heatmap titles
        backtesting.factor_name = factor_name
        original_factor_name = factor_name.replace(' ', '_')  # Store for cleanup later

        # For backtest mode, store window and threshold values
        if mode == 'backtest':
            backtest_window = config.get('window', window)
            backtest_threshold = config.get('threshold', threshold)
            if backtest_window is not None:
                backtesting.current_window = backtest_window
            if backtest_threshold is not None:
                backtesting.current_threshold = backtest_threshold

        # Create strategy-specific output directory with run_id if provided
        output_dir = create_output_directory(strategy_name, mode, run_id)
        print(f"Results will be saved to: {output_dir}")

        # Monkey patch figure saving and print function with run_id
        monkey_patch_save_figure(run_id=run_id, mode=mode)
        monkey_patch_print(run_id=run_id, mode=mode)

        # Save original SAVE_FIGURES setting
        original_save_figures = backtesting.SAVE_FIGURES

        # Force saving figures
        backtesting.SAVE_FIGURES = True

        # Also patch the ensure_output_directory function if it exists
        if hasattr(backtesting, 'ensure_output_directory'):
            original_ensure_output = backtesting.ensure_output_directory

            def custom_ensure_output(*args, **kwargs):
                # Just return our strategy directory
                return output_dir

            backtesting.ensure_output_directory = custom_ensure_output

        # Store original plot_backtest_results function for backtest mode
        if mode == 'backtest':
            original_plot_backtest = backtesting.plot_backtest_results

            # Create a custom plot_backtest_results function that saves files to the strategy directory
            def custom_plot_backtest(strategy_name, window, threshold, strategy_type, style, train_ratio=0.7):
                """
                Enhanced version of plot_backtest_results that ensures files are saved to the correct directory
                """
                # Extract the factor name from the API URL using the improved function
                backtesting.factor_name = extract_factor_name_from_api_url(backtesting.api_url)

                # Save the original SAVE_FIGURES setting
                original_save_figures = backtesting.SAVE_FIGURES
                backtesting.SAVE_FIGURES = True

                # Set the filename format to include strategy name
                original_basename = None
                if hasattr(backtesting, 'current_strategy_name'):
                    # Store the current value
                    original_strategy_name = backtesting.current_strategy_name
                    # Set it to our strategy name
                    backtesting.current_strategy_name = config['strategy_name']

                try:
                    # Call the original function
                    result = original_plot_backtest(strategy_name, window, threshold, strategy_type, style, train_ratio)
                    return result
                finally:
                    # Restore original settings
                    backtesting.SAVE_FIGURES = original_save_figures
                    if original_basename:
                        backtesting.current_basename = original_basename
                    if hasattr(backtesting, 'current_strategy_name') and 'original_strategy_name' in locals():
                        backtesting.current_strategy_name = original_strategy_name

            # Replace the function
            backtesting.plot_backtest_results = custom_plot_backtest

        # Prepare data
        since = backtesting.since
        until = backtesting.until

        # Get factor data
        try:
            print(f"Requesting factor data from Glassnode API: {backtesting.api_url}")
            data = make_glassnode_api_request(
                backtesting.api_url,
                params={"a": backtesting.api_symbol, "s": since,
                        "u": until, "api_key": backtesting.API_KEY,
                        "i": backtesting.resolution},
                timeout=30
            )

            if not data:
                print(f"\n===== EMPTY API RESPONSE =====")
                print(f"Glassnode API returned empty data.")
                print(f"URL: {backtesting.api_url}")
                print("\nWhat to do next:")
                print("- Check if the selected date range contains data")
                print("- Verify the API symbol and parameters are correct")
                print("- Try a different time range or metric")
                raise Exception("Empty data returned from API")

            df_value = backtesting.process_glassnode_data(data, metric_key=backtesting.metric_key)
            print(f"Successfully retrieved factor data: {len(df_value)} records")

        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 429:
                # This should now be handled by the retry decorator
                print(f"\n===== RATE LIMIT ERROR =====")
                print(f"Still hitting rate limits after maximum retries.")
                print("\nWhat to do next:")
                print("- Wait longer before running again (at least 5-10 minutes)")
                print("- Reduce the number of parallel requests")
                print("- Check your API plan limits")
                raise Exception(f"Rate limit exceeded even with retries: {str(e)}")
            else:
                print(f"\n===== API ERROR =====")
                print(f"Glassnode API returned error: {str(e)}")
                print(f"URL: {backtesting.api_url}")
                print("\nWhat to do next:")
                print("- Check if your API key is valid")
                print("- Verify the API URL is correct")
                print("- Wait a few minutes and try again")
                raise Exception(f"API request failed: {str(e)}")

        except (requests.exceptions.JSONDecodeError, ValueError) as e:
            print(f"\n===== API RESPONSE ERROR =====")
            print(f"Failed to decode or process API response: {str(e)}")
            print(f"URL: {backtesting.api_url}")
            print("\nWhat to do next:")
            print("- Verify your API key is still valid")
            print("- Check if the data format from Glassnode has changed")
            print("- Try a different time range or metric")
            raise Exception(f"Failed to process API response: {str(e)}")

        except requests.exceptions.RequestException as e:
            print(f"\n===== API CONNECTION ERROR =====")
            print(f"Failed to connect to Glassnode API after multiple retries: {str(e)}")
            print("\nWhat to do next:")
            print("- Check your internet connection")
            print("- Verify the API endpoint is correct")
            print("- Try again later when the API service may be more responsive")
            raise Exception(f"API connection error after retries: {str(e)}")

        # Get underlying price data
        try:
            print(f"Requesting price data for {backtesting.underlying} from Glassnode API")
            data = make_glassnode_api_request(
                "https://api.glassnode.com/v1/metrics/market/price_usd_close",
                params={"a": backtesting.underlying, "s": since,
                        "u": until, "api_key": backtesting.API_KEY,
                        "i": backtesting.resolution},
                timeout=30
            )

            if not data:
                print(f"\n===== EMPTY PRICE API RESPONSE =====")
                print(f"Glassnode price API returned empty data for {backtesting.underlying}.")
                print("\nWhat to do next:")
                print("- Check if the asset symbol is correct")
                print("- Verify the selected date range contains data")
                print("- Try a different time range")
                raise Exception(f"Empty price data returned for {backtesting.underlying}")

            df_price = backtesting.process_glassnode_data(data)
            print(f"Successfully retrieved price data: {len(df_price)} records")

        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 429:
                # This should now be handled by the retry decorator
                print(f"\n===== RATE LIMIT ERROR =====")
                print(f"Still hitting rate limits after maximum retries.")
                print("\nWhat to do next:")
                print("- Wait longer before running again (at least 5-10 minutes)")
                print("- Reduce the number of parallel requests")
                print("- Check your API plan limits")
                raise Exception(f"Rate limit exceeded even with retries: {str(e)}")
            else:
                print(f"\n===== PRICE API ERROR =====")
                print(f"Glassnode price API returned error: {str(e)}")
                print("\nWhat to do next:")
                print("- Check if your API key is valid")
                print("- Verify the asset symbol is supported")
                print("- Wait a few minutes and try again")
                raise Exception(f"Price API request failed: {str(e)}")

        except (requests.exceptions.JSONDecodeError, ValueError) as e:
            print(f"\n===== PRICE API RESPONSE ERROR =====")
            print(f"Failed to decode or process price API response: {str(e)}")
            print("\nWhat to do next:")
            print("- Verify your API key is still valid")
            print("- Check if the asset symbol is correct")
            print("- Try a different time range")
            raise Exception(f"Failed to process price API response: {str(e)}")

        except requests.exceptions.RequestException as e:
            print(f"\n===== PRICE API CONNECTION ERROR =====")
            print(f"Failed to connect to Glassnode price API after multiple retries: {str(e)}")
            print("\nWhat to do next:")
            print("- Check your internet connection")
            print("- Verify the API endpoint is correct")
            print("- Try again later when the API service may be more responsive")
            raise Exception(f"Price API connection error after retries: {str(e)}")

        # Process data
        backtesting.df = backtesting.prepare_data(df_value, df_price)
        backtesting.time_variable = backtesting.get_time_variable(backtesting.resolution)

        # Set strategy parameters based on style
        style = config['style'].lower()
        strategy_type = config['strategy_type'].lower()

        # Set shift periods based on resolution
        backtesting.SHIFT_PERIODS = backtesting.get_shift_periods(backtesting.resolution)

        print("\n===== Backtest Configuration =====")
        print(f"Strategy Name: {config['strategy_name']}")
        print(f"Asset: {config['underlying']}")
        print(f"Factor: {factor_name}")
        print(f"Metric Key: {backtesting.metric_key}")
        print(f"Resolution: {backtesting.resolution}")
        print(f"Model: {config['model']}")
        print(f"Style: {style}")
        print(f"Type: {strategy_type}")

        # Print time range
        start_date = pd.to_datetime(since, unit='s').strftime('%Y-%m-%d')
        end_date = pd.to_datetime(until, unit='s').strftime('%Y-%m-%d')
        print(f"Time Range: {start_date} to {end_date}")
        print(f"Training Ratio: {train_ratio * 100:.0f}%")
        print("=" * 40)

        # IMPORTANT: Explicitly set the factor name right before running strategy
        # This ensures the correct factor name is used in heatmap titles
        backtesting.factor_name = extract_factor_name_from_api_url(backtesting.api_url)
        print(f"Using factor name: {backtesting.factor_name}")

        if mode == 'optimize':
            print(f"\n=== Running optimization for {config['strategy_name']} ===")
            backtesting.run_strategy(mode='optimize')

        elif mode == 'walkforward':
            print(f"\n=== Running walk-forward analysis for {config['strategy_name']} ===")
            backtesting.run_strategy(mode='walkforward', train_ratio=train_ratio)

        elif mode == 'backtest':
            # For backtest mode, use window/threshold from config if available, otherwise use passed values
            backtest_window = config.get('window', window)
            backtest_threshold = config.get('threshold', threshold)

            if backtest_window is None or backtest_threshold is None:
                raise ValueError(
                    "Window and threshold must be provided either in the config or as arguments for backtest mode")

            print(f"\n=== Running backtest for {config['strategy_name']} ===")
            print(f"Window: {backtest_window}, Threshold: {backtest_threshold}")

            # Find the strategy model name from config
            model_name = config['model']

            # Check if the model exists in base_params
            if model_name not in backtesting.base_params:
                closest_match = find_closest_model(model_name, list(backtesting.base_params.keys()))
                print(f"Model '{model_name}' not found. Using closest match: '{closest_match}'")
                model_name = closest_match

            # Before running the backtest, save current time to identify new files
            before_time = time.time()

            backtesting.run_strategy(
                mode='backtest',
                strategy_name=model_name,
                window=backtest_window,
                threshold=backtest_threshold,
                strategy_type=strategy_type,
                style=style,
                train_ratio=train_ratio
            )

            # After running backtest, find and copy all newly created files to our output directory
            # Create a list of possible directories where backtest results might be saved
            search_directories = [
                # Check model directories (with and without spaces)
                os.path.join("backtest_result", model_name.replace(' ', '_')),
                os.path.join("backtest_result", model_name),
                # Check factor directories
                os.path.join("backtest_result", original_factor_name),
                os.path.join("backtest_result", factor_name),
                # Check strategy directories
                os.path.join("backtest_result", strategy_name),
                # Check all subdirectories of backtest_result (for any other places)
                "backtest_result"
            ]

            # Track if we found and copied any files
            found_any_files = False

            # First, process direct directories we know about
            for search_dir in search_directories:
                if os.path.exists(search_dir) and os.path.isdir(search_dir):
                    # If this is the base backtest_result directory, we need to search subdirectories
                    if search_dir == "backtest_result":
                        # Look through all subdirectories
                        for dir_name in os.listdir(search_dir):
                            subdir_path = os.path.join(search_dir, dir_name)
                            if os.path.isdir(subdir_path) and dir_name != strategy_name:
                                if subdir_path.endswith(strategy_name):
                                    # Skip directories with the strategy name to avoid duplicates
                                    continue
                                # Process files in this subdirectory
                                for filename in os.listdir(subdir_path):
                                    src = os.path.join(subdir_path, filename)
                                    # Check if this is a PNG file created after we started the backtest
                                    if (os.path.isfile(src) and
                                            filename.endswith('.png') and
                                            os.path.getmtime(src) >= before_time):

                                        # FIX: Only copy files that match the current model name
                                        # This prevents copying files from other strategies/models
                                        if model_name in filename:
                                            # Copy the file to our output directory
                                            dst = os.path.join(output_dir, filename)
                                            if not os.path.exists(dst) or os.path.getmtime(src) > os.path.getmtime(dst):
                                                shutil.copy2(src, dst)
                                                print(f"Copied backtest result: {src} → {dst}")
                                                found_any_files = True
                    else:
                        # Process files directly in this directory
                        for filename in os.listdir(search_dir):
                            src = os.path.join(search_dir, filename)
                            # Check if this is a PNG file created after we started the backtest
                            if (os.path.isfile(src) and
                                    filename.endswith('.png') and
                                    os.path.getmtime(src) >= before_time):

                                # FIX: Only copy files that match the current model name
                                # This prevents copying files from other strategies/models
                                if model_name in filename:
                                    # Copy the file to our output directory
                                    dst = os.path.join(output_dir, filename)
                                    if not os.path.exists(dst) or os.path.getmtime(src) > os.path.getmtime(dst):
                                        shutil.copy2(src, dst)
                                        print(f"Copied backtest result: {src} → {dst}")
                                        found_any_files = True

            # If we didn't find any files, search the entire backtest_result directory for PNG files
            if not found_any_files:
                print("No backtest result files found in expected locations. Searching all directories...")

                for root, dirs, files in os.walk("backtest_result"):
                    # Skip our output directory to avoid copying between strategy subdirectories
                    if root == output_dir:
                        continue

                    for filename in files:
                        if filename.endswith('.png'):
                            src = os.path.join(root, filename)
                            # Check if the file was created after we started the backtest
                            if os.path.getmtime(src) >= before_time:
                                # FIX: Only copy files that match the current model name
                                # This prevents copying files from other strategies/models
                                if model_name in filename:
                                    dst = os.path.join(output_dir, filename)
                                    if not os.path.exists(dst) or os.path.getmtime(src) > os.path.getmtime(dst):
                                        shutil.copy2(src, dst)
                                        print(f"Copied backtest result: {src} → {dst}")
                                        found_any_files = True

            if not found_any_files:
                print("Warning: No backtest result files found to copy. Check if the backtest generated any output.")

    finally:
        # Restore original plot_backtest_results if we modified it
        if mode == 'backtest' and 'original_plot_backtest' in locals():
            backtesting.plot_backtest_results = original_plot_backtest

        # Restore all monkey patches
        restore_monkey_patches()

        # Restore original SAVE_FIGURES setting
        if 'original_save_figures' in locals():
            backtesting.SAVE_FIGURES = original_save_figures

        # Restore ensure_output_directory if we patched it
        if hasattr(backtesting, 'ensure_output_directory') and 'original_ensure_output' in locals():
            backtesting.ensure_output_directory = original_ensure_output

        # Identify directories that should be cleaned up later
        try:
            # Add the factor directory to the cleanup list if it exists
            factor_name = extract_factor_name_from_api_url(backtesting.api_url)
            original_factor_name = factor_name.replace(' ', '_')

            # Add the API URL factor directory to cleanup list
            factor_dir = os.path.join("backtest_result", original_factor_name)
            if os.path.exists(factor_dir) and os.path.basename(factor_dir) != strategy_name:
                dirs_to_cleanup.append(factor_dir)

            # Add any variation of the factor name (with different capitalizations)
            variations = [
                original_factor_name,
                original_factor_name.lower(),
                original_factor_name.upper(),
                original_factor_name.capitalize(),
                original_factor_name.replace('_', '')
            ]

            for var in variations:
                var_dir = os.path.join("backtest_result", var)
                if os.path.exists(var_dir) and var_dir not in dirs_to_cleanup and os.path.basename(
                        var_dir) != strategy_name:
                    dirs_to_cleanup.append(var_dir)

            # Add the model directory to the cleanup list if it exists
            model_name = config['model']
            model_dir = os.path.join("backtest_result", model_name.replace(' ', '_'))
            if os.path.exists(model_dir) and os.path.basename(model_dir) != strategy_name:
                dirs_to_cleanup.append(model_dir)

            # Also check for the model directory with spaces
            model_dir_with_spaces = os.path.join("backtest_result", model_name)
            if os.path.exists(model_dir_with_spaces) and os.path.basename(model_dir_with_spaces) != strategy_name:
                dirs_to_cleanup.append(model_dir_with_spaces)

            # Check for combined strategy_factor directories
            combined_dir = os.path.join("backtest_result", f"{strategy_name}_{original_factor_name}")
            if os.path.exists(combined_dir) and os.path.basename(combined_dir) != strategy_name:
                dirs_to_cleanup.append(combined_dir)

            # List all directories in backtest_result
            base_dir = "backtest_result"
            if os.path.exists(base_dir):
                for dir_name in os.listdir(base_dir):
                    dir_path = os.path.join(base_dir, dir_name)

                    # Skip the strategy-specific directories
                    if dir_name == strategy_name:
                        continue

                    # Include directories that match additional patterns:
                    # 1. Any directory with underscores (likely a factor name)
                    # 2. Any directory starting with known metric prefixes
                    # 3. Any directory that matches the model name or factor name pattern
                    if os.path.isdir(dir_path) and (
                            "_" in dir_name or
                            dir_name.startswith(("Mvrv", "Options", "Derivatives", "Metrics")) or
                            dir_name.startswith(strategy_name + "_") or
                            dir_name.startswith(model_name) or
                            dir_name == original_factor_name or
                            dir_name == model_name.replace(' ', '_')
                    ):
                        dirs_to_cleanup.append(dir_path)

        except Exception as e:
            print(f"Warning: Failed to identify temporary directories: {e}")

    return dirs_to_cleanup


def find_closest_model(model_name: str, available_models: List[str]) -> str:
    """Find the closest matching model name from available models"""
    import difflib
    return difflib.get_close_matches(model_name, available_models, n=1, cutoff=0.1)[0]


def display_available_strategies(csv_file: str) -> None:
    """
    Display all available strategies in the CSV file.
    Always regenerate CSV from the corresponding Excel file when available.

    Args:
        csv_file: Path to the CSV configuration file
    """
    # Always try to generate from Excel first
    excel_file = csv_file.replace('.csv', '.xlsx')
    if os.path.exists(excel_file):
        print(f"Regenerating {csv_file} from {excel_file}")
        generate_csv_from_excel(excel_file, csv_file)

    # Check if CSV exists (either pre-existing or just generated)
    if not os.path.exists(csv_file):
        raise FileNotFoundError(f"Neither CSV ({csv_file}) nor Excel ({excel_file}) files found")

    try:
        # Read the CSV file
        df = pd.read_csv(csv_file)
        strategies = df['Strategy Name'].unique().tolist()

        print("\n===== CONFIGURATION FILE INFORMATION =====")
        print(f"File: {csv_file}")
        print(f"Found {len(strategies)} strategies:")

        for i, strategy in enumerate(strategies, 1):
            strategy_df = df[df['Strategy Name'] == strategy]
            model = strategy_df['Model'].iloc[0]
            style = strategy_df['Style'].iloc[0]
            strategy_type = strategy_df['Type'].iloc[0]
            print(f"  {i}. {strategy} - Model: {model}, Style: {style}, Type: {strategy_type}")

        print("=" * 40)
        return strategies

    except Exception as e:
        print(f"Error reading configuration file: {e}")
        return []


def extract_best_parameters(strategy_dir: str, strategy_type: str, style: str) -> Tuple[Optional[int], Optional[float]]:
    """
    Extract the best parameters (window, threshold) from optimization results.

    Args:
        strategy_dir: Path to the strategy directory
        strategy_type: Strategy type (long_only, short_only, long_short)
        style: Strategy style (momentum, reversion)

    Returns:
        Tuple of (window, threshold) or (None, None) if not found
    """
    # Convert style and type to short forms used in filenames
    style_short = 'M' if style.lower() == 'momentum' else 'R'

    if strategy_type.lower() == 'long_only':
        type_short = 'L'
    elif strategy_type.lower() == 'short_only':
        type_short = 'S'
    else:
        type_short = 'LS'

    # Look for heatmap files with good factors
    best_window, best_threshold = None, None

    # Try to find CSV files with optimization results
    for filename in os.listdir(strategy_dir):
        if filename.endswith('.csv') and 'good_factors' in filename:
            try:
                # Load the CSV file
                csv_path = os.path.join(strategy_dir, filename)
                results_df = pd.read_csv(csv_path)

                # Check if this contains results for our specific style and type
                style_type_match = False
                for _, row in results_df.iterrows():
                    if 'Strategy' in row:
                        strategy_desc = str(row['Strategy'])
                        if (style.lower() in strategy_desc.lower() and
                                strategy_type.lower().replace('_', ' ') in strategy_desc.lower()):
                            style_type_match = True
                            break

                if not style_type_match:
                    continue

                # If we found a match, sort by Sharpe ratio and take the best one
                if 'sr' in results_df.columns and 'Window' in results_df.columns and 'Threshold' in results_df.columns:
                    best_row = results_df.sort_values('sr', ascending=False).iloc[0]
                    best_window = int(best_row['Window'])
                    best_threshold = float(best_row['Threshold'])
                    print(f"Found best parameters from {filename}: Window={best_window}, Threshold={best_threshold}")
                    return best_window, best_threshold
            except Exception as e:
                print(f"Error reading {filename}: {e}")
                continue

    # If no CSV files found, try to extract from image filenames
    for filename in os.listdir(strategy_dir):
        # Look for heatmap images with the right style and type
        if (filename.endswith('.png') and
                f"_{style_short}_{type_short}_" in filename and
                'good_factors' in filename):
            try:
                # Try to extract window and threshold from filename
                # Format might be: heatmap_BTC_24h_above_100k_M_L_1_good_factors_timestamp.png
                parts = filename.split('_')
                for i, part in enumerate(parts):
                    if part == style_short and i < len(parts) - 1 and parts[i + 1] == type_short:
                        # Found style and type markers
                        if i + 2 < len(parts) and parts[i + 2].isdigit():
                            # This might be the window
                            best_window = int(parts[i + 2])
                            # And if there's another number after this, it might be the threshold
                            if i + 3 < len(parts) and parts[i + 3].replace('.', '', 1).isdigit():
                                best_threshold = float(parts[i + 3])
                                print(
                                    f"Extracted parameters from filename {filename}: Window={best_window}, Threshold={best_threshold}")
                                return best_window, best_threshold
            except Exception as e:
                print(f"Error extracting parameters from {filename}: {e}")
                continue

    # If we can't find anything, return None, None
    return None, None


def run_strategy_with_mode(strategy_name: str, config_file: str, mode: str, window: Optional[int] = None,
                           threshold: Optional[float] = None, train_ratio: float = 0.7,
                           run_id: Optional[str] = None) -> Tuple[List[str], bool]:
    """
    Run a single strategy with a specific mode.
    This function is designed to be used as a target for multiprocessing.

    Args:
        strategy_name: Name of the strategy to run
        config_file: Path to the CSV configuration file
        mode: Mode to run ('optimize', 'walkforward', or 'backtest')
        window: Window size for backtest mode
        threshold: Threshold for backtest mode
        train_ratio: Ratio of training data
        run_id: Unique identifier for this run to avoid directory conflicts

    Returns:
        Tuple of (temporary directory paths, success flag)
    """
    success = False
    try:
        # Create a unique run ID for this process if not provided
        if run_id is None:
            run_id = f"{mode}_{uuid.uuid4().hex[:8]}"

        # Load configuration for this strategy
        try:
            # Use os.path.abspath to resolve the path before loading the config
            config_file_path = os.path.abspath(config_file)
            config = load_config_from_csv(config_file_path, strategy_name)
        except FileNotFoundError as e:
            print(f"\n===== CONFIGURATION ERROR =====")
            print(f"Could not find configuration file: {str(e)}")
            print("\nWhat to do next:")
            print("- Check the file path and make sure the CSV or Excel file exists")
            print("- Ensure the file has the correct format with required columns")
            return [], success
        except ValueError as e:
            print(f"\n===== STRATEGY ERROR =====")
            print(f"Error with strategy configuration: {str(e)}")
            print("\nWhat to do next:")
            print("- Verify the strategy name exists in the configuration file")
            print("- Check that all required parameters are defined for this strategy")
            return [], success
        except Exception as e:
            print(f"\n===== CONFIGURATION LOADING ERROR =====")
            print(f"Failed to load configuration: {str(e)}")
            print("\nWhat to do next:")
            print("- Check the format of your configuration file")
            print("- Ensure all required columns are present")
            import traceback
            traceback.print_exc()
            return [], success

        # Build arguments for run_backtest
        kwargs = {
            'config': config,
            'mode': mode,
            'train_ratio': train_ratio,
            'run_id': run_id
        }

        # Add window and threshold if provided
        if window is not None:
            kwargs['window'] = window
        if threshold is not None:
            kwargs['threshold'] = threshold

        # For backtest mode, we need to ensure we have window/threshold parameters
        if mode == 'backtest':
            # First check if parameters are in config
            if 'window' in config and 'window' not in kwargs:
                kwargs['window'] = config['window']
            if 'threshold' in config and 'threshold' not in kwargs:
                kwargs['threshold'] = config['threshold']

            # If still missing parameters, try to extract from optimization results
            if 'window' not in kwargs or 'threshold' not in kwargs:
                strategy_dir = f"backtest_result/{strategy_name}"
                if os.path.exists(strategy_dir):
                    best_window, best_threshold = extract_best_parameters(
                        strategy_dir, config['strategy_type'], config['style']
                    )

                    if best_window is not None and 'window' not in kwargs:
                        kwargs['window'] = best_window
                        print(f"Using best window={best_window} from optimization results for {strategy_name}")

                    if best_threshold is not None and 'threshold' not in kwargs:
                        kwargs['threshold'] = best_threshold
                        print(f"Using best threshold={best_threshold} from optimization results for {strategy_name}")

            # If we still don't have window/threshold, use defaults
            if 'window' not in kwargs:
                # Use a reasonable default window based on resolution
                resolution = config.get('resolution', '24h')
                if resolution == '1h':
                    kwargs['window'] = 24  # 1 day for hourly data
                elif resolution == '10m':
                    kwargs['window'] = 144  # 1 day for 10-min data
                else:
                    kwargs['window'] = 60  # Default for daily data
                print(
                    f"Warning: No window parameter found. Using default window={kwargs['window']} for {strategy_name}")

            if 'threshold' not in kwargs:
                kwargs['threshold'] = 1.0  # Default threshold
                print(
                    f"Warning: No threshold parameter found. Using default threshold={kwargs['threshold']} for {strategy_name}")

        # Run the backtest
        print(f"Starting {mode} mode for strategy {strategy_name} (Process ID: {os.getpid()}, Run ID: {run_id})")
        dirs_to_cleanup = run_backtest(**kwargs)
        print(f"Completed {mode} mode for strategy {strategy_name}")
        success = True
        return dirs_to_cleanup, success

    except requests.exceptions.JSONDecodeError as e:
        print(f"\n===== API RESPONSE ERROR for {strategy_name} =====")
        print(f"Failed to decode JSON from API response: {str(e)}")
        print("\nWhat to do next:")
        print("- Check your API key in the backtesting.py file")
        print("- Verify your internet connection")
        print("- Wait a few minutes and try again")
        print("- Check if you've exceeded API rate limits")
        import traceback
        traceback.print_exc()
        return [], success

    except requests.exceptions.RequestException as e:
        print(f"\n===== API CONNECTION ERROR for {strategy_name} =====")
        print(f"Failed to connect to API: {str(e)}")
        print("\nWhat to do next:")
        print("- Check your internet connection")
        print("- Verify the API endpoints are correct")
        print("- Wait a few minutes and try again")
        import traceback
        traceback.print_exc()
        return [], success

    except ImportError as e:
        print(f"\n===== MODULE ERROR for {strategy_name} =====")
        print(f"Failed to import required module: {str(e)}")
        print("\nWhat to do next:")
        print("- Ensure all dependencies are installed")
        print("- Check that backtesting.py exists in the correct location")
        import traceback
        traceback.print_exc()
        return [], success

    except Exception as e:
        print(f"\n===== ERROR running {mode} mode for strategy {strategy_name} =====")
        print(f"Error: {str(e)}")
        print("\nWhat to do next:")
        print("- Check the error message and traceback for specific issues")
        print("- Verify your configuration settings")
        print("- Try running again with a different strategy or mode")
        import traceback
        traceback.print_exc()
        return [], success


def cleanup_temp_directories(dirs_to_cleanup: List[str], config_file: str = 'fund/fund_bybit.csv') -> None:
    """
    Instead of cleaning up temporary directories, create a timestamped backup of results.
    Only creates an archive if there are valid results to archive.
    Always regenerate CSV from the corresponding Excel file when available.

    Args:
        dirs_to_cleanup: List of directory paths (not used, kept for compatibility)
        config_file: Path to the CSV configuration file (to identify valid strategy names)
    """
    # Get list of valid strategy names from CSV file
    valid_strategies = []
    try:
        # Resolve the path to the CSV file using absolute paths
        config_file_path = os.path.abspath(config_file)

        # Always try to generate from Excel first
        excel_path = config_file_path.replace('.csv', '.xlsx')
        if os.path.exists(excel_path):
            print(f"Regenerating {config_file_path} from {excel_path}")
            generate_csv_from_excel(excel_path, config_file_path)

        # Check if CSV exists (either pre-existing or just generated)
        if not os.path.exists(config_file_path):
            print(
                f"Warning: Neither CSV ({config_file_path}) nor Excel ({excel_path}) found. Cannot determine valid strategy names.")
            valid_strategies = []
        else:
            # CSV exists, read it directly
            df = pd.read_csv(config_file_path)
            valid_strategies = df['Strategy Name'].unique().tolist()
            print(f"Valid strategy names from CSV: {', '.join(valid_strategies)}")
    except Exception as e:
        print(f"Warning: Failed to read strategy names from CSV or Excel: {e}")

    # Check if backtest_result directory exists - ensure it's relative to current working directory
    base_dir = os.path.join(os.getcwd(), "backtest_result")
    if not os.path.exists(base_dir):
        print("No backtest results to archive.")
        return

    # First check if we have any valid strategy folders to archive
    strategies_to_archive = []
    for strategy_name in valid_strategies:
        source_dir = os.path.join(base_dir, strategy_name)
        if os.path.exists(source_dir) and os.path.isdir(source_dir):
            strategies_to_archive.append(strategy_name)

    if not strategies_to_archive:
        print("\nNo strategy folders found to archive.")
        return

    # Create a timestamped directory only if we have strategies to archive
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    archive_dir = os.path.join(os.getcwd(), f"backtest_result_{timestamp}")

    # Create the archive directory
    os.makedirs(archive_dir, exist_ok=True)
    print(f"\nCreating archive directory: {archive_dir}")

    # Copy strategy folders to the archive
    copied_count = 0
    for strategy_name in strategies_to_archive:
        source_dir = os.path.join(base_dir, strategy_name)
        target_dir = os.path.join(archive_dir, strategy_name)
        try:
            print(f"Archiving: {source_dir} → {target_dir}")
            shutil.copytree(source_dir, target_dir)
            copied_count += 1
        except Exception as e:
            print(f"Failed to archive {source_dir}: {e}")

    if copied_count > 0:
        print(f"\nArchiving completed. {copied_count} strategy folders archived to: {archive_dir}")
    else:
        # This should not happen, but just in case
        print("\nNo strategy folders were successfully archived.")
        try:
            os.rmdir(archive_dir)
            print(f"Removed empty archive directory: {archive_dir}")
        except:
            pass


def run_all_strategies(config_file: str, modes: List[str] = None, parallel: bool = False,
                       max_workers: int = None, delay_between_tasks: float = 1.0) -> Dict[str, Dict[str, bool]]:
    """
    Run all strategies in the config file with specified modes.

    Args:
        config_file: Path to the CSV configuration file
        modes: List of modes to run (default: all three modes)
        parallel: Whether to run in parallel using multiprocessing
        max_workers: Maximum number of worker processes to use (default: CPU count)
        delay_between_tasks: Delay in seconds between starting tasks (to avoid API rate limits)

    Returns:
        Dictionary with results of each strategy and mode
    """
    # Default to all modes if none specified
    if modes is None:
        modes = ['optimize', 'walkforward', 'backtest']

    # Validate modes
    valid_modes = ['optimize', 'walkforward', 'backtest']
    for mode in modes:
        if mode not in valid_modes:
            raise ValueError(f"Invalid mode: {mode}. Must be one of {valid_modes}")

    # Resolve the path to the CSV file
    script_dir = os.path.dirname(os.path.abspath(__file__))
    csv_file = os.path.join(script_dir, config_file)

    # Check if CSV exists, generate from Excel if needed
    if not os.path.exists(csv_file):
        excel_file = csv_file.replace('.csv', '.xlsx')
        if not generate_csv_from_excel(excel_file, csv_file):
            raise FileNotFoundError(f"Neither CSV ({csv_file}) nor Excel ({excel_file}) files found")

    # Load all strategies from the CSV file
    df = pd.read_csv(csv_file)
    strategies = df['Strategy Name'].unique().tolist()

    if not strategies:
        print("No strategies found in the config file.")
        return {}

    print(f"Found {len(strategies)} strategies: {', '.join(strategies)}")
    print(f"Will run the following modes: {', '.join(modes)}")

    # Generate all the tasks to run (strategy + mode combinations)
    tasks = []
    for strategy_name in strategies:
        for mode in modes:
            # Generate a unique run ID for each strategy and mode combination
            run_id = f"{strategy_name}_{mode}_{uuid.uuid4().hex[:6]}"
            tasks.append((strategy_name, csv_file, mode, run_id))

    print(f"Total tasks to run: {len(tasks)}")

    # Dictionary to track results
    results = {strategy: {mode: False for mode in modes} for strategy in strategies}

    # List to collect all temporary directories that need to be cleaned up
    all_dirs_to_cleanup = []

    if parallel and len(tasks) > 1:
        # Determine number of workers
        if max_workers is None:
            # Limit the maximum workers to avoid overwhelming the API
            default_workers = min(multiprocessing.cpu_count(), 3)
            max_workers = default_workers
        max_workers = min(max_workers, len(tasks))

        print(f"Running in parallel with {max_workers} workers")
        print(f"Using {delay_between_tasks}s delay between task submissions to avoid API rate limits")

        # Run tasks in parallel with controlled submission rate
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            futures = []
            for i, (strategy_name, csv_file, mode, run_id) in enumerate(tasks):
                # Submit the task
                future = executor.submit(run_strategy_with_mode, strategy_name, csv_file, mode, run_id=run_id)
                futures.append((future, strategy_name, mode))

                # Add a delay between submissions to avoid overwhelming the API
                if i < len(tasks) - 1:  # No need to delay after the last task
                    # Add a small random jitter to avoid synchronized requests
                    jitter = random.uniform(0, 0.5)
                    delay = delay_between_tasks + jitter
                    print(f"Waiting {delay:.1f}s before submitting next task...")
                    time.sleep(delay)

            # Wait for all tasks to complete and collect dirs to cleanup
            for future, strategy_name, mode in futures:
                try:
                    dirs_to_cleanup, success = future.result()
                    all_dirs_to_cleanup.extend(dirs_to_cleanup)
                    results[strategy_name][mode] = success
                except Exception as e:
                    print(f"Task for {strategy_name} with {mode} mode failed: {str(e)}")
                    results[strategy_name][mode] = False
    else:
        print("Running sequentially")

        # Run tasks sequentially
        for i, (strategy_name, csv_file, mode, run_id) in enumerate(tasks):
            print(f"Running task {i + 1}/{len(tasks)}: {strategy_name} with {mode} mode")
            dirs_to_cleanup, success = run_strategy_with_mode(strategy_name, csv_file, mode, run_id=run_id)
            all_dirs_to_cleanup.extend(dirs_to_cleanup)
            results[strategy_name][mode] = success

            # Add a delay between tasks to avoid API rate limits
            if i < len(tasks) - 1:  # No need to delay after the last task
                # Add a small random jitter to avoid synchronized requests
                jitter = random.uniform(0, 0.5)
                delay = delay_between_tasks + jitter
                print(f"Waiting {delay:.1f}s before starting next task...")
                time.sleep(delay)

    # After all tasks are completed, archive strategy results
    cleanup_temp_directories(all_dirs_to_cleanup, config_file=config_file)

    return results


def display_results_summary(results: Dict[str, Dict[str, bool]]) -> None:
    """
    Display a summary of successful and failed strategies.

    Args:
        results: Dictionary with results of each strategy and mode
    """
    if not results:
        print("No results to display.")
        return

    print("\n===== RESULTS SUMMARY =====")
    print(f"{'Strategy':<15} | {'Optimize':<10} | {'Walkforward':<12} | {'Backtest':<10}")
    print("-" * 55)

    # Count successful and failed runs
    total_success = 0
    total_runs = 0

    for strategy, modes in results.items():
        optimize = "✓ Success" if modes.get('optimize', False) else "✗ Failed" if 'optimize' in modes else "- Skipped"
        walkforward = "✓ Success" if modes.get('walkforward',
                                               False) else "✗ Failed" if 'walkforward' in modes else "- Skipped"
        backtest = "✓ Success" if modes.get('backtest', False) else "✗ Failed" if 'backtest' in modes else "- Skipped"

        print(f"{strategy:<15} | {optimize:<10} | {walkforward:<12} | {backtest:<10}")

        # Update counters
        for mode, success in modes.items():
            total_runs += 1
            if success:
                total_success += 1

    print("-" * 55)
    success_rate = (total_success / total_runs * 100) if total_runs > 0 else 0
    print(f"Overall: {total_success}/{total_runs} tasks completed successfully ({success_rate:.1f}%)")
    print("=" * 55)


def display_menu() -> str:
    """
    Display a user-friendly menu and get the user's selection.

    Returns:
        Selected mode as a string
    """
    print("\n===== AUTOTEST MENU =====")
    print("1. Optimize (Find best parameters)")
    print("2. Walkforward (Test parameter stability)")
    print("3. Backtest (Run with specific parameters)")
    print("4. Full (Run all three modes for one strategy)")
    print("5. All (Run all modes for all strategies)")
    print("6. List Strategies")
    print("0. Exit")

    choice = input("\nEnter your choice (0-6): ")

    mode_map = {
        "1": "optimize",
        "2": "walkforward",
        "3": "backtest",
        "4": "full",
        "5": "all",
        "6": "list",
        "0": "exit"
    }

    return mode_map.get(choice, "invalid")


def display_file_info(csv_file: str) -> List[str]:
    """
    Display information about the file being processed and the available strategies.
    Always regenerate CSV from the corresponding Excel file when available.

    Args:
        csv_file: Path to the CSV configuration file

    Returns:
        List of strategy names in the CSV file
    """
    strategies = []

    # Always try to generate from Excel first
    excel_file = csv_file.replace('.csv', '.xlsx')
    if os.path.exists(excel_file):
        print(f"Regenerating {csv_file} from {excel_file}")
        generate_csv_from_excel(excel_file, csv_file)

    # Check if CSV exists (either pre-existing or just generated)
    if not os.path.exists(csv_file):
        print(f"Neither CSV ({csv_file}) nor Excel ({excel_file}) files found")
        return []

    try:
        # Read the CSV file
        df = pd.read_csv(csv_file)
        strategies = df['Strategy Name'].unique().tolist()

        print("\n===== CONFIGURATION FILE INFORMATION =====")
        print(f"File: {csv_file}")
        print(f"Found {len(strategies)} strategies:")

        for i, strategy in enumerate(strategies, 1):
            strategy_df = df[df['Strategy Name'] == strategy]
            model = strategy_df['Model'].iloc[0]
            style = strategy_df['Style'].iloc[0]
            strategy_type = strategy_df['Type'].iloc[0]
            print(f"  {i}. {strategy} - Model: {model}, Style: {style}, Type: {strategy_type}")

        print("=" * 40)
        return strategies

    except Exception as e:
        print(f"Error reading configuration file: {e}")
        return []


def check_existing_results() -> None:
    """
    Check if backtest_result directory exists and ask user if they want to archive it.
    This helps avoid confusion with old results and ensures a clean testing environment.
    """
    if os.path.exists("backtest_result"):
        print("\n===== WARNING =====")
        print("Existing backtest results found in 'backtest_result' directory.")
        print("Keeping these may cause confusion when analyzing new results.")
        choice = input("Would you like to archive existing results before proceeding? (y/n): ")

        if choice.lower() in ("y", "yes"):
            try:
                # Create a timestamped archive
                timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                archive_dir = f"backtest_result_{timestamp}"

                # Copy the entire directory
                shutil.copytree("backtest_result", archive_dir)
                print(f"Archived existing results to '{archive_dir}'")

                # Remove the original
                shutil.rmtree("backtest_result")
                print("Removed original backtest_result directory.")
            except Exception as e:
                print(f"Error archiving directory: {str(e)}")
                print("Please manually archive or delete the directory if needed.")
        else:
            print("Keeping existing results. New results will be merged with existing ones.")
    # If directory doesn't exist, no action needed


def extract_factor_name_from_api_url(api_url: str) -> str:
    """
    Extract a human-readable factor name from a Glassnode API URL.
    
    Args:
        api_url: The Glassnode API URL
        
    Returns:
        Human-readable factor name
    """
    # Get the last part of the URL path
    url_path = api_url.split('/')[-1]
    
    # Create mappings for common abbreviations and patterns
    replacements = [
        # Time period mappings (order matters - longer patterns first)
        ('_3_months', ' 3 Months'),
        ('_6_months', ' 6 Months'),
        ('_1_month', ' 1 Month'),
        ('_1_year', ' 1 Year'),
        ('_3m', ' 3 Months'),
        ('_6m', ' 6 Months'),
        ('_1m', ' 1 Month'),
        ('_1y', ' 1 Year'),
        ('_1h', ' 1 Hour'),
        ('_1d', ' 1 Day'),
        ('_1w', ' 1 Week'),
        
        # Options and derivatives patterns
        ('options_', 'Options '),
        ('derivatives_', 'Derivatives '),
        ('25delta_skew', '25Delta Skew'),
        ('_25delta', ' 25Delta'),
        ('_skew', ' Skew'),
        
        # Other common patterns
        ('mvrv', 'MVRV'),
        ('_ratio', ' Ratio'),
        ('_volume', ' Volume'),
        ('_open_interest', ' Open Interest'),
        ('balance_exchanges', 'Balance Exchanges'),
        ('distribution_', 'Distribution '),
        ('market_', 'Market '),
        ('_price_usd_close', ' Price USD Close'),
    ]
    
    # Apply replacements
    result = url_path
    for pattern, replacement in replacements:
        result = result.replace(pattern, replacement)
    
    # Replace remaining underscores with spaces and title case
    result = result.replace('_', ' ').title()
    
    return result


def main():
    """Main function to run the autotest script"""
    import argparse
    import sys
    from datetime import datetime, timedelta

    # Track start time
    start_time = time.time()

    try:
        # Get the backtesting module
        backtesting = import_backtesting()
    except Exception as e:
        # The import_backtesting function now handles all exceptions internally
        # and provides detailed error messages, so we don't need to do anything here
        return

    parser = argparse.ArgumentParser(description='Automated backtest system')
    parser.add_argument('--config', type=str, default='fund/fund_bybit.csv',
                        help='Path to the configuration CSV file')
    parser.add_argument('--strategy', type=str, help='Strategy name to run (omit to run all strategies)')
    parser.add_argument('--mode', type=str, default=None,
                        choices=['optimize', 'walkforward', 'backtest', 'full', 'all'],
                        help='Mode to run (full runs all three modes for one strategy, all runs all modes for all strategies)')
    parser.add_argument('--window', type=int, help='Window size for backtest mode (overrides CSV value)')
    parser.add_argument('--threshold', type=float, help='Threshold for backtest mode (overrides CSV value)')
    parser.add_argument('--train_ratio', type=float, default=0.7,
                        help='Ratio of training data (default: 0.7)')
    parser.add_argument('--list', action='store_true',
                        help='List all available strategies in the config file')
    parser.add_argument('--since', type=str, help='Start date in YYYY-MM-DD format')
    parser.add_argument('--until', type=str, help='End date in YYYY-MM-DD format (default: today)')
    parser.add_argument('--parallel', action='store_true',
                        help='Run strategies in parallel using multiprocessing')
    parser.add_argument('--max_workers', type=int, default=None,
                        help='Maximum number of worker processes (default: number of CPU cores)')
    parser.add_argument('--delay', type=float, default=2.0,
                        help='Delay in seconds between task submissions to avoid API rate limits (default: 2.0)')
    parser.add_argument('--menu', action='store_true',
                        help='Use interactive menu interface')

    args = parser.parse_args()

    # Check if script is run without arguments (only the script name in sys.argv) or with menu flag
    if len(sys.argv) == 1 or args.menu:
        # Set to menu mode
        use_menu = True
    else:
        use_menu = False
        # Default behavior for no arguments: run all strategies with all modes in parallel
        if len(sys.argv) == 1:
            args.mode = 'all'
            args.parallel = True
            print("Running in default mode: all strategies with all modes in parallel")

    # Resolve the path to the CSV file
    # Use os.path.join with the current directory to ensure paths are correct
    if not os.path.isabs(args.config):
        csv_file = os.path.join(os.getcwd(), args.config)
    else:
        csv_file = args.config

    # Always try to generate from Excel first if it exists
    excel_file = csv_file.replace('.csv', '.xlsx')
    if os.path.exists(excel_file):
        print(f"Regenerating {csv_file} from {excel_file}")
        if not generate_csv_from_excel(excel_file, csv_file):
            print(f"Failed to generate CSV from Excel: {excel_file}")

    # If CSV still doesn't exist, try with script_dir path as fallback
    if not os.path.exists(csv_file):
        script_dir = os.path.dirname(os.path.abspath(__file__))
        alt_csv_file = os.path.join(script_dir, args.config)
        alt_excel_file = alt_csv_file.replace('.csv', '.xlsx')

        if os.path.exists(alt_excel_file):
            print(f"Trying alternative path. Regenerating {alt_csv_file} from {alt_excel_file}")
            if generate_csv_from_excel(alt_excel_file, alt_csv_file):
                csv_file = alt_csv_file  # Use this path if successful
            else:
                print(f"Failed to generate CSV from Excel: {alt_excel_file}")

        if not os.path.exists(csv_file) and os.path.exists(alt_csv_file):
            csv_file = alt_csv_file

    # Final check if CSV exists
    if not os.path.exists(csv_file):
        print(f"Error: Configuration file not found: {csv_file}")
        print(f"Please make sure the Excel file {excel_file} exists and is readable.")
        return

    # Variable to keep track of whether user selected 'all' for strategies
    strategy_choice = None

    # Ask about multiprocessing at the beginning for menu mode
    if use_menu:
        # Check for existing results before proceeding
        check_existing_results()

        # Display file information and available strategies
        strategies = display_file_info(csv_file)
        if not strategies:
            print("No strategies found. Exiting.")
            return

        # Force parallel processing with all available CPU cores
        args.parallel = True
        args.max_workers = multiprocessing.cpu_count()
        print(f"\nParallel processing enabled with {args.max_workers} CPU cores for maximum performance.")

        # Show menu and get user selection
        selected_mode = display_menu()

        if selected_mode == "exit":
            print("Exiting program.")
            return

        if selected_mode == "list":
            # Display the menu again without duplicating the file info display
            # that was already shown at the beginning
            print("\nReturning to menu...")
            return main()

        # Set mode based on user selection
        args.mode = selected_mode

        # For modes other than 'all', ask which strategy to run
        if selected_mode != "all":
            print("\nSelect a strategy to run:")
            for i, strategy in enumerate(strategies, 1):
                print(f"{i}. {strategy}")

            strategy_choice = input(f"\nEnter strategy number (1-{len(strategies)}) or 'all' for all strategies: ")

            if strategy_choice.lower() == 'all':
                # Run all strategies with the selected mode
                args.strategy = None
                # args.mode remains as selected_mode (optimize, walkforward, or backtest)
            else:
                try:
                    strategy_idx = int(strategy_choice) - 1
                    if 0 <= strategy_idx < len(strategies):
                        args.strategy = strategies[strategy_idx]
                    else:
                        print(f"Invalid selection. Using the first strategy: {strategies[0]}")
                        args.strategy = strategies[0]
                except ValueError:
                    print(f"Invalid selection. Using the first strategy: {strategies[0]}")
                    args.strategy = strategies[0]

            # For backtest mode, ask for window and threshold if not in CSV
            if selected_mode == "backtest":
                # Check if window/threshold are in CSV for this strategy
                config = load_config_from_csv(csv_file, args.strategy)

                if 'window' not in config:
                    window_input = input("\nEnter window size (or press Enter for default 60): ")
                    if window_input.strip():
                        try:
                            args.window = int(window_input)
                        except ValueError:
                            print("Invalid window value. Using default 60.")
                            args.window = 60
                    else:
                        args.window = 60

                if 'threshold' not in config:
                    threshold_input = input("\nEnter threshold value (or press Enter for default 1.0): ")
                    if threshold_input.strip():
                        try:
                            args.threshold = float(threshold_input)
                        except ValueError:
                            print("Invalid threshold value. Using default 1.0.")
                            args.threshold = 1.0
                    else:
                        args.threshold = 1.0

    # List available strategies if requested
    if args.list:
        display_available_strategies(csv_file)
        return

    # Set custom date range if provided
    if args.since:
        since_date = pd.to_datetime(args.since)
        backtesting.since = int(since_date.timestamp())

    if args.until:
        until_date = pd.to_datetime(args.until)
        backtesting.until = int(until_date.timestamp())
    else:
        backtesting.until = int(time.time())  # Default to current time

    print("\n===== STARTING BACKTESTING =====")
    print(f"Mode: {args.mode}")
    if args.strategy:
        print(f"Strategy: {args.strategy}")
    else:
        print("Strategy: All available strategies")
    print(
        f"Parallel processing: {'Enabled with {0} cores'.format(args.max_workers if args.max_workers else multiprocessing.cpu_count()) if args.parallel else 'Disabled'}")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 40 + "\n")

    # Store results to display summary
    results = {}

    # Process the specific mode selected
    if args.mode == 'all':
        # Check if we need to run all strategies or just one strategy with all modes
        if strategy_choice and strategy_choice.lower() == 'all':
            # Run a single mode (optimize, walkforward, or backtest) for all strategies
            results = run_all_strategies(
                config_file=csv_file,  # Use the resolved csv_file path
                modes=[selected_mode],  # Just the selected mode
                parallel=args.parallel,
                max_workers=args.max_workers,
                delay_between_tasks=args.delay
            )
        else:
            # Run all strategies with all modes
            if args.strategy:
                print(f"Warning: Strategy '{args.strategy}' specified with 'all' mode will be ignored.")
                print("Running all strategies with all modes...")

            # Run all three modes for all strategies
            results = run_all_strategies(
                config_file=csv_file,  # Use the resolved csv_file path
                modes=['optimize', 'walkforward', 'backtest'],
                parallel=args.parallel,
                max_workers=args.max_workers,
                delay_between_tasks=args.delay
            )
    elif args.mode == 'full':
        # Run all three modes in sequence for one strategy
        if not args.strategy:
            # If no strategy specified, use the first one from CSV
            df = pd.read_csv(csv_file)
            if df.empty:
                print("Error: Config file is empty.")
                return
            args.strategy = df.iloc[0]['Strategy Name']
            print(f"No strategy specified. Using the first one: {args.strategy}")

        print(f"\n======= RUNNING FULL MODE FOR {args.strategy} =======")

        # Track results for this strategy
        results = {args.strategy: {}}

        # Create a unique run ID for this full run
        full_run_id = f"full_{uuid.uuid4().hex[:8]}"

        # Run optimize
        print("\n======= OPTIMIZE MODE =======")
        dirs_to_cleanup = []
        optimize_dirs, success = run_strategy_with_mode(
            args.strategy,
            csv_file,  # Use the resolved csv_file path
            'optimize',
            train_ratio=args.train_ratio,
            run_id=f"{full_run_id}_optimize"
        )
        dirs_to_cleanup.extend(optimize_dirs)
        results[args.strategy]['optimize'] = success

        # Run walkforward
        print("\n======= WALKFORWARD MODE =======")
        walkforward_dirs, success = run_strategy_with_mode(
            args.strategy,
            csv_file,  # Use the resolved csv_file path
            'walkforward',
            train_ratio=args.train_ratio,
            run_id=f"{full_run_id}_walkforward"
        )
        dirs_to_cleanup.extend(walkforward_dirs)
        results[args.strategy]['walkforward'] = success

        # Run backtest
        print("\n======= BACKTEST MODE =======")
        backtest_dirs, success = run_strategy_with_mode(
            args.strategy,
            csv_file,  # Use the resolved csv_file path
            'backtest',
            window=args.window,
            threshold=args.threshold,
            train_ratio=args.train_ratio,
            run_id=f"{full_run_id}_backtest"
        )
        dirs_to_cleanup.extend(backtest_dirs)
        results[args.strategy]['backtest'] = success

        print("\n======= FULL MODE COMPLETED =======")

        # Clean up temporary directories
        cleanup_temp_directories(dirs_to_cleanup, config_file=csv_file)
    else:
        # For single mode (optimize, walkforward, or backtest)
        if not args.strategy:
            # If no strategy specified, use the first one from CSV
            df = pd.read_csv(csv_file)
            if df.empty:
                print("Error: Config file is empty.")
                return
            args.strategy = df.iloc[0]['Strategy Name']
            print(f"No strategy specified. Using the first one: {args.strategy}")

        # If user selected a specific mode through the menu,
        # run just that mode for a single strategy or all strategies
        if use_menu and strategy_choice and strategy_choice.lower() == 'all':
            # User selected to run a single mode for all strategies
            print(f"\n======= RUNNING {args.mode.upper()} MODE FOR ALL STRATEGIES =======")
            results = run_all_strategies(
                config_file=csv_file,  # Use the resolved csv_file path
                modes=[args.mode],  # Just the selected mode
                parallel=args.parallel,
                max_workers=args.max_workers,
                delay_between_tasks=args.delay
            )
        else:
            # Run single mode for a single strategy
            print(f"\n======= RUNNING {args.mode.upper()} MODE FOR {args.strategy} =======")
            # Create a unique run ID for this single run
            single_run_id = f"{args.mode}_{uuid.uuid4().hex[:8]}"

            dirs_to_cleanup, success = run_strategy_with_mode(
                args.strategy,
                csv_file,  # Use the resolved csv_file path
                args.mode,
                window=args.window,
                threshold=args.threshold,
                train_ratio=args.train_ratio,
                run_id=single_run_id
            )

            # Store results
            results = {args.strategy: {args.mode: success}}

            # Clean up temporary directories
            cleanup_temp_directories(dirs_to_cleanup, config_file=csv_file)

    # Calculate execution time
    end_time = time.time()
    execution_time = end_time - start_time
    hours, remainder = divmod(execution_time, 3600)
    minutes, seconds = divmod(remainder, 60)

    print("\n===== BACKTESTING COMPLETED =====")
    print(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Total execution time: {int(hours)}h {int(minutes)}m {int(seconds)}s")
    print("=" * 40 + "\n")

    # Display results summary
    display_results_summary(results)

    # Exit the program
    sys.exit(0)


if __name__ == "__main__":
    main()