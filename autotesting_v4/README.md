# Auto Testing Version 4.0

_QA your strategy by strategy input excel_

🎯Goal
-------------
- QA your strategy running on production, given by an input excel

🚀 Script
-------------

   ```
    https://github.com/codylai/algotrade/tree/main/autotesting_v4/
   ```

⭐ Functions
-------------
- Support bybit backtesting (api=bybit, metric key=open,high,low,close,volume,funding_rate)!
- Support all versions from hoho's backtest v6,v7,v8,v9 (better upgrade!)
- Able to cross-check the manual testing result with hoho's test script
- Add user-friendly menu
- Support cross assets backtesting (favor for <PERSON><PERSON>’s strategies!)
- Export audit data each run for better QA the data pattern
- Enhance glassnode api retry mechansim to make it more robust
- Generate Optimized Heatmap automatically
- Generate Walkforward Heatmap automatically
- Generate Equity Curve automatically
- Support all glassnode API 
- Support glassnode API error retry  
- Support multprocessing and 10x faster
- Support live trade v5 excel format

🚀 How to use?
-------------
**Step 1**: Update the excel fund/fund_bybit.xlsx. The Excel format should be same as live_trade_v5.

⚠️Make sure you input the Bybit key information.⚠️

<img src="https://i.imgur.com/OY7QhJS.png" width="600">

**⚠️Make sure you input correct information to support Bybit backtesting:⚠️**

**api:**
bybit

**metric key:** 
open,high,low,close,volume,funding_rate

**Resolution:**
1m,3m,5m,15m,30m,1h,2h,4h,6h,12h,1d

**API Symbol:**
All symbols available from Bybit. e.g. BTCUSDT

**Asset:**
All symbols available from Bybit. e.g. BTCUSDT

<img src="https://i.imgur.com/XhNLige.png" width="800">

**Step 2**: run install.py on Pycharm or terminal. Select which version you want to use.
   ```
    python install.py
   ```

<img src="https://i.imgur.com/XuFOz1z.png" width="300">

**Step 3**: run autotest.py on Pycharm or terminal. Follow the menu instructions to generate backtest results.
   ```
    python autotest.py
   ```

<img src="https://i.imgur.com/hDOGk16.png" width="600">

**Step 4**: Check all output results in "backtest_result" folder, all subfolder filed by Strategy Name.


📌 Remark
-------------
- Need to use hardcode variable "Since" when reading Glassnode API. (to be upgraded)
